{"ANALYTICS_ENABLED": "ENC[AES256_GCM,data:e7EbsA==,iv:DQLiMomLpE4qt0oam9oAQaVbJ1VSybIn2k/GQZFq9qM=,tag:WBZFAtNHZqsTm0t9KWgRNg==,type:bool]", "BOOKR_API_URL": "ENC[AES256_GCM,data:p45gnzG4ALip/GxxEVOH1lKiEq0=,iv:WuhWgJe+12iNCbzXAYSx2B//OfY94fUPe5BJPQ1UABU=,tag:9s0a4dNP1DeK6HRwQUhsNw==,type:str]", "BOOKR_API_URL_INTERNAL": "ENC[AES256_GCM,data:L/rM+gacZovJNmmmC0v2xuYK+ck=,iv:EsQX8keel5KoEBShTjHcDD8mYdcCDwpU+8FHeGvBjr4=,tag:x5lIkMAOUAmYvHbzy6P6VQ==,type:str]", "FACEBOOK_PIXEL_ENABLED": "ENC[AES256_GCM,data:C2xFNg==,iv:HMdFE96rwPxMOlArCAs1vL/r4tUO0LG4jYwtOPU/Z8U=,tag:KIOYs5O+ztt4h0wGSNmFew==,type:bool]", "FACEBOOK_PIXEL_ID": "ENC[AES256_GCM,data:iT0GVrpSCr4nJjk+dqFT,iv:gtOAVWKupeZDOt+lqibLshioIXG8jzDGlXk5K0OH27s=,tag:ZcYFxGZKxDP/Oq/h3O77nw==,type:str]", "FIREBASE_ANALYTICS_ENABLED": "ENC[AES256_GCM,data:LjmreQ==,iv:qji9M2g3VHmeTGhkqmuOLoIpQfm1orZxRqb9KtOQ5Dw=,tag:cSj0Pb1fh2Uo5L1vreA+DQ==,type:bool]", "GOOGLE_MAPS_API_KEY": "ENC[AES256_GCM,data:37fEuKXn+Pe+ydlMyjc1mVAt0EFSXM9KN9DLXgzPeerJvZfZWc4S,iv:Fia+3pQ4z476bIx8WvDHC9sqkLi8jVcNGp5PZ7o6BP0=,tag:KVBNKG72L8Ugb5XYM9hdgA==,type:str]", "GOOGLE_TAG_MANAGER_ENABLED": "ENC[AES256_GCM,data:QYJjpw==,iv:Cs5nacVfnwzbDXxzVgHqI01wcVC0UZTvd+RuGWsD/3c=,tag:alGJ1NLiAOxxX8Xhl9uqtA==,type:bool]", "HUBSPOT_ENABLED": "ENC[AES256_GCM,data:PQRKJg==,iv:HFHPhxXaqhrKapTDqWxtTkZp76k2Jmu1v8oXGay7jZs=,tag:GX5KtbL+8Kazquamg7sPTQ==,type:bool]", "MIXPANEL_ENABLED": "ENC[AES256_GCM,data:P7jiig==,iv:0nvXLbIlTh0XpZUf/fpOPnkOvsEx3czectA5+BFH/yE=,tag:hkPXb/hioq8DpbhB3l2vYQ==,type:bool]", "MIXPANEL_TOKEN": "ENC[AES256_GCM,data:DC0WV7vHt0n7uPN5Y3xdJOefis2O2/O+8X3FJr3JVPE=,iv:QAFFciDposhc7Je7hdE3QkAAmDM0V3dx7rhhVBa+x9A=,tag:dMrZ7ZlTL+P3a+xJpbE30w==,type:str]", "NEXT_API_URL": "ENC[AES256_GCM,data:E+6xd8220PTqNkuJeh4jYw==,iv:fE8Fa3wBp/Fw727wip97/h+LqS3UI+xO4eevzso1baU=,tag:Vb7wS4G6Nt0zVjsPCb6h/Q==,type:str]", "NEXT_API_URL_INTERNAL": "ENC[AES256_GCM,data:eRBTwndqFh8o0T0XhfWvPw==,iv:qelv8hEaoH65wPNG5WDz3Z0VRL6ZEh/rmIGYYf9cQIk=,tag:V2NwSV7iMRLK+mvELEAAjQ==,type:str]", "PWA_ENABLED": "ENC[AES256_GCM,data:Tok4oR0=,iv:ACVDtANkH33m7cPFL+6DKFEkmHYviJaBAGywCsbzFNw=,tag:hDgMQOAj5dZyR4fqYlYbRg==,type:bool]", "REDIS_HOST": "ENC[AES256_GCM,data:lTaZfdreEP3goVdOuv0=,iv:MlREUDVJb3BTlNEvR/ekRp9YzXzDGgOHVV46k6jB8Fw=,tag:yW5jYAq7+L3g9G1Czmuq4Q==,type:str]", "REDIS_PORT": "ENC[AES256_GCM,data:05X/4A==,iv:Kz3NtSD0Ux9RBH0mpzFA6aHEfZX8HLPBBpW8otzSgfk=,tag:e3vZ5ZuzmI4CbC+UERJeig==,type:float]", "SENTRY_DSN": "ENC[AES256_GCM,data:N/KZ8uU3z8Zx/CTVFq7+MWUW/tTF3vECBdfxiJ4092oLzd5T+JB1xgkQo6fnVcxZ18xFiz7kbBQBnWbqXu6NEIhASdRyHqAewHEEYagBjnX6iOU=,iv:NirZt9mXgUlkXqoxpupguV/GG6cMn1SSnwffaFT6YKI=,tag:9lHCMd4588rVx1y/V3Weow==,type:str]", "SENTRY_ENABLED": "ENC[AES256_GCM,data:iwLl0Q==,iv:HYyLDY+mKOgZreFSS3me1SFSw5VidXWoHs1tTAG/u5Q=,tag:YYS0tirgdwe9AR2y3wuJfA==,type:bool]", "URL_CDN": "", "URL_DASHBOARD": "", "URL_WEB": "", "sops": {"kms": null, "gcp_kms": [{"resource_id": "projects/bookr-api/locations/global/keyRings/sops/cryptoKeys/sops-key", "created_at": "2023-06-14T22:21:12Z", "enc": "CiQA8ZMOg9cHZmI4iPABI6DcqJMD1NwY1xeddtVrlFJ6a9HTWyQSSQB5qCxEgMnmJpjTCpqGKMDanHTRAoTW0kp5ruC6oGCVkbumDbS2FkQhKyWm+eq47wNDfvDPUtpiJD0wAUmkNQ4An0NrbWbqRtg="}], "azure_kv": null, "hc_vault": null, "age": null, "lastmodified": "2025-01-25T21:01:36Z", "mac": "ENC[AES256_GCM,data:MAjztMLw+xTRxLPsRwLQ5liuRuIkTWf0/s5h9/FimQfooylZo+vdjk6UDNC/E2PFiAraWT9PLcXXNwo+b9fbFDvPD1v4PmOSzUd+B7uYMtk4CrNJEnW4yiMnDDOO62lsTtNMEeA8meGfrWGd6QfwfpE52WmaKrYrfBl3m+ehQ1c=,iv:si01qKhtIvVfPo0I2jmQa4xShvvuRffuhPWYpey6KH4=,tag:szBIrSA1HX8SGSF8HOAX7Q==,type:str]", "pgp": null, "unencrypted_suffix": "_unencrypted", "version": "3.9.0"}}