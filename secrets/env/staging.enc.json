{"ANALYTICS_ENABLED": "ENC[AES256_GCM,data:0CD/1DE=,iv:gD8ki4xvxAu3mvGLISyp/8rtCx0V2bA3fUcrhBkieQc=,tag:xSqceEU1tVOLukCBTdA9lQ==,type:bool]", "BOOKR_API_URL": "ENC[AES256_GCM,data:0qbv8zvGltaNb12HekQDoONSvZYY/ZfDWK/VdA==,iv:8uWl2EH1davRdsk8Qc/YBJvNTvrXdBBugIuNo2O3Ou4=,tag:Sjm3sCehPgyP0U9wCQLcwQ==,type:str]", "BOOKR_API_URL_INTERNAL": "ENC[AES256_GCM,data:YeiwuXQsK8kQdqK1ZYKt+uZJhQGsVJoxBkrWNQ==,iv:FwuOwHpQgssnpLA4jqYE2xa6cq2SK2oKmXWfHb3NsJI=,tag:WYrcvAjFMC7++Qt7z0xMGg==,type:str]", "FACEBOOK_PIXEL_ENABLED": "ENC[AES256_GCM,data:GIKJDyo=,iv:VqDVBeDV3BEDn7EOouBfFDJxOoIbyCDCEPHBHa66t3w=,tag:vGPHOYFiz0Y1CELXZmJb5Q==,type:bool]", "FACEBOOK_PIXEL_ID": "", "FIREBASE_ANALYTICS_ENABLED": "ENC[AES256_GCM,data:GZjbyPw=,iv:tryVqllDa4cqZOUUoW3Z+E1tu9I3Rnf/GjGGYbPuc88=,tag:huXQC0csSUJQJ10KNDAqhA==,type:bool]", "GOOGLE_MAPS_API_KEY": "ENC[AES256_GCM,data:4LbTcilrLHk5+F+Yc3XnzPKgz2Iy8A7W5S2b7YmTG0O7z/FI3A9B,iv:wQNg0qnByBQld7DgPvVYlq2Y9YroNkv7RkzBmfI2INA=,tag:s6ABDzIEIrPsOW1vh4LHBg==,type:str]", "GOOGLE_TAG_MANAGER_ENABLED": "ENC[AES256_GCM,data:uz10OZY=,iv:Ep84w1GaKJ59TVrh5rahQFwU52/Bx+LJZCccpZntKZo=,tag:GvP+hEKupKoKWc31AlYjsg==,type:bool]", "HUBSPOT_ENABLED": "ENC[AES256_GCM,data:McaOkqg=,iv:79p8pTDGKSUTL166bg2rZlxZAUcOxvEX4/vtSvOaPII=,tag:UH/sc8ulLYTj/lfwFt3nBg==,type:bool]", "MIXPANEL_ENABLED": "ENC[AES256_GCM,data:BClHpYA=,iv:NKxJqHyeLw5BVQ2IX6IG0GPiptYTac3tTmqP0ERTuhI=,tag:vWz+MvsouL8VERHQH+GSAg==,type:bool]", "MIXPANEL_TOKEN": "", "NEXT_API_URL": "ENC[AES256_GCM,data:LO+4GHy7FF5JHg9Lt92EDGp4SBW2ZkW3,iv:OX00M934euBGn+S2H3uejry1M0KQQxA4s4fI0+b6Fm8=,tag:HBET8Z83eurOH60pIweoMA==,type:str]", "NEXT_API_URL_INTERNAL": "ENC[AES256_GCM,data:3F391izPJqEWPRwKnXrzRA==,iv:5jlQ1TdygXv1e2TKwY+7VWxMXDIDgfB+bfWBkYQzV7E=,tag:oVYaBcHFtyN/uyxAxbewnQ==,type:str]", "SENTRY_DSN": "", "SENTRY_ENABLED": "ENC[AES256_GCM,data:hdPtZeM=,iv:oj+omo8QRdHr9T81zbKbhRQATBfASFR8EmQQv13R5lQ=,tag:QtBcjFGTFSTQw5q+6f0l7w==,type:bool]", "PWA_ENABLED": "ENC[AES256_GCM,data:yJ3YlMc=,iv:hEPziG4vs9u4nZ50Bw+2/MH7xajT9L9S5ZC8sUiQ55c=,tag:ETZ2psWavdWWc4DtUsgrmw==,type:bool]", "REDIS_HOST": "ENC[AES256_GCM,data:c/HyOfHR3DNb,iv:OLP7seI6z5mdHqo4D2R4imcE8fTnya1HrDDTU8YsltM=,tag:X22z6uQmCTIVsFuxw2x9nw==,type:str]", "URL_CDN": "", "URL_DASHBOARD": "", "URL_WEB": "", "sops": {"kms": null, "gcp_kms": [{"resource_id": "projects/bookr-api/locations/global/keyRings/sops/cryptoKeys/sops-key", "created_at": "2023-06-14T22:20:10Z", "enc": "CiQA8ZMOgz6RFotM1Wf77ZJJwp0akvOcJVZvUzJfSdoWX/YP2OQSSQB5qCxE0SYI/V3NFFyEInGPnoeYh906HFs90AVodR5U23GzTpKxOEjUEEGaBgtNSq1YMSr9xSo4zTY9UjhlfZg3yClpU50TVck="}], "azure_kv": null, "hc_vault": null, "age": null, "lastmodified": "2024-12-27T12:06:09Z", "mac": "ENC[AES256_GCM,data:sAqvW55IS0p/YCY4UNI4awzO2ZuPUcVuNRYGS8VRdLxTpvNREzxR2CSLVr0iEHQVL8T/t+KzukpxESB8De11qvtOMWrpQ7akAU9LBk2p4rFbmvk9fQfNrX8VsQZyngiMK1bcAJ+I5HPFvFQJx0dZj8031WycvOsZPVghvjQlzp8=,iv:b7gNAPZ4JPz5iIwzLtS6C0rp36XNRnZXhk4QrMfmjCg=,tag:r13i11JJ804njzm7KIfaHQ==,type:str]", "pgp": null, "unencrypted_suffix": "_unencrypted", "version": "3.9.0"}}