name: Deploy
run-name: Deploy to ${{ inputs.environment || 'staging' }}

permissions:
  id-token: write
  contents: read

on:
  push:
    branches:
      - main
  workflow_dispatch:
    inputs:
      version:
        description: 'Custom version'
        type: string
      environment:
        description: 'Environment'
        type: choice
        default: 'staging'
        options:
          - 'production'
          - 'staging'

concurrency:
  cancel-in-progress: true
  group: |
    ${{ github.workflow }}-${{ github.ref }}-${{ inputs.environment || 'staging' }}

env:
  AWS_REGION: eu-central-1

jobs:
  version:
    runs-on: ubuntu-latest
    outputs:
      version: ${{ steps.version.outputs.version }}
      env: ${{ steps.env.outputs.env }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: env
        id: env
        env:
          ENVIRONMENT: ${{ inputs.environment || 'staging' }}
        run: |
          echo "env: ${ENVIRONMENT}"
          if [[ -z "${ENVIRONMENT}" ]]; then
            ENVIRONMENT = "staging"
            echo "ref: ${BRANCH}"
            if [[ "${BRANCH}" == "main" ]]; then
              ENVIRONMENT = "production"
            fi
          fi

          echo "env=${ENVIRONMENT}" >> "$GITHUB_OUTPUT"

      - name: version
        id: version
        env:
          ENVIRONMENT: ${{ steps.env.outputs.env }}
        run: |
          if [[ "${{ inputs.version }}" != "" ]]; then
            version="${{ inputs.version }}"
          else
            version="$(cat ./apps/bookr/package.json | jq -r ".version")"
          fi

          if [[ "${{github.event_name}}" == "pull_request" ]]; then
            version="${version}-pr-${{github.event.number}}"
          else
            if [[ "${version}" != *"-rc"* && "${ENVIRONMENT}" != "production" ]]; then
              version="${version}-rc-${{github.run_number}}"
            fi
          fi

          echo "version=${version}" >> "$GITHUB_OUTPUT"

  code_style:
    uses: bookr-technologies/infra/.github/workflows/code-style.yaml@main
    if: github.event_name != 'workflow_dispatch'
    with:
      skipLint: false
      skipFormat: false

  deploy:
    runs-on: ubuntu-latest
    needs: [version]
    env:
      APP_VERSION: ${{ needs.version.outputs.version }}
      APP_ENV: ${{ needs.version.outputs.env }}
      APP_REVISION: ${{ github.sha }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Configure gcloud
        uses: 'google-github-actions/auth@v1'
        with:
          credentials_json: '${{ secrets.GCP_DEPLOY_SA }}'

      - name: 'Set up Cloud SDK'
        uses: 'google-github-actions/setup-gcloud@v1'
        with:
          project_id: ${{ secrets.GCP_PROJECT_ID }}

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::743062438238:role/GithubActionRole
          aws-region: ${{ env.AWS_REGION }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Firebase Certificate
        env:
          CERT: ${{ secrets.FIREBASE_CERT }}
        run: |
          echo "$CERT" | base64 -d > ./packages/firebase/firebase.cert.json

      - name: Sentry CLI configuration
        env:
          SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
        run: |
          echo "[auth]" > ./apps/bookr/.sentryclirc
          echo "token=${SENTRY_AUTH_TOKEN}" >> apps/bookr/.sentryclirc

      - name: Configure environment
        run: |
          curl https://github.com/mozilla/sops/releases/download/v3.7.3/sops-v3.7.3.linux.amd64 -L -o /tmp/sops
          chmod +x /tmp/sops

          echo "Setting up environment ${APP_ENV}"
          /tmp/sops -d "./secrets/env/${APP_ENV}.enc.json" > ./secrets/env/env.json

      - name: Create Metadata
        id: metadata
        env:
          ECR_REGISTRY: '${{ steps.login-ecr.outputs.registry }}'
          REPOSITORY: '${{ github.repository }}'
          APP_ENVIRONMENT: '${{ inputs.environment }}'
        run: |
          APP_REVISION="$(echo ${APP_REVISION} | cut -c1-8)"
          ENV_TAG="preview"
          if [[ "${APP_ENVIRONMENT}" == "production" ]]; then
            ENV_TAG="latest"
          fi

          IMAGE="${ECR_REGISTRY}/${REPOSITORY}/bookr:${APP_VERSION}"
          IMAGE_ENV="${ECR_REGISTRY}/${REPOSITORY}/bookr:${ENV_TAG}"

          echo "docker_image=${IMAGE}" >> $GITHUB_OUTPUT
          echo "docker_image_env=${IMAGE_ENV}" >> $GITHUB_OUTPUT

      - name: Build and push
        uses: docker/build-push-action@v6
        with:
          context: .
          file: apps/bookr/Dockerfile
          push: true
          cache-from: type=gha
          cache-to: type=gha,mode=max
          tags: |
            ${{ steps.metadata.outputs.docker_image }}
            ${{ steps.metadata.outputs.docker_image_env }}
          build-args: |
            APP_VERSION=${{ env.APP_VERSION }}
            APP_REVISION=${{ env.APP_REVISION }}
            APP_ENV=${{ env.APP_ENV }}

      - name: Deploy to Server
        uses: appleboy/ssh-action@v1.2.0
        with:
          host: ${{ secrets.SSH_HOST }}
          username: ${{ secrets.SSH_USER }}
          key: ${{ secrets.SSH_KEY }}
          port: ${{ secrets.SSH_PORT }}
          script: |
            docker stack deploy --detach=false --with-registry-auth -c /src/docker-stack/staging_bookr_ro/web.yaml staging_bookr_ro

      - name: Slack Notification
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_CHANNEL: ci-cd
          SLACK_COLOR: ${{ job.status }}
          SLACK_ICON: https://github.com/bookr-technologies.png?size=48
          SLACK_TITLE: New frontend deployment available
          SLACK_USERNAME: bookr-bot
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_MESSAGE: |
            A new docker images has been published to the ECR registry:
            - `${{ steps.metadata.outputs.docker_image }}`
            - `${{ steps.metadata.outputs.docker_image_env }}`
