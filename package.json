{"name": "bookr-technologies", "version": "0.0.0", "private": true, "packageManager": "yarn@1.22.19", "engines": {"node": ">=16.0.0"}, "workspaces": ["apps/*", "packages/*"], "scripts": {"build": "turbo run build", "dev": "turbo run dev --parallel --scope bookr", "test": "turbo run test --parallel", "type:check": "turbo run type:check --parallel", "lint": "turbo run lint", "format:write": "prettier --write \"**/*.{ts,tsx,md}\"", "format:check": "prettier --check \"**/*.{ts,tsx,md}\"", "link:cli": "yarn link --cwd apps/bkr", "unlink:cli": "yarn unlink --cwd apps/bkr", "prepare": "husky install", "cleanup": "rm -rf node_modules yarn.lock */*/node_modules */*/yarn.lock", "refresh": "yarn cleanup && yarn install"}, "devDependencies": {"@babel/core": "^7.21.8", "@babel/traverse": "^7.25.9", "@trivago/prettier-plugin-sort-imports": "^4.2.0", "@types/jest": "^29.5.1", "@types/node": "^20.1.7", "@types/react": "^18.2.6", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.40.0", "eslint-config-next": "^13.4.2", "eslint-config-prettier": "^9.0.0", "eslint-config-turbo": "^1.9.6", "eslint-plugin-react": "^7.32.2", "husky": "^8.0.3", "jest": "^29.5.0", "jest-environment-jsdom": "^29.5.0", "lint-staged": "^15.0.0", "prettier": "^3.0.2", "ts-jest": "^29.1.0", "ts-node": "^10.9.1", "turbo": "^1.9.6", "typescript": "^5.0.4"}}