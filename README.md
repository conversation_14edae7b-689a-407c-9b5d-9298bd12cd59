# Bookr Technologies

[![Deploy](https://github.com/bookr-technologies/bookr-technologies/actions/workflows/main.yaml/badge.svg)](https://github.com/bookr-technologies/bookr-technologies/actions/workflows/main.yaml)

A general purpose booking system for any kind of business.

## Getting Started

```shell
# Clone the repository (make sure you have SSH configured before cloning)
$ <NAME_EMAIL>:bookr-technologies/bookr-technologies.git
$ cd bookr-technologies

# Install dependencies
$ yarn install
$ yarn bkr postinstall

# Setup environment
$ cp ./secrets/env/example.json ./secrets/env/env.json

# Start the development server
$ yarn dev
```

## Development

The project is split into two parts:

-   The `apps` folder contains only the applications.
    -   The `bkr` folder contains the `bkr` CLI.
    -   The `com.bookr` folder contains the Bookr application.
    -   The `design-system` folder contains the design system & useful things to
        prototype using storybooks.
-   The `packages` folder contains the packages shared across applications.
    -   The `api` folder contains everything we need to deal with api queries &
        mutations.
    -   The `auth` folder contains everything we need to deal with
        authentication.
    -   The `core` folder contains the core of the application.
    -   The `eslint-config-custom` folder contains the custom eslint
        configuration.
    -   The `firebase` folder contains the firebase configuration.
    -   The `hooks` folder contains the custom react hooks.
    -   The `i18n` folder contains the internationalization configuration.
    -   The `jest` folder contains the custom jest configuration.
    -   The `sdk` folder contains bookr and next clients generated from the
        openapi schema.
    -   The `tsconfig` folder contains the custom typescript configuration.
    -   The `types` folder contains the custom types.
    -   The `ui` folder contains the custom ui components.

### Useful commands

```shell
# Build applications
$ yarn build

# Run applications in development mode
$ yarn dev

# Run tests
$ yarn test

# Run typescript type checking
$ yarn type:check

# Run linter
$ yarn lint

# Run prettier to format files
$ yarn format:write

# Run prettier check
$ yarn format:check

# Link the bkr cli
$ yarn link:cli

# Unlink the bkr cli
$ yarn unlink:cli
```

### Bookr CLI

The `bkr` CLI is used to manage the project. It is a simple CLI that uses the
`gluegun` [package](https://github.com/infinitered/gluegun/tree/master) to parse
the arguments and execute the commands.

```shell
# Run the bkr cli
$ yarn bkr

# or
$ bkr
```

#### Commands

| Command                | Alias   | Description                       |
| ---------------------- | ------- | --------------------------------- |
| **add**                | **a**   | Add a new package to the project  |
| **addScript**          |         | Add a new script to the project   |
| **api generate**       | **gen** | Generate the api client           |
| **generate app**       | **a**   | Generate a new app                |
| **generate component** | **c**   | Generate a new component          |
| **generate jest**      |         | Generate a new jest configuration |
| **generate favicon**   | **pkg** | Generate favicon                  |
| **postinstall**        |         | Run the post install script       |
| **upgrade**            |         | Upgrade the project dependencies  |
| **version**            | **v**   | Show the version                  |
| **help**               | **h**   | Show the help                     |
