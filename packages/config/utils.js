class Config {
    constructor(cfg) {
        this.props = {};
        try {
            this.props = require(cfg);
        } catch (e) {
            console.warn('Config %s file not found', cfg);
        }
    }

    get(value, defaultValue = null) {
        if (value in process.env && process.env[value]) {
            return process.env[value];
        }

        if (value in this.props && this.props[value]) {
            return this.props[value];
        }

        return defaultValue;
    }

    str(value, defaultValue = '') {
        return String(this.get(value) || defaultValue);
    }

    int(value, defaultValue) {
        return parseInt(this.get(value) || defaultValue);
    }

    bool(value, defaultValue = false) {
        return ['1', 'true', 'yes', 'on'].includes(
            String(this.get(value) ?? defaultValue),
        );
    }
}

module.exports = {
    createConfig: (props) => new Config(props),
};
