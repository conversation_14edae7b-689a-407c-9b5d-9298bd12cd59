/* eslint-disable @typescript-eslint/no-var-requires */
const path = require('path');
const { createConfig } = require('./utils');

const config = createConfig(
    path.resolve(__dirname, '../../secrets/env/env.json'),
);
const env = config.str('APP_ENV') || config.str('NODE_ENV', 'development');

/**
 *
 * @param {{staging: string, production: string}} values
 * @param {string} defaultValue
 */
function envValue(values, defaultValue = undefined) {
    return env in values ? values[env] : defaultValue;
}

/**
 * @return {import('./config').RuntimeConfig}
 */
function getRuntimeConfig() {
    return {
        env,
        production: env === 'production',
        version: config.str('APP_VERSION', 'n/a'),
        revision: config.str('APP_REVISION', 'n/a'),
        endpoints: {
            bookrApiUrl: config.str('BOOKR_API_URL'),
            nextApiUrl: config.str('NEXT_API_URL'),
        },
        urls: {
            web: config.str(
                'URL_WEB',
                envValue(
                    {
                        staging: 'https://staging.bookr.ro',
                        production: 'https://bookr.ro',
                    },
                    'http://localhost:3000',
                ),
            ),
            dashboard: config.str(
                'URL_DASHBOARD',
                envValue(
                    {
                        staging: 'https://staging.dashboard.bookr.ro',
                        production: 'https://dashboard.bookr.ro',
                    },
                    'http://localhost:3001',
                ),
            ),
            cdn: config.str(
                'URL_CDN',
                envValue(
                    {
                        staging: 'https://d230pchl7el2vc.cloudfront.net',
                        production: 'https://d230pchl7el2vc.cloudfront.net',
                    },
                    'https://d230pchl7el2vc.cloudfront.net',
                ),
            ),
        },
        googleMaps: {
            apiKey: config.str('GOOGLE_MAPS_API_KEY', ''),
        },
    };
}

/**
 * @return {import('./config').ServerRuntimeConfig}
 */
function getServerRuntimeConfig() {
    const runtimeConfig = getRuntimeConfig();
    return {
        ...runtimeConfig,
        endpoints: {
            ...runtimeConfig.endpoints,
            bookrApiUrl: config.str('BOOKR_API_URL_INTERNAL'),
            nextApiUrl: config.str('NEXT_API_URL_INTERNAL'),
        },
        redis: {
            host: config.str('REDIS_HOST', 'localhost'),
            port: config.int('REDIS_PORT', 6379),
            user: config.str('REDIS_USER', ''),
            password: config.str('REDIS_PASSWORD', ''),
        },
        pwa: {
            enabled: config.bool('PWA_ENABLED', false),
        },
    };
}

/**
 * @return {import('./config').ClientRuntimeConfig}
 */
function getPublicRuntimeConfig() {
    return {
        ...getRuntimeConfig(),
        analytics: {
            enabled: config.bool('ANALYTICS_ENABLED', false),
            mixpanel: {
                enabled: config.bool('MIXPANEL_ENABLED', false),
                token: config.str('MIXPANEL_TOKEN', ''),
            },
            googleTagManager: {
                enabled: config.bool('GOOGLE_TAG_MANAGER_ENABLED', false),
            },
            firebaseAnalytics: {
                enabled: config.bool('FIREBASE_ANALYTICS_ENABLED', false),
            },
            facebookPixel: {
                enabled: config.bool('FACEBOOK_PIXEL_ENABLED', false),
                pixelId: config.str('FACEBOOK_PIXEL_ID', ''),
            },
            hubspot: {
                enabled: config.bool('HUBSPOT_ENABLED', false),
            },
            sentry: {
                enabled: config.bool('SENTRY_ENABLED', false),
                dsn: config.str('SENTRY_DSN', ''),
            },
        },
    };
}

module.exports = {
    getServerRuntimeConfig,
    getPublicRuntimeConfig,
};
