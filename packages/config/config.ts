export interface RuntimeConfig {
    env: string;
    production: boolean;
    version: string;
    revision: string;
    endpoints: {
        bookrApiUrl: string;
        nextApiUrl: string;
    };
    urls: {
        cdn: string;
        web: string;
        dashboard: string;
    };

    googleMaps: {
        apiKey: string;
    };
}

export interface ServerRuntimeConfig extends RuntimeConfig {
    pwa: {
        enabled: boolean;
    };
    redis: {
        host: string;
        port: number;
        user: string;
        password: string;
    };
}

export interface ClientRuntimeConfig extends RuntimeConfig {
    analytics: {
        enabled: boolean;
        mixpanel: {
            enabled: boolean;
            token: string;
        };
        sentry: {
            enabled: boolean;
            dsn: string;
        };
        googleTagManager: {
            enabled: boolean;
        };
        firebaseAnalytics: {
            enabled: boolean;
        };
        facebookPixel: {
            enabled: boolean;
            pixelId: string;
        };
        hubspot: {
            enabled: boolean;
        };
    };
}
