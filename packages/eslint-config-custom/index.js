module.exports = {
    extends: [
        'next',
        'turbo',
        'prettier',
        'plugin:@typescript-eslint/recommended',
    ],
    parser: '@typescript-eslint/parser',
    plugins: ['@typescript-eslint'],
    rules: {
        '@next/next/no-html-link-for-pages': 'off',
        '@typescript-eslint/no-explicit-any': 'off',
        'no-restricted-imports': [
            'error',
            {
                paths: [
                    {
                        name: '@mui/material/*/*',
                        message: 'Avoid using @mui/material 3d level imports',
                    },
                    {
                        name: '@mui/material',
                        message: 'Avoid using @mui/material imports directly',
                    },
                    {
                        name: '@mui/icons-material',
                        message:
                            'Avoid using @mui/icons-material imports directly',
                    },
                    {
                        name: '@mui/x-date-pickers',
                        message:
                            'Avoid using @mui/x-date-pickers imports directly',
                    },
                ],
            },
        ],
    },
};
