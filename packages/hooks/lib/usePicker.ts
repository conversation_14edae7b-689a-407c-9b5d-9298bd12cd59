import { useRef, useState } from 'react';
import { ErrorCode } from '@bookr-technologies/core';
import { useEvent } from './useEvent';

interface PickOptions {
    accept?: string;
}

export function usePicker() {
    const [file, setFile] = useState<File | null>(null);
    const inputRef = useRef<HTMLInputElement | null>();
    const [isSelecting, setIsSelecting] = useState(false);

    const exec = useEvent(
        ({ accept }: PickOptions = {}) =>
            new Promise<File>((resolve, reject) => {
                setIsSelecting(true);
                inputRef.current ??= document.createElement('input');
                inputRef.current.type = 'file';
                inputRef.current.accept = accept ?? '*/*';
                inputRef.current.oncancel = () =>
                    reject(new ErrorCode('cancelled'));
                inputRef.current.onchange = () => {
                    setIsSelecting(false);
                    const file = inputRef.current?.files?.[0];
                    if (file) {
                        setFile(file);
                        resolve(file);
                        return;
                    }

                    reject(new ErrorCode('cancelled'));
                };
                inputRef.current.click();
            }),
    );

    const pick = useEvent(async (options: PickOptions = {}) => {
        try {
            return await exec(options);
        } finally {
            if (inputRef.current) {
                inputRef.current.remove();
                inputRef.current = null;
            }
        }
    });

    return {
        isSelecting,
        file,
        pick,
    };
}
