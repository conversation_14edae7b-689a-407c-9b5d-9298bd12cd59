import { useRef } from 'react';
import { useEvent } from './useEvent';

export function useDebouncedEvent<T extends any[]>(
    callback: (...args: T) => void,
    delay: number,
): (...args: T) => void {
    const timeoutRef = useRef<ReturnType<typeof setTimeout>>();
    const callbackEvent = useEvent(callback);

    return useEvent((...args: T) => {
        if (timeoutRef.current) {
            clearTimeout(timeoutRef.current);
        }

        timeoutRef.current = setTimeout(() => callbackEvent(...args), delay);
    });
}
