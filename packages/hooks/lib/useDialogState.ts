import type { DialogProps } from '@mui/material/Dialog';
import type { Breakpoint, Theme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import { atom, useAtom } from 'jotai';

interface DialogState {
    open: boolean;
}

interface Options {
    responsiveFullscreen?: true | Breakpoint | ((theme: Theme) => string);
}

const dialogStateAtom = atom<Record<any, DialogState>>({});

export function useDialogState(
    name: string,
    { responsiveFullscreen }: Options = {},
) {
    const [dialogState, setDialogState] = useAtom(dialogStateAtom);
    const fullscreenMatchBreakpoint = useMediaQuery<Theme>((theme) => {
        if (typeof responsiveFullscreen === 'boolean' && responsiveFullscreen) {
            return theme.breakpoints.down('sm');
        }

        if (typeof responsiveFullscreen === 'function') {
            return responsiveFullscreen(theme);
        }

        return theme.breakpoints.down(responsiveFullscreen ?? 'sm');
    });
    const state = dialogState[name] || { open: false };

    const open = () => {
        setDialogState((state) => ({
            ...state,
            [name]: { open: true },
        }));
    };

    const close = () => {
        setDialogState((state) => ({
            ...state,
            [name]: { open: false },
        }));
    };

    return {
        state,
        open,
        close,
        props: {
            open: state.open,
            fullscreen: fullscreenMatchBreakpoint,
            onClose: close,
        } as DialogProps,
    };
}
