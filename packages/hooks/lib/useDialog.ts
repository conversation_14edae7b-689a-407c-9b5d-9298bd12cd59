import { MutableRefObject, useRef, useState } from 'react';
import { Defer } from '@bookr-technologies/core';
import { useEvent } from './useEvent';

interface DialogProps {
    open: boolean;

    onClose(): void;
}

interface UseDialog<C = any> {
    props: DialogProps;
    context: MutableRefObject<C>;

    setState(state: boolean): void;

    open(): void;

    close(): void;

    lock(): Promise<void>;

    openWithContext(context: C): void;
}

export function useDialog<C = any>(): UseDialog<C> {
    const context = useRef<any>();
    const lockRef = useRef<Defer>();
    const [isOpen, setIsOpen] = useState(false);

    const onClose = useEvent(() => {
        setIsOpen(false);
        lockRef.current?.resolve();
        context.current = undefined;
        lockRef.current = undefined;
    });

    const handleOpen = useEvent(() => {
        lockRef.current = new Defer();
        setIsOpen(true);
    });

    const setState = useEvent((state: boolean) => {
        state ? handleOpen() : onClose();
    });

    const handleOpenWithContext = useEvent((value: any) => {
        handleOpen();
        context.current = value;
    });

    const lock = useEvent(async () => lockRef.current?.wait());

    return {
        context,
        lock,
        setState,
        open: handleOpen,
        close: onClose,
        openWithContext: handleOpenWithContext,
        props: {
            open: isOpen,
            onClose,
        },
    };
}
