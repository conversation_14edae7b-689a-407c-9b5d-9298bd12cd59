import { RefObject, useEffect, useState } from 'react';
import { Optional } from '@bookr-technologies/types';

interface Scroll {
    x: number;
    y: number;
}

function getScroll(el?: Optional<Element>): Scroll {
    if (el) {
        return {
            y: el.scrollTop,
            x: el.scrollLeft,
        };
    }

    if (typeof window !== 'undefined') {
        return {
            y: window.scrollY,
            x: window.scrollX,
        };
    }

    return {
        y: 0,
        x: 0,
    };
}

export function useScroll(el?: RefObject<Element>): Scroll {
    const [scroll, setScroll] = useState(() => getScroll(el?.current));

    useEffect(() => {
        function handler() {
            setScroll(getScroll(el?.current));
        }

        if (el?.current) {
            const scrollElement = el.current;
            scrollElement.addEventListener('scroll', handler);
            return () => scrollElement.removeEventListener('scroll', handler);
        }

        window.addEventListener('scroll', handler, { passive: true });
        window.dispatchEvent(new Event('scroll'));

        return () => window.removeEventListener('scroll', handler);
    }, [el]);

    return scroll;
}
