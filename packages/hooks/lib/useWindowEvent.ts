import { useEffect } from 'react';
import { useEvent } from './useEvent';

export function useWindowEvent<K extends keyof WindowEventMap>(
    event: string,
    handler: (event: WindowEventMap[K]) => void,
) {
    const callback = useEvent((ev) => handler(ev));

    useEffect(() => {
        window.addEventListener(event, callback, { passive: true });
        window.dispatchEvent(new Event(event));

        return () => window.removeEventListener(event, callback);
    }, [event, callback]);
}
