import { Dispatch, SetStateAction, useEffect, useState } from 'react';
import { silentPromise } from '@bookr-technologies/core';
import { PersistentStore } from '@bookr-technologies/core';
import { useEvent } from './useEvent';

export function usePersistentState<T>(
    key: string,
    defaultValue: T,
): [T, Dispatch<SetStateAction<T>>] {
    const [value, setValue] = useState<T>(defaultValue);

    useEffect(() => {
        silentPromise(PersistentStore.getItem(key)).then((result) => {
            if (result !== null) {
                setValue(result as T);
            }
        });
    }, [key]);

    return [
        value,
        useEvent(() => {
            setValue(value);
            // noinspection JSIgnoredPromiseFromCall
            silentPromise(PersistentStore.setItem(key, value));
        }),
    ];
}
