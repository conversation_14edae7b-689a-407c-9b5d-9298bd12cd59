import { useState } from 'react';
import { useEvent } from './useEvent';

type LoadingFromPromise = (
    promise: any | Promise<any> | (() => any | Promise<any>),
) => Promise<void>;

export function useLoading() {
    const [state, setState] = useState(false);
    const from = useEvent<LoadingFromPromise>(async (promise) => {
        setState(true);
        try {
            return await (typeof promise === 'function' ? promise() : promise);
        } catch (e) {
            throw e;
        } finally {
            setState(false);
        }
    });

    const start = useEvent(() => setState(true));
    const stop = useEvent(() => setState(false));

    return { state, from, start, stop };
}
