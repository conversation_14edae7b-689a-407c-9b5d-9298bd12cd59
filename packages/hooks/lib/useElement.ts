import { useMemo, useRef } from 'react';
import { useEvent } from './useEvent';

type ElementArg<T extends HTMLElement> = string | (() => T | null);

export function useElement<T extends HTMLElement = HTMLElement>(
    el: ElementArg<T>,
) {
    const elementRef = useRef<T | null>(null);

    const get = useEvent(() => {
        if (!elementRef.current) {
            elementRef.current =
                typeof el === 'function' ? el() : document.querySelector(el);
        }

        return elementRef.current;
    });

    return useMemo(() => ({ get }), [get]);
}
