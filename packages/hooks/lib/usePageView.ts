import Router, { useRouter } from 'next/router';
import { useEffect } from 'react';
import { track, TrackEvents } from '@bookr-technologies/analytics';
import { useEvent } from './useEvent';

export function usePageView() {
    const router = useRouter();

    const handler = useEvent((pathname: string) => {
        track(TrackEvents.PageView, { pathname });
    });

    useEffect(() => {
        Router.events.on('routeChangeComplete', handler);
        handler(router.asPath);

        return () => {
            Router.events.off('routeChangeComplete', handler);
        };
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [handler]);
}
