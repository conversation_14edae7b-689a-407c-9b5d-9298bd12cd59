import { Value } from '@firebase/remote-config';
import { useEffect, useMemo, useState } from 'react';
import { getValue, getValueAsync } from '@bookr-technologies/firebase';

export enum RemoteConfigFlag {
    MaintenanceEnabled = 'webMaintenanceEnabled',
    SocialLoginEnabled = 'socialLoginEnabled',
}

export function useFeatureFlag(flag: RemoteConfigFlag) {
    return useMemo(() => {
        if (typeof window === 'undefined') {
            return null;
        }

        return getValue(flag);
    }, [flag]);
}

export function useFeatureFlagAsync(flag: RemoteConfigFlag) {
    const [value, setValue] = useState<Value | null>(null);

    useEffect(() => {
        if (typeof window === 'undefined') {
            return;
        }

        setValue(null);
        getValueAsync(flag).then(setValue);
    }, [flag]);

    return value;
}
