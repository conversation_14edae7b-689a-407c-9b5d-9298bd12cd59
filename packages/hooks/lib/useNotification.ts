import { OptionsWithExtraProps, SnackbarKey, useSnackbar } from 'notistack';
import { useEvent } from './useEvent';

export function useNotification() {
    const notistack = useSnackbar();

    const close = useEvent((key?: SnackbarKey) => notistack.closeSnackbar(key));

    const info = useEvent(
        (
            message: string,
            options?: Omit<OptionsWithExtraProps<'info'>, 'variant'>,
        ) =>
            notistack.enqueueSnackbar(message, { variant: 'info', ...options }),
    );

    const warning = useEvent(
        (
            message: string,
            options?: Omit<OptionsWithExtraProps<'warning'>, 'variant'>,
        ) =>
            notistack.enqueueSnackbar(message, {
                variant: 'warning',
                ...options,
            }),
    );

    const success = useEvent(
        (
            message: string,
            options?: Omit<OptionsWithExtraProps<'success'>, 'variant'>,
        ) =>
            notistack.enqueueSnackbar(message, {
                variant: 'success',
                ...options,
            }),
    );

    const error = useEvent(
        (
            message: string,
            options?: Omit<OptionsWithExtraProps<'error'>, 'variant'>,
        ) =>
            notistack.enqueueSnackbar(message, {
                variant: 'error',
                ...options,
            }),
    );

    return { info, warning, success, error, close };
}
