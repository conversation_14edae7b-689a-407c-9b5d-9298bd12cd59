import { RefObject, useEffect } from 'react';
import { retry, silentPromise } from '@bookr-technologies/core';
import { useEvent } from './useEvent';

type KeysOf<T> = T extends (key: infer K, ...args: any[]) => any ? K : never;
type HandlerOf<T> = T extends (key: any, handler: infer H) => any ? H : never;

export function useElementEvent<
    E extends HTMLElement,
    K extends KeysOf<E['addEventListener']>,
    H extends HandlerOf<E['addEventListener']>,
>(el: RefObject<E | null>, event: K, handler: H) {
    const callback = useEvent((ev) => {
        if (typeof handler === 'function') {
            handler(ev);
        }
    });

    useEffect(() => {
        silentPromise(
            retry(() => {
                if (!el.current) {
                    throw new Error('Element not found');
                }
            }),
        ).then(() => {
            const element = el.current;
            element?.addEventListener(event, callback, { passive: true });
            element?.dispatchEvent(new Event(event));
        });
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [event, callback]);

    useEffect(
        () => () => {
            // eslint-disable-next-line react-hooks/exhaustive-deps
            el.current?.removeEventListener(event, callback);
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [],
    );
}
