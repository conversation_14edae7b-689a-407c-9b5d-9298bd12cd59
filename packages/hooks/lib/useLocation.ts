import { useAtom } from 'jotai';
import { useEffect } from 'react';
import { captureError } from '@bookr-technologies/analytics';
import { Duration, persistentAtom } from '@bookr-technologies/core';

export class LocationError extends Error {
    constructor(
        public type: LocationErrorType,
        public error: GeolocationPositionError | Error,
    ) {
        super(error.message);
    }
}

export enum LocationErrorType {
    PositionUnavailable = 'geolocation.position_unavailable',
    Timeout = 'geolocation.timeout',
    PermissionDenied = 'geolocation.permission_denied',
    UnknownError = 'geolocation.unknown_error',
}

interface Options extends PositionOptions {
    onSuccess?: (coords: LocationCoordinates) => void;
    onError?: (error: LocationError) => void;
}

export type LocationCoordinates = Omit<GeolocationCoordinates, 'toJSON'>;

function getLocation() {
    return new Promise<LocationCoordinates>((resolve, reject) => {
        try {
            navigator.geolocation.getCurrentPosition(
                (result) => {
                    const { coords } = result;
                    resolve(coords);
                },
                (error) => {
                    let errorType: LocationErrorType;

                    switch (error.code) {
                        case GeolocationPositionError.POSITION_UNAVAILABLE:
                            errorType = LocationErrorType.PositionUnavailable;
                            break;
                        case GeolocationPositionError.TIMEOUT:
                            errorType = LocationErrorType.Timeout;
                            break;
                        case GeolocationPositionError.PERMISSION_DENIED:
                            errorType = LocationErrorType.PermissionDenied;
                            break;
                        default:
                            errorType = LocationErrorType.UnknownError;
                    }

                    reject(new LocationError(errorType, error));
                },
                {
                    timeout: Duration.seconds(5),
                    maximumAge: Duration.minutes(5),
                    enableHighAccuracy: false,
                },
            );
        } catch (e) {
            captureError(e);
            reject(
                new LocationError(LocationErrorType.UnknownError, e as Error),
            );
        }
    });
}

const locationAtom = persistentAtom<LocationCoordinates>('device.location');

export function useLocation(options?: Options) {
    const [location, setLocation] = useAtom(locationAtom);

    useEffect(() => {
        (async () => {
            try {
                const location = await getLocation();
                setLocation(location);
            } catch (e) {
                options?.onError?.(e as LocationError);
            }
        })();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    useEffect(() => {
        if (location.data) {
            options?.onSuccess?.(location.data);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [location.data]);

    return location.data;
}
