import { type FormikConfig } from 'formik';
import { useMemo } from 'react';
import { type ObjectShape } from 'yup';
import * as Yup from 'yup';
import { value, Value } from '@bookr-technologies/core';
import { useEvent } from './useEvent';

export function useValidationSchema(
    schema: Value<ObjectShape, [yup: typeof Yup]>,
    deps: any[] = [],
): Pick<
    FormikConfig<unknown>,
    'validationSchema' | 'validateOnBlur' | 'validateOnChange'
> {
    const createSchema = useEvent(() => Yup.object().shape(value(schema, Yup)));

    // eslint-disable-next-line react-hooks/exhaustive-deps
    const validationSchema = useMemo(() => createSchema(), deps);

    return {
        validateOnBlur: true,
        validateOnChange: true,
        validationSchema,
    };
}
