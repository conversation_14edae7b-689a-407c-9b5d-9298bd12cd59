import { RefObject, useRef, useState } from 'react';
import { clamp } from '@bookr-technologies/core';
import { useDebouncedEvent } from './useDebouncedEvent';
import { useElementEvent } from './useElementEvent';
import { useEvent } from './useEvent';

type Meta = {
    maxScroll: number;
    fullSize: number;
    firstElementWidth: number;
    scrollWidth: number;
    scrollLeft: number;
    clientWidth: number;
    normalizedScrollLeft: number;
};

function getMeta(
    elementRef: RefObject<HTMLDivElement>,
    gap: number,
    lastMeta?: Meta,
): Meta {
    const firstElementWidth =
        elementRef.current?.firstElementChild?.clientWidth ?? 0;
    const scrollWidth = elementRef.current?.scrollWidth ?? 0;
    const clientWidth = elementRef.current?.clientWidth ?? 0;
    const fullSize = firstElementWidth + gap;
    const scrollLeft = elementRef.current?.scrollLeft ?? 0;

    let rounder = Math.ceil;
    if (lastMeta && lastMeta.scrollLeft > scrollLeft) {
        rounder = Math.floor;
    }

    let normalizedScrollLeft = rounder(scrollLeft / fullSize) * fullSize;

    if (scrollLeft + clientWidth >= scrollWidth - 1) {
        normalizedScrollLeft = scrollWidth - clientWidth;
    }

    return {
        maxScroll: scrollWidth - clientWidth,
        fullSize,
        firstElementWidth,
        normalizedScrollLeft,
        scrollWidth,
        scrollLeft,
        clientWidth,
    };
}

export function useElementsScroller(gap: number) {
    const lastMeta = useRef<Meta>();
    const elementRef = useRef<HTMLDivElement>(null);
    const [isFirst, setFirst] = useState(false);
    const [isLast, setIsLast] = useState(false);
    const previous = useEvent(() => handleScroll(-1));
    const next = useEvent(() => handleScroll(1));

    function updateScroll(scrollX: number, meta: Meta) {
        setFirst(scrollX <= 0);
        setIsLast(scrollX >= meta.maxScroll);

        elementRef.current?.scroll?.({
            top: 0,
            left: clamp(scrollX, 0, meta.maxScroll),
            behavior: 'smooth',
        });
    }

    function handleScroll(direction: number) {
        const meta = getMeta(elementRef, gap);
        const step = meta.fullSize * direction;
        const scrollX = meta.scrollLeft + step;

        updateScroll(scrollX, meta);
    }

    useElementEvent(
        elementRef,
        'scroll',
        useDebouncedEvent(() => {
            const meta = getMeta(elementRef, gap, lastMeta.current);
            updateScroll(meta.normalizedScrollLeft, meta);
            lastMeta.current = meta;
        }, 100),
    );

    return {
        elementRef,
        isFirst,
        isLast,
        previous,
        next,
    };
}
