import { BaseService } from '../../lib';
import { PaginatedResponse } from '../../lib';
import * as ReviewsForms from '../forms/ReviewsForms';
import { AppointmentReviewModel } from '../models';
import { RatingsChartModel } from '../models';

export class ReviewsService extends BaseService {
    protected readonly basePath: string = '/reviews';

    public getReviews(params: ReviewsForms.GetReviewsParams) {
        return this.get<PaginatedResponse<AppointmentReviewModel>>('/', {
            params,
        });
    }

    public getReviewsChart(params: ReviewsForms.GetReviewsChartParams) {
        return this.get<RatingsChartModel>('/chart', { params });
    }
}
