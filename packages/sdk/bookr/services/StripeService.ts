import { BaseService } from '../../lib';
import { PeriodTypeEnum, SubscriptionPlanTypeEnum } from '../enums';
import { StripeForms } from '../forms';
import { SubscriptionPlanModel } from '../models';

export class StripeService extends BaseService {
    protected readonly basePath: string = '/stripe';

    public async getSubscriptionPlans(withCustom?: boolean) {
        const result =
            await this.get<
                Record<SubscriptionPlanTypeEnum, SubscriptionPlanModel[]>
            >('/subscriptionPlans');

        if (withCustom) {
            if (withCustom) {
                result.data = {
                    ...result.data,
                    CUSTOM: [
                        {
                            period: PeriodTypeEnum.Monthly,
                            priceId: 'custom',
                            price: 'custom',
                        },
                        {
                            period: PeriodTypeEnum.Yearly,
                            priceId: 'custom',
                            price: 'custom',
                        },
                    ],
                };
            }
        }

        return result;
    }

    async createCheckoutSession(data: StripeForms.CreateCheckoutSessionData) {
        return this.post<StripeForms.CreateCheckoutSessionResponse>(
            '/checkoutSession',
            { data },
        );
    }
}
