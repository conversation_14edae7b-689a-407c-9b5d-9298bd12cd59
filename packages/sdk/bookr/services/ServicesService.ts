import { BaseService } from '../../lib';
import { ServiceForms } from '../forms';
import { ServiceModel } from '../models';

export class ServicesService extends BaseService {
    protected readonly basePath: string = '/services';

    public getService({ serviceId }: ServiceForms.GetServiceParams) {
        return this.get<ServiceModel>('/{serviceId}', {
            path: { serviceId },
        });
    }
}
