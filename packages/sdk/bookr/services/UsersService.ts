import { BaseService } from '../../lib';
import * as UserForms from '../forms/UserForms';
import { MinimalBusinessModel } from '../models';
import { UserMetadataModel } from '../models';
import { UserModel } from '../models';

export class UsersService extends BaseService {
    protected readonly basePath: string = '/users';

    public createUser(data: UserForms.CreateUserData) {
        return this.post<UserModel>('/', { data });
    }

    public deleteUser() {
        return this.delete<boolean>('/');
    }

    public getById(path: UserForms.GetByIdPath) {
        return this.get<UserModel>('/{uid}', { path });
    }

    public updateUser({ uid, ...data }: UserForms.UpdateUserData) {
        return this.put<UserModel>('/{uid}', {
            data,
            path: { uid },
        });
    }

    public getMetadata(params: UserForms.GetMetadataParams) {
        return this.get<UserMetadataModel>('/metadata', { params });
    }

    public uploadUserPhoto(formData: UserForms.UploadUserPhotoData) {
        const data = this.client.objectToFormData(formData);

        return this.post<UserModel>('/photoURL', { data });
    }

    public getFavourites() {
        return this.get<MinimalBusinessModel[]>('/favourites');
    }

    public addFavourite(data: UserForms.AddFavouriteData) {
        return this.post<MinimalBusinessModel[]>('/favourites', { data });
    }

    public getFavourite(path: UserForms.GetFavouritePath) {
        return this.get<MinimalBusinessModel>('/favourites/{businessId}', {
            path,
        });
    }

    public deleteFavorite(path: UserForms.DeleteFavoritePath) {
        return this.delete<MinimalBusinessModel[]>(`/favourites/{businessId}`, {
            path,
        });
    }
}
