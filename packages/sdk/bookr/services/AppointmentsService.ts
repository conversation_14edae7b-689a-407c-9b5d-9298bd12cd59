import getUnixTime from 'date-fns/getUnixTime';
import { getTimezoneOffset } from '@bookr-technologies/core';
import { BaseService, PaginatedParams, PaginatedResponse } from '../../lib';
import * as AppointmentsForms from '../forms/AppointmentsForms';
import {
    AppointmentModel,
    CreatedAppointmentModel,
    TimeslotModel,
} from '../models';

export class AppointmentsService extends BaseService {
    protected readonly basePath: string = '/appointments';

    public getAppointmentsByUserId(
        params: AppointmentsForms.GetAppointmentsByUserIdParams,
    ) {
        return this.get<PaginatedResponse<AppointmentModel>, PaginatedParams>(
            this.client.absoluteUrl('/users/appointments'),
            { params },
        );
    }

    public createAppointment(data: AppointmentsForms.CreateAppointmentData) {
        const { timezoneOffset, timestamp, ...rest } = data;
        return this.post<CreatedAppointmentModel>('/', {
            data: {
                ...rest,
                timezoneOffset: timezoneOffset ?? getTimezoneOffset(),
                timestamp: getUnixTime(timestamp),
            },
        });
    }

    public getAppointments(params: AppointmentsForms.GetAppointmentsParams) {
        return this.get<AppointmentModel[]>('/', { params });
    }

    public getAppointment(path: AppointmentsForms.GetAppointmentPath) {
        return this.get<AppointmentModel>('{appointmentId}', {
            path,
        });
    }

    public cancelAppointment(path: AppointmentsForms.CancelAppointmentPath) {
        return this.put<boolean>('/cancel/{appointmentId}', { path });
    }

    public getTimeslots(params: AppointmentsForms.GetTimeslotsParams) {
        return this.get<TimeslotModel[]>('/generateTimeslots', { params });
    }

    public getDaysThatCanBeBooked(
        params: AppointmentsForms.GetDaysThatCanBeBookedParams,
    ) {
        return this.get<Record<string, boolean>>('/daysThatCanBeBooked', {
            params,
        });
    }
}
