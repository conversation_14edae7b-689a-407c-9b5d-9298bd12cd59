import format from 'date-fns/format';
import { BaseService } from '../../lib';
import * as WaitingListForms from '../forms/WaitingListForms';

export class WaitingListService extends BaseService {
    protected readonly basePath: string = '/waitingList';

    public subscribe({ date, ...params }: WaitingListForms.SubscribeParams) {
        return this.post<boolean>('/', {
            params: {
                date:
                    typeof date === 'string'
                        ? date
                        : format(date, 'yyyy-MM-dd'),
                ...params,
            },
        });
    }
}
