import { BaseService } from '../../lib';
import * as BusinessesForms from '../forms/BusinessesForms';
import {
    AlgoliaBusinessModel,
    BusinessModel,
    BusinessReportModel,
    SearchBusinessModel,
    SitemapBusinessModel,
} from '../models';

export class BusinessesService extends BaseService {
    protected readonly basePath: string = '/business';

    public getById({ id, ...params }: BusinessesForms.GetByIDParams) {
        return this.get<BusinessModel>('/{id}', { params, path: { id } });
    }

    public search(params: BusinessesForms.SearchParams) {
        return this.get<AlgoliaBusinessModel>('/search', { params });
    }

    public getBestBusinesses(params: BusinessesForms.GetBestBusinessesParams) {
        return this.get<SearchBusinessModel[]>('/best', { params });
    }

    public getNearByBusinesses(
        params: BusinessesForms.GetNearByBusinessesParams,
    ) {
        return this.get<SearchBusinessModel[]>('/nearby', { params });
    }

    public getRecentBusinesses(
        params: BusinessesForms.GetRecentBusinessesParams,
    ) {
        return this.get<SearchBusinessModel[]>('/recent', { params });
    }

    public getBookedBusinesses() {
        return this.get<SearchBusinessModel[]>('/booked');
    }

    public reportContent(data: BusinessesForms.BusinessReportData) {
        return this.post<BusinessReportModel>(
            this.client.absoluteUrl('/reportContent'),
            { data },
        );
    }

    public getSitemapBusinesses() {
        return this.get<SitemapBusinessModel[]>('/sitemap');
    }
}
