import type { GeoLocationModel } from './GeoLocationModel';

export interface SearchBusinessModel {
    _geoloc: GeoLocationModel;
    averageRating: number;
    categories: string[];
    description: string;
    formattedAddress: string;
    id: string;
    name: string;
    noOfAppointmentsRounded: number;
    objectID: string;
    photos: string[];
    profilePicture: string;
    services: string[];
    slug: string;
    virtualTourURL: string;
    hasServices: boolean;

    businessServices: {
        currency: string;
        duration: number;
        id: number;
        name: string;
        ownerName: string;
        price: number;
        serviceRank: number;
        staffRank: number;
    }[];
}
