import type { SearchBusinessModel } from './SearchBusinessModel';

export type AlternativeModel = {
    type: string;
    words: string[];
    typos: number;
    offset: number;
    length: number;
};

export interface QueryMatchModel {
    alternatives: AlternativeModel[];
}

export interface ExplainModel {
    params: Record<string, any>;
    match: QueryMatchModel;
}

export interface FacetStatsModel {
    min: number;
    max: number;
    avg: number;
    sum: number;
}

export interface FacetsOrderModel {
    order: string[];
}

export interface FacetValuesOrderModel {
    order: string[];
    sortRemainingBy: string;
}

export interface FacetOrderingModel {
    facets: FacetsOrderModel;
    values: Record<string, FacetValuesOrderModel>;
}

export interface RenderingContentModel {
    facetOrdering: FacetOrderingModel;
}

export interface AlgoliaBusinessModel {
    abTestVariantID: number;
    appliedRelevancyStrictness: number;
    appliedRules: Record<string, any>[];
    aroundLatLng: string;
    automaticRadius: string;
    exhaustiveFacetsCount: boolean;
    exhaustiveNbHits: boolean;
    explain: ExplainModel;
    facets: Record<string, any>;
    facets_stats: Record<string, FacetStatsModel>;
    hits: SearchBusinessModel[];
    hitsPerPage: number;
    index: string;
    indexUsed: string;
    length: number;
    message: string;
    nbHits: number;
    nbPages: number;
    nbSortedHits: number;
    offset: number;
    page: number;
    params: string;
    parsedQuery: string;
    processed: boolean;
    processingTimeMS: number;
    query: string;
    queryAfterRemoval: string;
    queryID: string;
    renderingContent: RenderingContentModel;
    serverUsed: string;
    userData: Record<string, any>[];
}
