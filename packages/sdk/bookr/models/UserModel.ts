import { Nullable } from '@bookr-technologies/types';
import { AccountTypeEnum, PrivilegeTypeEnum, UserInterestEnum } from '../enums';
import { AppointmentModel } from './AppointmentModel';
import { BreakModel } from './BreakModel';
import type { ReviewAverageModel } from './ReviewAverageModel';
import type { ServiceModel } from './ServiceModel';
import type { UserSettingsModel } from './UserSettingsModel';
import type { WorkingHourModel } from './WorkingHourModel';

export interface UserModel {
    accountType: AccountTypeEnum;
    appointmentsAsClient: AppointmentModel[];
    appointmentsAsStaff: AppointmentModel[];
    breaks: BreakModel[];
    business: Nullable<{
        id: string;
    }>;
    city: string | null;
    country: string | null;
    dateOfBirth: string | null;
    displayName: string;
    email: string;
    hidden: boolean;
    interests: UserInterestEnum[];
    language: string;
    maxFutureDaysAppointment: number;
    phoneNumber: string;
    photoURL: string;
    privileges: PrivilegeTypeEnum[];
    rating: ReviewAverageModel;
    services: ServiceModel[];
    staffRank: number;
    uid: string;
    userSettings: UserSettingsModel;
    workingHours: WorkingHourModel[];
}
