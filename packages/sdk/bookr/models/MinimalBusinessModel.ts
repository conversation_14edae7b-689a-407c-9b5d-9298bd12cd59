import type { CategoryModel } from './CategoryModel';
import { ReviewAverageModel } from './ReviewAverageModel';
import type { WorkingHourModel } from './WorkingHourModel';

export type MinimalBusinessModel = {
    categories: CategoryModel[];
    description: string;
    facebookURL: string;
    formattedAddress: string;
    hidden: boolean;
    id: string;
    instagramURL: string;
    latitude: number;
    latitudeDelta: number;
    longitude: number;
    longitudeDelta: number;
    name: string;
    phoneNumber: string;
    photos: string[];
    profilePicture: string;
    slug: string;
    startingDate: string;
    virtualTourURL: string;
    websiteURL: string;
    workingHours: WorkingHourModel[];
    zoneId: string;
    reviewInfo?: ReviewAverageModel;
};
