import { IntegrationTypeEnum } from '../enums';
import { MinimalBusinessModel } from './MinimalBusinessModel';
import { StaffMemberModel } from './StaffMemberModel';

export interface ServiceModel {
    pricePerSession: number;
    breakBetweenServices: number;
    color: string;
    currency: string;
    description: string;
    duration: number;
    hiddenFromClients: boolean;
    id: number;
    inactive: boolean;
    name: string;
    numberOfSessions: number;
    onlineEvent: boolean;
    onlineEventIntegrationType: IntegrationTypeEnum;
    price: number;
    serviceRank: number;
    staff?: StaffMemberModel;
    business?: MinimalBusinessModel;
}
