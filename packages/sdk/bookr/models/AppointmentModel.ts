import { AppointmentStatusEnum } from '../enums';
import { ActivityModel } from './ActivityModel';
import { MinimalStaffMemberModel } from './MinimalStaffMemberModel';
import { MinimalUserModel } from './MinimalUserModel';
import { NoteModel } from './NoteModel';
import { ServiceEventModel } from './ServiceEventModel';
import { ServiceModel } from './ServiceModel';

export interface AppointmentModel {
    activities: ActivityModel[];
    cancelled: boolean;
    client: MinimalUserModel;
    clientArrivedAtLocation: boolean;
    createdAt: string;
    dateTime: string;
    id: number;
    noShow: boolean;
    notes: NoteModel[];
    onlineEvent: ServiceEventModel;
    recurrent: boolean;
    service: ServiceModel;
    staff: MinimalStaffMemberModel;
    status: AppointmentStatusEnum;
}
