import type { MinimalBusinessModel } from './MinimalBusinessModel';
import type { ReviewAverageModel } from './ReviewAverageModel';
import type { UserModel } from './UserModel';

export interface BusinessModel extends MinimalBusinessModel {
    blocked: string[];
    createdAt: string;
    hidden: boolean;
    reviewInfo: ReviewAverageModel;
    smsRemindersEnabled: boolean;
    staffMembers: UserModel[];
    templateAppointmentCancelled: string;
    templateAppointmentCreated: string;
    templateAppointmentReminder: string;
    templateAppointmentRescheduled: string;
    totalSMS: number;
}
