import { Optional } from '@bookr-technologies/types';
import { AccountTypeEnum } from '../enums';

export interface UpdateUserData extends Partial<CreateUserData> {
    uid: string;
}

export interface CreateUserData {
    uid: string;
    displayName: string;
    email: string;
    phoneNumber: string;
    language: string;
    photoURL?: string;
    accountType: AccountTypeEnum;
}

export interface GetByIdPath {
    uid: string;
}

export interface GetMetadataParams {
    email: Optional<string>;
}

export interface UploadUserPhotoData {
    file?: Blob;
}

export interface AddFavouriteData {
    businessId: string;
}

export interface GetFavouritePath {
    businessId: string;
}

export interface DeleteFavoritePath {
    businessId: string;
}
