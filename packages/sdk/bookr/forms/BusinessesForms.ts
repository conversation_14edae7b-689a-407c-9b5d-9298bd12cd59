import { PaginatedParams } from '../../lib';
import { ReportReasonEnum } from '../enums';

export type GetRecentBusinessesParams = PaginatedParams;

export interface GetByIDParams {
    id: string;
    includeAppointments?: boolean;
    daysInPast?: number;
    daysInFuture?: number;
    timestampFrom?: number;
    timestampTo?: number;
}

export interface SearchParams {
    text?: string;
    category?: string;
    latLng?: string;
    radius?: string;
    page?: string;
    size?: string;
}

export interface GetNearByBusinessesParams {
    radius?: string;
    latLng?: string;
}

export interface GetBestBusinessesParams {
    radius?: string;
    latLng?: string;
}

export interface BusinessReportData {
    businessId: string;
    reason: ReportReasonEnum;
}
