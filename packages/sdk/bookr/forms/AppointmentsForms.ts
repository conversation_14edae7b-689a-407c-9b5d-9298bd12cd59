import { PaginatedParams } from '../../lib';

export type GetAppointmentsByUserIdParams = PaginatedParams;

export interface CancelAppointmentPath {
    appointmentId: number;
}

export interface CreateAppointmentData {
    staffId: string;
    timestamp: number | Date;
    serviceId: number;
    timezoneOffset: number;
}

export interface GetAppointmentPath {
    appointmentId: number;
}

export interface GetAppointmentsParams {
    businessId: string;
    staffId: string;
}

export interface GetTimeslotsParams {
    staffId: string;
    date: string;
    serviceId: string | number;
}

export interface GetDaysThatCanBeBookedParams {
    staffId: string;
    serviceId: string | number;
}
