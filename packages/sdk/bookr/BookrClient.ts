import { BaseClient } from '../lib';
import {
    AppointmentsService,
    BusinessesService,
    CategoriesService,
    ReviewsService,
    ServicesService,
    StripeService,
    UsersService,
    WaitingListService,
} from './services';

export class BookrClient extends BaseClient {
    public readonly appointments = new AppointmentsService(this);
    public readonly businesses = new BusinessesService(this);
    public readonly categories = new CategoriesService(this);
    public readonly reviews = new ReviewsService(this);
    public readonly services = new ServicesService(this);
    public readonly stripe = new StripeService(this);
    public readonly users = new UsersService(this);
    public readonly waitingList = new WaitingListService(this);
}
