export type PageableObject = {
    offset: number;
    sort: PageSort;
    pageNumber: number;
    pageSize: number;
    paged: boolean;
    unpaged: boolean;
};

export type PageSort = {
    empty: boolean;
    sorted: boolean;
    unsorted: boolean;
};

export interface PaginatedResponse<T = any> {
    content: T[];
    empty: boolean;
    first: boolean;
    last: boolean;
    number: number;
    numberOfElements: number;
    pageable: PageableObject;
    size: number;
    sort: PageSort;
    totalElements: number;
    totalPages: number;
}
