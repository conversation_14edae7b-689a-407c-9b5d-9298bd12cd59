import Axios, {
    AxiosInstance,
    AxiosResponse,
    InternalAxiosRequestConfig,
} from 'axios';
import FormData from 'form-data';
import { stringify } from 'qs';
import { cleanSlashes, str, value } from '@bookr-technologies/core';
import { ClientOptions } from './ClientOptions';
import {
    HttpClientMethods,
    RequestConfig,
    RequestMethodConfig,
} from './HttpClientMethods';

export abstract class BaseClient implements HttpClientMethods {
    private readonly httpClient: AxiosInstance;

    constructor(public readonly options: ClientOptions) {
        this.httpClient = this.createInstance();
    }

    public request<T, D = any, P = any>(
        config: RequestConfig<P, D>,
    ): Promise<AxiosResponse<T>> {
        return this.httpClient.request<T>(config);
    }

    public get<T, P = any>(
        url: string,
        config?: RequestMethodConfig<P, never>,
    ) {
        return this.request<T>({
            method: 'get',
            url,
            ...config,
        });
    }

    public delete<T, P = any>(
        url: string,
        config?: RequestMethodConfig<P, never>,
    ) {
        return this.request<T>({
            method: 'delete',
            url,
            ...config,
        });
    }

    public post<T, D = any, P = any>(
        url: string,
        config?: RequestMethodConfig<P, D>,
    ) {
        return this.request<T>({
            method: 'post',
            url,
            ...config,
        });
    }

    public put<T, D = any, P = any>(
        url: string,
        config?: RequestMethodConfig<P, D>,
    ) {
        return this.request<T>({
            method: 'put',
            url,
            ...config,
        });
    }

    private createInstance() {
        const instance = Axios.create({
            baseURL: value(this.options.baseUrl),
            headers: {
                Accept: 'application/json',
            },
            timeout: 10000,
            timeoutErrorMessage: 'Request timed out',
            paramsSerializer: (params: any): string =>
                stringify(params, { arrayFormat: 'repeat' }),
        });

        instance.interceptors.request.use((config) => this.onRequest(config));
        instance.interceptors.response.use(
            (config) => this.onResponse(config),
            (error) => this.onResponseError(error),
        );

        return instance;
    }

    private isRestBody(
        response: AxiosResponse,
    ): response is AxiosResponse<{ data: any }> {
        return (
            response.data &&
            response.data.data &&
            Object.keys(response.data).length === 1
        );
    }

    private onResponse<T>(response: AxiosResponse<T>) {
        if (this.isRestBody(response)) {
            response.data = response.data.data as any;
        }
        return response;
    }

    private onResponseError(error: any) {
        if (error.response) {
            error.response = this.onResponse(error.response);
        }

        return Promise.reject(error);
    }

    private async onRequest(
        config: RequestConfig & InternalAxiosRequestConfig,
    ) {
        if (!config.headers.hasAuthorization()) {
            const token = await value(this.options.getBearer);
            if (token) {
                config.headers.setAuthorization(`Bearer ${token}`);
            }
        }

        const baseUrl = value(this.options.baseUrl);
        const reqBaseUrl = config.baseURL ?? '';

        if (reqBaseUrl !== baseUrl) {
            config.baseURL = baseUrl;
            if (!this.isAbsoluteUrl(config.url)) {
                const newUrl = str(reqBaseUrl) + '/' + str(config.url);
                config.url = cleanSlashes(newUrl);
            } else {
                config.url = config.url?.substring(1);
            }
        }

        config.url = this.buildUrl(config.url, config.path || config.params);

        return config;
    }

    objectToFormData<T extends object>(formData: T) {
        const form = new FormData();

        Object.entries(formData).forEach(([key, value]) => {
            form.append(key, value);
        });

        return form;
    }

    public absoluteUrl(url: string) {
        return `^${url}`;
    }

    private isAbsoluteUrl(url: string | undefined) {
        return url?.startsWith('^');
    }

    private buildUrl(url: string | undefined, params: any) {
        return (
            url?.replace(/{([^}]+)}/g, (_, key) =>
                encodeURIComponent(params[key]),
            ) ?? ''
        );
    }
}
