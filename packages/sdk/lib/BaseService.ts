import { BaseClient } from './BaseClient';
import {
    HttpClientMethods,
    RequestConfig,
    RequestMethodConfig,
} from './HttpClientMethods';

export abstract class BaseService implements HttpClientMethods {
    protected abstract readonly basePath: string;

    constructor(protected client: BaseClient) {}

    request<T, D = any, P = any>(config: RequestConfig<P, D>) {
        return this.client.request<T>({
            baseURL: this.basePath,
            ...config,
        });
    }

    get<T, P = any>(url: string, config?: RequestMethodConfig<P, never>) {
        return this.client.get<T, P>(url, {
            baseURL: this.basePath,
            ...config,
        });
    }

    delete<T, P = any>(url: string, config?: RequestMethodConfig<P, never>) {
        return this.client.delete<T, P>(url, {
            baseURL: this.basePath,
            ...config,
        });
    }

    post<T, D = any, P = any>(url: string, config?: RequestMethodConfig<P, D>) {
        return this.client.post<T, D, P>(url, {
            baseURL: this.basePath,
            ...config,
        });
    }

    put<T, D = any, P = any>(url: string, config?: RequestMethodConfig<P, D>) {
        return this.client.put<T, D, P>(url, {
            baseURL: this.basePath,
            ...config,
        });
    }
}
