import { AxiosRequestConfig, AxiosResponse } from 'axios';

export type RequestConfig<P = any, D = any> = AxiosRequestConfig<D> & {
    params?: P;
    path?: Partial<P>;
};

export type RequestMethodConfig<P = any, D = any> = Omit<
    RequestConfig<P, D>,
    'method' | 'url'
>;

export interface HttpClientMethods {
    request<T, D = any, P = any>(
        config: RequestConfig<P, D>,
    ): Promise<AxiosResponse<T>>;

    get<T, P = any>(
        url: string,
        config?: RequestMethodConfig<P, never>,
    ): Promise<AxiosResponse<T>>;

    delete<T, P = any>(
        url: string,
        config?: RequestMethodConfig<P, never>,
    ): Promise<AxiosResponse<T>>;

    post<T, D = any, P = any>(
        url: string,
        config?: RequestMethodConfig<P, D>,
    ): Promise<AxiosResponse<T>>;

    put<T, D = any, P = any>(
        url: string,
        config?: RequestMethodConfig<P, D>,
    ): Promise<AxiosResponse<T>>;
}
