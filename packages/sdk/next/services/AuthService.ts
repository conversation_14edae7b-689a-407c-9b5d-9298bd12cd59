import { Optional } from '@bookr-technologies/types';
import { BaseService, OkResponse } from '../../lib';
import { UserSessionModel } from '../models';

export class AuthService extends BaseService {
    protected readonly basePath: string = '/auth';

    public postSession(token: Optional<string>) {
        return this.post<OkResponse>('/session', {
            data: { token },
        });
    }

    public getSession() {
        return this.get<UserSessionModel>('/session');
    }

    public deleteSession() {
        return this.delete<OkResponse>('/session');
    }
}
