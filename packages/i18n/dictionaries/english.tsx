import {
    formatNumber,
    num,
    pluralize,
} from '@bookr-technologies/core/utilities';
import {
    AppointmentStatusEnum,
    ReportReasonEnum,
} from '@bookr-technologies/sdk';
import { SingleProp, SingleRequiredProp } from '@bookr-technologies/types';
import { ExpectedMinLengthProps } from './types';
import { withTermination } from './utils';

export default {
    '3dVirtualTour': '3D Virtual Tour',
    Accept: 'Accept',
    Account: 'Account',
    Appointments: 'Appointments',
    'Become a Business': 'Become a Business',
    'Book now': 'Book now',
    'Business Owner': 'Business Owner',
    'Contact & Social': 'Contact & Social',
    Decline: 'Decline',
    'Explore 3D virtual tour': 'Explore 3D virtual tour',
    Favorites: 'Favorites',
    Feedback: 'Feedback',
    ID: 'ID',
    'Load more': 'Load more',
    Logout: 'Logout',
    'No reviews found': 'No reviews found',
    'No. of Reviews': ({
        noOfReviews,
        locale,
    }: {
        noOfReviews: number;
        locale: string;
    }) =>
        pluralize(noOfReviews, {
            one: 'One review',
            zero: 'No reviews',
            many: `${formatNumber(noOfReviews, locale)} Reviews`,
        }),
    Notifications: 'Notifications',
    Program: 'Program',
    Rating: ({ averageRating }: SingleProp<'averageRating'>) =>
        `${num(averageRating).toFixed(2)} Rating`,
    'Read more': 'Read more',
    Report: 'Report',
    [ReportReasonEnum.CHILD_ABUSE]: 'Child abuse',
    [ReportReasonEnum.HARMFUL_DANGEROUS_ACTS]: 'Harmful dangerous acts',
    [ReportReasonEnum.HATEFUL_OR_ABUSIVE_CONTENT]: 'Hateful or abusive content',
    [ReportReasonEnum.INFRINGES_MY_RIGHTS]: 'Infringes my rights',
    [ReportReasonEnum.PROMOTES_TERRORISM]: 'Promotes terrorism',
    [ReportReasonEnum.SEXUAL_CONTENT]: 'Sexual Content',
    [ReportReasonEnum.SPAM_OR_MISLEADING]: 'Spam or misleading',
    [ReportReasonEnum.VIOLENT_OR_REPULSIVE_CONTENT]:
        'Violent or repulsive content',
    Reviews: 'Reviews',
    Services: 'Services',
    'Staff Member': 'Staff Member',
    'View all reviews': 'View all reviews',
    'View all services': 'View all services',
    'View less services': 'View less services',
    [`appointmentStatus.${AppointmentStatusEnum.CANCELLED}` as const]:
        'Cancelled',
    [`appointmentStatus.${AppointmentStatusEnum.COMPLETED}` as const]:
        'Completed',
    [`appointmentStatus.${AppointmentStatusEnum.CONFIRMED}` as const]:
        'Confirmed',
    [`appointmentStatus.${AppointmentStatusEnum.CREATED}` as const]: 'Created',
    [`appointmentStatus.${AppointmentStatusEnum.IN_PROGRESS}` as const]:
        'In progress',
    [`appointmentStatus.${AppointmentStatusEnum.NO_SHOW}` as const]: 'No show',
    [`appointmentStatus.${AppointmentStatusEnum.PENDING}` as const]: 'Pending',
    accountBusinessDetails:
        'This profile suits people who own a business and use the app to schedule their clients.',
    accountBusinessName: 'Business',
    accountClientDetails:
        'This profile is suitable for people who use the app to schedule their appointments.',
    accountClientName: 'Personal',
    activeAppointments: 'Active',
    addAppointment: 'Add appointment',
    addFirstAppointmentOfSubscription: 'Add first appointment of subscription',
    addMeToWaitingList: 'Add me to waiting list',
    addressedFor: 'Addressed for',
    advancedFilters: 'Advanced filters',
    allBusinesses: 'All businesses',
    allBusinessesFilterDescription:
        'This type of business includes all the business available in the Bookr app. Some of these businesses are automatically added from public sources.',

    allCategories: 'All categories',
    anytime: '24/7',
    'app.description':
        'Discover Bookr: Schedule Beauty, Health, and Sports Appointments. Simple and convenient. Book the service you need now and enjoy the experience!',
    'app.keywords': 'bookr mobile app application booking appointment',

    'app.title': 'Bookr - Online Appointments for Beauty, Medical, and Sports',
    applyFilters: 'Apply filters',
    appointmentCanceledSuccessfully: 'Appointment canceled!',
    appointmentDetails: 'Appointment details',
    appointmentTimeRange: ({
        from,
        to,
    }: SingleRequiredProp<'from' | 'to', string>) => `Hour ${from} - ${to}`,
    authDialogCreatePasswordSubtitle:
        'Choose a password to protect your account, you will need it to log ' +
        'in later into your.',
    authDialogCreatePasswordTitle: 'Create Password',
    authDialogLoginSubtitle:
        'The password is required to make sure that you are the owner of the ' +
        'account.',
    authDialogLoginTitle: 'Enter Password',
    authDialogSetupSubtitle:
        'Choose one of the options below that you think is representative of ' +
        'your activity.',
    authDialogSetupTitle: 'What will you use Bookr for?',
    authDialogSignupSubtitle:
        'Fill in your details to complete your Bookr client profile.',
    authDialogSignupTitle: "Let's get started",
    bestCategories: 'Best categories',
    bookByPhone: 'Book by phone',
    bookNowAt: 'Book now at',
    bookNowAtBusiness: ({ businessName }: SingleProp<'businessName'>) =>
        `Book now at ${businessName}`,
    bookedBusinesses: 'Booked businesses',
    'bookingError.Service does not accept online payments':
        'This service does not accept online payments.',
    'bookingError.Slot not available': 'This slot is not available anymore.',
    bookingHasBeenSuccessfullyMade: 'Booking has been successfully made!',
    break: 'Break',
    businessReviewLine: ({
        rate,
        noOfReviews,
        locale,
    }: SingleProp<'rate'> & SingleProp<'noOfReviews'> & SingleProp<'locale'>) =>
        `${num(rate).toFixed(2)} • ${pluralize(noOfReviews, {
            one: 'One review',
            zero: 'No reviews',
            many: `${formatNumber(noOfReviews, locale)} Reviews`,
        })}`,
    businessType: 'Business type',
    call: 'Call',
    cancel: 'Cancel',
    categories: 'categories',
    'categories.barber.name': 'Barber',
    'categories.body_remodeling.name': 'Body Remodeling',
    'categories.coaching.name': 'Coaching',
    'categories.consultancy.name': 'Consultancy',
    'categories.cosmetics.name': 'Cosmetics',
    'categories.coworking.name': 'Coworking',
    'categories.dentistry.name': 'Dentistry',
    'categories.dermatology.name': 'Dermatology',
    'categories.event.name': 'Event',
    'categories.gynecology.name': 'Gynecology',
    'categories.hairstyling.name': 'Hairstyling',
    'categories.implant.name': 'Implant',
    'categories.makeup.name': 'Makeup',
    'categories.manicure.name': 'Manicure',
    'categories.massage.name': 'Massage',
    'categories.mentoring.name': 'Mentoring',
    'categories.nutrition_dietetics.name': 'Nutrition Dietetics',
    'categories.ophthalmology.name': 'Ophthalmology',
    'categories.orl.name': 'Orl',
    'categories.other.name': 'Other',
    'categories.permanent_hair_removal.name': 'Permanent Hair Removal',
    'categories.personal_trainer.name': 'Personal Trainer',
    'categories.photography.name': 'Photography',
    'categories.plastic_surgery.name': 'Plastic Surgery',
    'categories.podiatry.name': 'Podiatry',
    'categories.psychologist.name': 'Psychologist',
    'categories.sport.name': 'Sport',
    'categories.surgery.name': 'Surgery',
    'categories.tattoos.name': 'Tattoos',
    'categories.videography.name': 'Videography',
    changePassword: 'Change password',
    changePasswordMessage: 'Create a new password to secure your account.',
    chooseTime: 'Choose time',
    chooseTimeInstructions:
        'To choose the time of your appointment, you need to choose a date from the calendar above.',
    close: 'Close',
    closed: 'Closed',
    comparePlans: 'Compare plans',
    confirmPassword: 'Confirm password',
    confirmYourPassword: 'Confirm your password',
    connectToBookr: 'Connect to Bookr',
    connectToBookrUsingYourEmail: 'Connect to Bookr using your email address.',
    contactUs: 'Contact us',
    continue: 'Continue',
    continueWithApple: 'Continue with Apple',
    continueWithFacebook: 'Continue with Facebook',
    continueWithGoogle: 'Continue with Google',
    cookieBannerDescription:
        'We use cookies and similar technologies to enhance user experience. ' +
        'For more information, please read our Privacy Policy.',
    couldNotSubscribeToWaitingList: 'Could not subscribe to the waiting list!',
    createAPassword: 'Create a password',
    createASubscription: 'Create a subscription',
    creationDate: 'Creation date',
    dateAndTime: 'Date & Time',
    deleteAccount: 'Delete account',
    deleteAccountDetails: [
        "You'll be logged out of all your devices.",
        'Your profile will be deleted.',
        'All staff members will be eliminated from your business. (no account' +
            ' will be deleted)',
        'All appointments will be canceled.',
    ] as string[],
    deleteAccountMessage:
        'Your account will be permanently deleted and you will not be able to' +
        ' recover it.',
    deleteFilters: 'Delete filters',
    deleteMyAccount: 'Delete my account',
    deleteYourAccount: 'Delete your account',
    deleteYourAccountMessage:
        "Your account will be permanently deleted and you'll not be able to " +
        'recover it. ' +
        'Are you sure you want to delete your account?',
    didYouForgotYourPassword: 'Forgot password?',
    discoverBestBusinessesNearYou: 'Discover the best businesses near you.',
    discoverBestBusinessesNearYouDescription:
        'You can quickly discover what businesses you have around you and not ' +
        'only, and you can make a reservation in just a few moments.',
    displayName: 'First & Last name',
    doYouWantToDeleteThisAppointment: 'Do you want to delete this appointment?',
    edit: 'Edit',
    editAppointment: 'Edit appointment',
    email: 'Email',
    english: 'English',
    englishUk: 'English (UK)',
    englishUs: 'English (US)',
    enterYourEmail: 'Enter your email',
    enterYourPassword: 'Enter your password',
    'errors.notFound': 'Not found page',
    'errors.notFoundMessage': 'The page you are looking for does not exist.',
    'errors.unableToSelectImage': 'We are unable to pick selected image.',
    expectedMinLength: ({
        length,
        name = 'The field',
    }: ExpectedMinLengthProps) =>
        `${name} should have at least ${length} characters.`,
    exploreNewBusinesses: 'Explore new businesses',
    feedback: 'Feedback',
    filters: 'Filters',
    finishSubscription: 'Finish subscription',
    'footer.links.aboutUs': 'About Us',
    'footer.links.beauty': 'Beauty',
    'footer.links.becomeBusiness': 'Become a Business',
    'footer.links.blog': 'Blog',
    'footer.links.bookrBusiness': 'Bookr Business',
    'footer.links.community': 'Community',
    'footer.links.company': 'Company',
    'footer.links.facebook': 'Facebook',
    'footer.links.faq': 'FAQ',
    'footer.links.forBusiness': 'For business',
    'footer.links.forEveryone': 'For everyone',
    'footer.links.hairstyle': 'Hairstyle',
    'footer.links.instagram': 'Instagram',
    'footer.links.linkedin': 'Linkedin',
    'footer.links.massage': 'Massage',
    'footer.links.medical': 'Medical',
    'footer.links.newsletter': 'Newsletter',
    'footer.links.personal': 'Personal',
    'footer.links.pricing': 'Pricing',
    'footer.links.socialMedia': 'Social media',
    'footer.links.twitter': 'Twitter',
    footerCaption: 'Book your style, book your smile, book your lifestyle!',
    forgotPassword: 'Forgot password?',
    forgotPasswordError:
        "Something went wrong, we can't send forgot password email.",
    forgotPasswordSuccess: 'Password reset email sent successfully!',
    friday: 'Friday',
    'geolocation.permission_denied':
        'Permission to get user location was denied.',
    'geolocation.position_unavailable': 'Location information is unavailable.',
    'geolocation.timeout': 'The request to get user location timed out.',
    'geolocation.unknown_error':
        "An unknown error occurred, the location couldn't be fetched.",
    getStarted: 'Get Started',
    gotIt: 'Got it',
    id: 'ID',
    instantBooking: 'Instant Booking',
    instantBusiness: '"Instant Booking" Businesses',
    instantBusinessFilterDescription:
        'These businesses are verified and official partners of Bookr. Only for these businesses you have the opportunity to schedule directly through our application. ',
    invalidEmail: 'Invalid email.',
    joinOnlineMeeting: 'Join online meeting',
    keepMyAccountActive: 'Keep my account active',
    'landings.beauty.accessToClients': (
        <>
            24/7 <br /> access to <br /> your <br /> clients.
        </>
    ),
    'landings.beauty.businessManagement': 'Manage your business',
    'landings.beauty.businessManagementDescription':
        'You have 100% control over your business. Schedule, prices, location, services and staff management, all in one place',
    'landings.beauty.businessesInBeauty': 'Digitalized beauty businesses',
    'landings.beauty.digitalizeYourBusiness': (
        <>
            Digitalize your business with <br /> Bookr
        </>
    ),
    'landings.beauty.feature11Description':
        'A complex dashboard with all the information you need to manage your business.',
    'landings.beauty.feature11Title': 'Dashboard',
    'landings.beauty.feature1Description':
        'Forget about the hassle of appointments over the phone or paper. Your clients can book appointments to your business anytime, anywhere.',
    'landings.beauty.feature1Title': 'Simple and fast bookings',
    'landings.beauty.feature3Description':
        'You get predictability and find solutions to scale your business.',
    'landings.beauty.feature3Title': 'Performance indicators',
    'landings.beauty.feature4Description':
        'You and your clients will receive automatic reminders and notifications, so nobody will forget about their appointments.',
    'landings.beauty.feature4Title': 'Reminders and notifications',
    'landings.beauty.feature5Description':
        'Grow your interaction and visibility rate by 350% by using Bookr.',
    'landings.beauty.feature5Title': 'Visibility and interaction',
    'landings.beauty.feature6Description':
        'Intuitive for you and for your clients. Bookr is easy to use and you can manage your business in a few clicks.',
    'landings.beauty.feature6Title': 'Easy to use',
    'landings.beauty.feature8Description':
        'We are the only company that offers a 3D virtual tour of your business. You and your clients can see the location of your business in 3D.',
    'landings.beauty.feature8Title': '3D Virtual Tour',
    'landings.beauty.heroSubtitle':
        'Discover Bookr, the booking platform that helps you manage your business, your staff and your clients in an easy way.',
    'landings.beauty.heroTitle': 'Beauty appointments, now simpler',
    'landings.beauty.section1Heading':
        'Bookr gives you back over 500 hours that you spend on your appointments',
    'landings.conclusionDescription': (
        <>
            Use Bookr for free to book your appointments at your favorite places{' '}
            <br /> and manage your business in a digital way
        </>
    ),
    'landings.conclusionTitle': (
        <>
            Connect with over 500 businesses <br /> all over the world
        </>
    ),
    'landings.medical.bookrDashboardPatients': (
        <>
            Bookr Dashboard – a new way to <br /> manage patients and
            appointments
        </>
    ),
    'landings.medical.conclusionDescription':
        'Provide your patients with a better experience and a better experience for you. Contact us for more information!',
    'landings.medical.conclusionTitle':
        'Use Bookr Dashboard for your medical business',
    'landings.medical.feature1Description':
        'Improve your relationship with your patients by managing their bookings using our innovative booking dashboard platform.',
    'landings.medical.feature1Title': 'Web access',
    'landings.medical.feature2Description':
        'Improve your relationship with your patients by managing their bookings using our innovative booking dashboard platform.',
    'landings.medical.feature2Title': 'Digital patient records',
    'landings.medical.feature3Description':
        'Schedule your appointments with ease. You can book your patients in a few clicks.',
    'landings.medical.feature3Title': 'Easier bookings',
    'landings.medical.feature4Description':
        'All the medical businesses in the world use Bookr. We provide you with the best medical booking platform for you and for your patients.',
    'landings.medical.feature4Title':
        'Digital medical businesses only with Bookr',
    'landings.medical.feature5Description':
        'You can see the real-time data reports and statistics of your business. You can see statistics for your business and your staff members.',
    'landings.medical.feature5Title': 'Reports and statistics',
    'landings.medical.feature6Description':
        'You and your clients will receive automatic reminders and notifications, so nobody will forget about their appointments.',
    'landings.medical.feature6Title': 'Reminders and notifications',
    'landings.medical.feature7Description':
        'You get predictability and find solutions to scale your business.',
    'landings.medical.feature7Title': 'Performance indicators',
    'landings.medical.feature8Description':
        'Intuitive for you and for your clients. Bookr is easy to use and you can manage your business in a few clicks.',
    'landings.medical.feature8Title': 'Easy to use',
    'landings.medical.heroSubtitle':
        'Discover Bookr, the online booking platform that helps you manage your business, your staff and your patients in an easy way.',
    'landings.medical.heroTitle': 'Medical appointments, now simpler',
    'landings.medical.medicalPlaces': (
        <>
            Medical clinics <br /> Medical centers
        </>
    ),
    'landings.medical.predictability': 'Predictability',
    'landings.medical.predictabilityDescription':
        'Bookr is the only booking platform that allows you to predict your business’s future.',
    'landings.medical.userExperience': 'User Experience',
    'landings.medical.userExperienceDescription':
        'You, your staff members and your patients will love Bookr. Simple. Intuitive. Fast.',
    'landings.pricing.accessToCommunity': 'Access to community',
    'landings.pricing.activityHistory': 'Activity history',
    'landings.pricing.assistance': 'Assistance',
    'landings.pricing.automaticNotifications': 'Automatic notifications',
    'landings.pricing.booking': 'Booking',
    'landings.pricing.businessListing': 'Business listing',
    'landings.pricing.businessManagement': 'Business Management',
    'landings.pricing.clientSupport': 'Client support',
    'landings.pricing.clientsDocuments': 'Clients documents',
    'landings.pricing.clientsManagement': 'Clients Management (CRM)',
    'landings.pricing.customerRecord': 'Customer record',
    'landings.pricing.googleIntegration': 'Google integration',
    'landings.pricing.importClients': 'Import clients',
    'landings.pricing.listClients': 'Clients list',
    'landings.pricing.notifications': 'Notifications',
    'landings.pricing.ownWebsite': 'Own website',
    'landings.pricing.performanceIndicators': 'Performance indicators',
    'landings.pricing.personalisedNotifications': 'Personalised notifications',
    'landings.pricing.reminders': 'Reminders',
    'landings.pricing.salesReports': 'Sales reports',
    'landings.pricing.smsNotifications': 'SMS notifications',
    'landings.pricing.staffMembers': 'Staff members',
    'landings.pricing.teamManagement': 'Team management',
    'landings.pricing.topProfessionals': 'Top professionals',
    'landings.pricing.unlimitedBookings': 'Unlimited bookings',
    'landings.pricing.waitingList': 'Waiting list',
    language: 'Language',
    lastSearches: 'Last searches',
    learnMore: 'Learn more',
    limited: 'Limited',
    listYourBusiness: 'List your Business',
    location: 'Location',
    maximizeList: 'Maximize list',
    maximum: 'Maximum',
    message: 'Message',
    minimum: 'Minimum',
    minutes: ({ count }: SingleProp<'count', number>) => {
        if (count === 1) {
            return 'minute';
        }

        return 'minutes';
    },
    monday: 'Monday',
    monthly: 'Monthly',
    mostAppreciated: 'Most Appreciated',
    myProfile: 'My profile',
    n_others: ({
        count,
        suffix,
    }: SingleProp<'count', number> & SingleProp<'suffix'>) =>
        pluralize(num(count), {
            zero: `no other ${suffix}`.trim(),
            one: `one other ${suffix}`.trim(),
            any: `${num(count)} other ${suffix}`.trim(),
        }),
    nearByBusinesses: 'Nearby businesses',
    nearest: 'Nearest',
    new: 'New',
    newPassword: 'New password',
    newPasswordRequirements: 'Password must be at least 8 characters long.',
    no: 'No',
    noActiveAppointments:
        "You don't have any active appointments. Book one now!",
    noAvailableTimeslotsMessage:
        'There are no available timeslots for this day. Instead, I can put' +
        ' you on the waiting list by pressing the button below.',
    noResults: 'No results',
    nonBookrServicesDescription:
        'This business does not benefit from Bookr services, to make an appointment, call the business by phone.',
    nonBookrServicesTitle: 'Phone booking',
    numberOfAppointments: 'Number of appointments',
    numberOfSessions: ({ count }: SingleProp<'count', number>) =>
        pluralize(count, {
            one: 'subscription (one session)',
            many: `subscription (${count} sessions)`,
        }),
    oldPassword: 'Old password',
    opened: 'Opened',
    or: 'or',
    outOf: ({ current, total }: SingleProp<'current' | 'total'>) =>
        `${num(current)} of ${num(total)}`,
    password: 'Password',
    passwordSuccessfullyChanged: 'Password successfully changed!',
    passwordsDontMatch: "Passwords doesn't match.",
    personalDetails: 'Personal details',
    phoneNumber: 'Phone Number',
    planPrice: ({ price, period }: SingleRequiredProp<'price' | 'period'>) =>
        `${price}/${period}`,
    previousAppointments: 'Previous',
    price: 'Price',
    pricePerAppointment: 'Price per appointment',
    priceRange: 'Price range',
    'pricing.choosePlan': ({ plan }: SingleRequiredProp<'plan'>) =>
        `Choose ${plan}`,
    'pricing.planBenefitsFrom': ({ plan }: SingleRequiredProp<'plan'>) =>
        `Plan benefits from ${plan}`,
    'pricing.plans.custom.description': 'Custom solutions for your business',
    'pricing.plans.custom.features': `
        Custom solutions for your business
    `,
    'pricing.plans.custom.name': 'Custom',
    'pricing.plans.custom.price': 'custom price',
    'pricing.plans.free.description': 'Free',
    'pricing.plans.free.features': `
        One staff member
        Business listing
        Your personal website
        Unlimited appointments
        Google integration
    `,
    'pricing.plans.free.name': 'Free',
    'pricing.plans.professional.description': 'Professional',
    'pricing.plans.professional.features': `
        Sales reports
        Performance indicators
        Top staff members
        Custom notifications
    `,
    'pricing.plans.professional.name': 'Professional',
    'pricing.plans.standard.description': 'Standard',
    'pricing.plans.standard.features': `
        Unlimited staff members
        Team management
        Client history
        Automatic Notifications
        Reminders
    `,
    'pricing.plans.standard.name': 'Standard',
    'pricing.promoText': 'Save 20% with yearly subscription',
    'pricing.subtitle':
        'No surprises, just fair and honest prices. Simply choose a plan with the desired features or compare plans',
    'pricing.title': 'Compare our plans',
    privacyPolicy: 'Privacy policy',
    recommended: 'Recommended',
    recurring: 'Recurring',
    report: 'Report',
    reportThisBusiness: 'Report this business',
    requiredField: 'Required field.',
    results: 'Results',
    retry: 'Retry',
    reviewsDialogDescription:
        'All our reviews are from verified customers who have booked an ' +
        'appointment.',
    romanian: 'Română',
    saturday: 'Saturday',
    saveChanges: 'Save changes',
    search: 'Search',
    searchByCategory: 'Search by category',
    searchByTyping: 'Search by typing...',
    searchByService: 'Search by service...',
    searchResults: ({ totals }: SingleRequiredProp<'totals'>) =>
        `${num(totals)} results based on your search.`,
    security: 'Security',
    seeAllCategories: 'See all categories',
    seeLessCategories: 'See less categories',
    seeMoreOnBusinessPage: 'See more on business page',
    service: 'Service',
    services: 'Services',
    showPassword: 'Show password',
    signOut: 'Sign out',
    smallestPrice: 'Smallest price',
    somethingWentWrong: 'Something went wrong',
    somethingWentWrongDescription:
        'We are sorry, but something went wrong. Please try again later.',
    somethingWentWrongDuringAppointmentCanceling:
        "Something went wrong, appointment couldn't be cancelled, please try again later!",
    somethingWentWrongError: ({ reason }: SingleRequiredProp<'reason'>) =>
        `Something went wrong, ${withTermination(reason, '!')}`,
    sortBy: 'Sort by',
    staffMember: 'Staff member',
    startNow: 'Start now',
    subscribedSuccessfullyToWaitingList:
        'You have been subscribed successfully to the waiting list.',
    subscriptionDetails: 'Subscription details',
    subscriptionLeavingConfirmationMessage:
        'Going back will cancel all appointments. Are you sure you want to continue?',
    sunday: 'Sunday',
    termsAndConditions: 'Terms and conditions',
    theBestBusinesses: 'The best businesses',
    theLatestBusinesses: 'The latest businesses',
    thursday: 'Thursday',
    timeslotsLoadErrorMessage:
        'We could not load available timeslots. Please try again.',
    tooManyRequests: 'Too many requests',
    totalPrice: 'Total price',
    tuesday: 'Tuesday',
    typeYourPreferredLocation: 'Type your preferred location',
    unlimited: 'Unlimited',
    verificationEmailHasBeenSent: ({ email }: SingleProp<'email'>) =>
        `A verification email has been sent, check inbox of your email address ${email}.`,
    viewAll: 'View all',
    viewAppointment: 'View appointment',
    viewLocation: 'View location',
    viewMore: 'View more',
    wednesday: 'Wednesday',
    welcome: 'Welcome',
    wereDoYouNeedIt: 'Where do you need it?',
    whatServiceDoYouNeed: 'What service do you need?',
    why: 'Why',
    wrongPassword: 'Wrong password',
    yearly: 'Yearly',
    yes: 'Yes',
    yesCancel: 'Yes, cancel',
    youDontHaveAnyAppointmentsYet: 'You do not have any appointments yet.',
    youDontHaveAnyAppointmentsYetDetails:
        'Explore the businesses in the Bookr ecosystem and schedule at the best businesses available to you.',
    yourPasswordChangedSuccessfully: 'Your password changed successfully!',
    yourPasswordChangedSuccessfullyDescription:
        'From now on you can login with your new password.',
} as const;
