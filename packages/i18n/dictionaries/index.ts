import english from './english';
import englishUk from './english-uk';
import romanian from './romanian';

export const dictionaries = {
    english,
    englishUk,
    romanian,
};

export const isDictionaryLanguage = (
    locale: string,
): locale is keyof typeof dictionaries =>
    Object.keys(dictionaries).includes(locale);

export const localeMap = {
    en: dictionaries.english,
    'en-gb,en-gb,en-ie': dictionaries.englishUk,
    ro: dictionaries.romanian,
};
