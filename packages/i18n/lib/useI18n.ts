import { useRouter } from 'next/router';
import { useCallback, useMemo } from 'react';
import { useEvent } from '@bookr-technologies/hooks';
import { Dictionary } from '../dictionaries/dictionary';
import { DictionaryKeys } from '../dictionaries/types';
import { getDictionary } from './dictionary';
import { translate } from './translate';

interface TOptions {
    defaultValue?: string;
}

export function useI18n() {
    const { locale, defaultLocale, push, asPath } = useRouter();
    const currentLocale = locale || defaultLocale || 'ro';
    const dictionary = useMemo(
        () => getDictionary(currentLocale),
        [currentLocale],
    );

    const lang = useMemo(() => currentLocale.split('-')[0], [currentLocale]);

    const t = useCallback(
        <K extends DictionaryKeys>(
            key: K,
            props?: (Dictionary[K] extends (props: infer P) => string
                ? P
                : string) &
                TOptions,
        ): Dictionary[K] extends (props: any) => infer T
            ? T
            : Dictionary[K] => {
            if (key in dictionary) {
                return translate(dictionary?.[key] as any, props || {}) as any;
            }

            return (props?.defaultValue ?? key) as any;
        },
        [dictionary],
    );

    const setLocale = useEvent((locale) => push(asPath, asPath, { locale }));

    return {
        t,
        setLocale,
        lang,
        locale: currentLocale,
    };
}
