import { dictionaries } from '../dictionaries';
import { getDictionary } from './dictionary';

describe('i18n#lib/dictionary', () => {
    it.each([
        ['en', dictionaries.english],
        ['en-us', dictionaries.english],
        ['en-US', dictionaries.english],
        ['en-tr', dictionaries.english], // not in dictionary so it should still fall back to the language part
        ['en-gb', dictionaries.englishUk],
        ['en-gb', dictionaries.englishUk],
        ['ro', dictionaries.romanian],
        ['ro-ro', dictionaries.romanian],
        ['ro-RO', dictionaries.romanian],
        ['english', dictionaries.english],
        ['englishUk', dictionaries.englishUk],
        ['romanian', dictionaries.romanian],
    ])('should correctly find dictionary -- %s', (locale, dictionary) => {
        expect(getDictionary(locale)).toEqual(dictionary);
    });
});
