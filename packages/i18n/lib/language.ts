export interface LanguageType {
    name: string;
    code: string;
    flag: string;
}

export const AvailableLanguages: LanguageType[] = [
    { code: 'en-EN', name: 'English (UK)', flag: 'https://flagcdn.com/gb.svg' },
    { code: 'en-US', name: 'English (US)', flag: 'https://flagcdn.com/us.svg' },
    { code: 'ro-RO', name: 'Român<PERSON>', flag: 'https://flagcdn.com/ro.svg' },
];

export const DefaultLanguage = AvailableLanguages[0];

export type SimpleLocales = 'en' | 'ro';
