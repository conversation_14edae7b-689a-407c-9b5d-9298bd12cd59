import { localeMap, dictionaries, isDictionaryLanguage } from '../dictionaries';
import { Dictionary } from '../dictionaries/dictionary';
import english from '../dictionaries/english';

export function getDictionary(dictionaryLocale: string): Dictionary {
    if (isDictionaryLanguage(dictionaryLocale)) {
        return dictionaries[dictionaryLocale] as Dictionary;
    }

    const localeMatch = (locale: string, matchingLocal: string) => {
        const fullMatch = locale === matchingLocal;
        const languageMatch =
            locale.split('-')[0] === matchingLocal.split('-')[0];

        return fullMatch || languageMatch;
    };

    const entry = Object.entries(localeMap).find(([locale]) =>
        locale
            .split(',')
            .map((locale) => locale.trim())
            .find((locale) =>
                localeMatch(
                    locale.toLowerCase(),
                    dictionaryLocale.toLowerCase(),
                ),
            ),
    );

    return entry ? entry[1] : english;
}
