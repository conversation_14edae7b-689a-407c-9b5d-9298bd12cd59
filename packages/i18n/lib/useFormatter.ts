import format from 'date-fns/format';
import { useLocale } from '@bookr-technologies/hooks';

export function useFormatter() {
    const [locale] = useLocale();

    function datetime(
        date: Date,
        pattern: string,
        options?: Omit<Parameters<typeof format>[2], 'locale'>,
    ) {
        return format(date, pattern, {
            locale,
            ...options,
        });
    }

    return {
        datetime,
    };
}
