{"name": "@bookr-technologies/ui", "version": "0.0.0", "main": "./index.ts", "types": "./index.ts", "license": "MIT", "scripts": {"lint": "TIMING=1 eslint \"**/*.ts*\"", "type:check": "yarn tsc --noEmit -p ./tsconfig.json"}, "devDependencies": {"@bookr-technologies/eslint-config-custom": "*", "@bookr-technologies/tsconfig": "*", "@bookr-technologies/types": "*", "@types/react": "^18.2.6", "@types/react-dom": "^18.2.4", "@tanstack/react-query-devtools": "^4.36.1"}, "dependencies": {"@bookr-technologies/firebase": "*", "@bookr-technologies/hooks": "*", "@bookr-technologies/i18n": "*", "@emotion/cache": "^11.11.0", "@emotion/react": "^11.11.0", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.11.16", "@mui/lab": "^5.0.0-alpha.129", "@mui/material": "^5.13.0", "clsx": "^2.0.0", "formik": "^2.2.9", "fuse.js": "^6.6.2", "jotai": "^2.1.0", "libphonenumber-js": "^1.10.30", "notistack": "^3.0.1", "react": "^18.2.0", "react-virtuoso": "^4.3.6", "yup": "^1.1.1"}}