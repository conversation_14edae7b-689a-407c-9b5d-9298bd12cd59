import { createTheme } from '@mui/material/styles';
import { buttonOverrides } from './buttonOverrides';
import { chipOverrides } from './chipOverrides';
import { palette } from './palette';
import { typographyConfiguration } from './typography';

declare module '@mui/material/Button' {
    interface ButtonPropsVariantOverrides {
        subtle: true;
    }
}

declare module '@mui/material/Chip' {
    interface ChipPropsVariantOverrides {
        subtle: true;
    }
}

const baseTheme = createTheme();
const typography = typographyConfiguration();

export const theme = createTheme({
    typography,
    palette: {
        mode: 'light',
        primary: {
            main: palette.foundation.primary,
            contrastText: palette.foundation.white,
        },
        secondary: {
            light: palette.gray.gray100,
            main: palette.gray.gray200,
            dark: palette.gray.gray300,
            contrastText: palette.foundation.primary,
        },
        error: {
            main: palette.foundation.error,
            contrastText: palette.foundation.white,
        },
        success: {
            main: palette.foundation.success,
            contrastText: palette.foundation.white,
        },
        warning: {
            main: palette.foundation.warning,
            contrastText: palette.foundation.white,
        },
        info: {
            main: palette.foundation.accent,
            contrastText: palette.foundation.white,
        },
        text: {
            primary: palette.gray.gray900,
            secondary: palette.gray.gray600,
            disabled: palette.gray.gray500,
        },
    },
    components: {
        MuiButton: buttonOverrides(),
        MuiChip: chipOverrides(),
        MuiAvatar: {
            styleOverrides: {
                root: {
                    fontWeight: 600,
                    fontSize: '1rem',
                },
            },
        },
        MuiOutlinedInput: {
            styleOverrides: {
                root: {
                    borderRadius: 12,
                    backgroundColor: palette.gray.gray100,
                    '&.Mui-focused, &:hover': {
                        '& .MuiOutlinedInput-notchedOutline': {
                            border: `1px solid ${palette.gray.gray500}`,
                        },
                    },
                },
                input: {
                    paddingTop: 24,
                    paddingBottom: 8,
                    paddingLeft: 16,
                    paddingRight: 16,
                    borderRadius: 12,
                    backgroundColor: palette.gray.gray100,
                    fontSize: typography.subhead.fontSize,
                    fontWeight: 600,
                },
                inputAdornedStart: {
                    paddingTop: 28,
                    paddingLeft: 8,
                },
                notchedOutline: {
                    top: 0,
                    borderRadius: 12,
                    border: `1px solid ${palette.gray.gray400}`,
                    '& legend': {
                        display: 'none',
                    },
                },
                sizeSmall: {},
                inputSizeSmall: {
                    paddingTop: 14,
                    paddingRight: 10,
                    paddingBottom: 4,
                    paddingLeft: 10,
                },
            },
        },
        MuiFormHelperText: {
            styleOverrides: {
                root: {
                    fontSize: typography.caption1.fontSize,
                    fontWeight: 600,
                    marginLeft: 14,
                    marginRight: 14,
                },
            },
        },
        MuiInputLabel: {
            styleOverrides: {
                root: {
                    fontSize: typography.subhead.fontSize,
                    fontWeight: 600,
                    transform: 'translate(16px, 16px) scale(1)',
                    pointerEvents: 'none',
                },
                shrink: {
                    transform: 'translate(16px, 8px) scale(0.8)',
                    color: palette.gray.gray600,
                    fontWeight: 600,
                    '&.Mui-focused': {
                        color: palette.gray.gray600,
                    },
                },
                sizeSmall: {
                    transform: 'translate(10px, 9px) scale(1)',
                    '&.MuiInputLabel-shrink': {
                        transform: 'translate(10px, 4px) scale(0.7)',
                    },
                },
            },
        },
        MuiInputBase: {
            styleOverrides: {
                adornedStart: {
                    alignItems: 'flex-end',
                },
            },
        },
        MuiInputAdornment: {
            styleOverrides: {
                root: {
                    height: 'auto',
                    marginBottom: 6,
                    marginRight: 0,
                },
            },
        },
        MuiMenuItem: {
            styleOverrides: {
                root: {
                    fontSize: typography.subhead.fontSize,
                    fontWeight: 600,
                },
            },
        },
        MuiPaper: {
            defaultProps: {
                elevation: 0,
            },
            styleOverrides: {
                rounded: {
                    borderRadius: 12,
                },
            },
        },
        MuiAppBar: {
            styleOverrides: {
                root: {},
                colorPrimary: {
                    backgroundColor: palette.extended.backgroundPrimary,
                    borderColor: palette.extended.borderPrimary,
                    color: palette.foundation.primary,
                },
            },
        },
        MuiCssBaseline: {
            styleOverrides: {
                body: {
                    backgroundColor: palette.extended.backgroundSecondary,
                },
            },
        },
        MuiLinearProgress: {
            styleOverrides: {
                root: {
                    height: 6,
                    borderRadius: 3,
                },
                colorPrimary: {
                    backgroundColor: palette.gray.gray300,
                },
            },
        },
        MuiLink: {
            styleOverrides: {
                root: {
                    cursor: 'pointer',
                },
            },
        },
        MuiContainer: {
            styleOverrides: {
                root: {
                    [baseTheme.breakpoints.up(1920)]: {
                        maxWidth: 1600,
                    },
                    [baseTheme.breakpoints.between(1600, 1920)]: {
                        maxWidth: 1440,
                    },
                },
            },
        },
    },
});
