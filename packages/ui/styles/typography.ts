import { TypographyOptions } from '@mui/material/styles/createTypography';
import { CSSProperties } from 'react';

declare module '@mui/material/styles' {
    interface TypographyVariants {
        largeTitle: CSSProperties;
        title1: CSSProperties;
        title2: CSSProperties;
        title3: CSSProperties;
        body: CSSProperties;
        callout: CSSProperties;
        subhead: CSSProperties;
        footnote: CSSProperties;
        caption1: CSSProperties;
        caption2: CSSProperties;
    }

    interface TypographyVariantsOptions {
        largeTitle: CSSProperties;
        title1: CSSProperties;
        title2: CSSProperties;
        title3: CSSProperties;
        body: CSSProperties;
        callout: CSSProperties;
        subhead: CSSProperties;
        footnote: CSSProperties;
        caption1: CSSProperties;
        caption2: CSSProperties;
    }
}

declare module '@mui/material/Typography' {
    interface TypographyPropsVariantOverrides {
        largeTitle: true;
        title1: true;
        title2: true;
        title3: true;
        body: true;
        callout: true;
        subhead: true;
        footnote: true;
        caption1: true;
        caption2: true;
    }
}

function buildFont(size: number) {
    return {
        fontSize: size,
        lineHeight: `${size * 1.25}px`,
    };
}

export function typographyConfiguration(): TypographyOptions {
    return {
        fontFamily: '"Plus Jakarta Sans", sans-serif',
        fontWeightLight: 400,
        fontWeightRegular: 500,
        fontWeightMedium: 600,
        fontWeightBold: 800,
        largeTitle: {
            ...buildFont(34),
        },
        title1: {
            ...buildFont(28),
            letterSpacing: '-0.005em',
        },
        title2: {
            ...buildFont(22),
        },
        title3: {
            ...buildFont(20),
        },
        body: {
            ...buildFont(16),
        },
        callout: {
            ...buildFont(16),
        },
        subhead: {
            ...buildFont(15),
        },
        footnote: {
            ...buildFont(14),
        },
        caption1: {
            ...buildFont(12),
        },
        caption2: {
            ...buildFont(11),
        },
    };
}
