import { OverrideComponent } from './components';
import { palette } from './palette';

export function chipOverrides(): OverrideComponent<'MuiChip'> {
    const subtleDefaultStyle = {
        backgroundColor: palette.gray.gray400,
        color: palette.gray.gray900,
        '&:hover': {
            backgroundColor: palette.gray.gray300,
        },
        '&:disabled': {
            backgroundColor: palette.gray.gray100,
        },
    };

    return {
        styleOverrides: {
            root: ({ theme }) => ({
                fontWeight: 600,
                fontSize: '12px',
                lineHeight: '15px',
                height: 'auto',
                borderRadius: theme.spacing(1),
                padding: theme.spacing(0.75, 1),
            }),
            icon: ({ theme }) => ({
                marginRight: theme.spacing(0.5),
            }),
            label: {
                padding: 0,
            },
        },
        variants: [
            {
                props: { variant: 'subtle' },
                style: subtleDefaultStyle,
            },
            {
                props: { variant: 'subtle', color: 'primary' },
                style: subtleDefaultStyle,
            },
            {
                props: { variant: 'subtle', color: 'secondary' },
                style: {
                    backgroundColor: palette.gray.gray600,
                    color: palette.foundation.primary,
                    '&:hover': {
                        backgroundColor: palette.gray.gray500,
                    },
                    '&:disabled': {
                        backgroundColor: palette.gray.gray700,
                    },
                },
            },
            {
                props: { variant: 'subtle', color: 'error' },
                style: {
                    backgroundColor: palette.error.error100,
                    color: palette.foundation.error,
                    '&:hover': {
                        backgroundColor: palette.error.error200,
                    },
                    '&:disabled': {
                        backgroundColor: palette.gray.gray200,
                    },
                },
            },
            {
                props: { variant: 'subtle', color: 'info' },
                style: {
                    backgroundColor: palette.accent.accent100,
                    color: palette.foundation.accent,
                    '&:hover': {
                        backgroundColor: palette.accent.accent200,
                    },
                    '&:disabled': {
                        backgroundColor: palette.gray.gray200,
                    },
                },
            },
            {
                props: { variant: 'subtle', color: 'success' },
                style: () => ({
                    backgroundColor: palette.success.success200,
                    color: palette.success.success900,
                }),
            },
            {
                props: { variant: 'subtle', color: 'warning' },
                style: () => ({
                    backgroundColor: palette.warning.warning200,
                    color: '#eb9c00',
                }),
            },
        ],
    };
}
