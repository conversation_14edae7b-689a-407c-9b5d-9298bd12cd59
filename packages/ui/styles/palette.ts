export const foundation = {
    primary: 'rgb(17, 17, 17)',
    secondary: 'rgb(255, 255, 255)',
    accent: 'rgb(47, 128, 251)',
    error: 'rgb(240, 82, 83)',
    warning: 'rgb(255, 192, 67)',
    success: 'rgb(5, 148, 79)',
    white: 'rgb(255, 255, 255)',
};

export const gray = {
    gray100: 'rgb(246, 246, 246)',
    gray200: 'rgb(238, 238, 238)',
    gray300: 'rgb(226, 226, 226)',
    gray400: 'rgb(203, 203, 203)',
    gray500: 'rgb(175, 175, 175)',
    gray600: 'rgb(117, 117, 117)',
    gray700: 'rgb(84, 84, 84)',
    gray800: 'rgb(51, 51, 51)',
    gray900: 'rgb(31, 31, 31)',
};

export const accent = {
    accent900: 'rgba(47, 128, 251, 0.9)',
    accent800: 'rgba(47, 128, 251, 0.8)',
    accent700: 'rgba(47, 128, 251, 0.7)',
    accent600: 'rgba(47, 128, 251, 0.6)',
    accent500: 'rgba(47, 128, 251, 0.5)',
    accent400: 'rgba(47, 128, 251, 0.4)',
    accent300: 'rgba(47, 128, 251, 0.3)',
    accent200: 'rgba(47, 128, 251, 0.2)',
    accent100: 'rgba(47, 128, 251, 0.1)',
};
export const success = {
    success900: 'rgba(5, 148, 79, 0.9)',
    success800: 'rgba(5, 148, 79, 0.8)',
    success700: 'rgba(5, 148, 79, 0.7)',
    success600: 'rgba(5, 148, 79, 0.6)',
    success500: 'rgba(5, 148, 79, 0.5)',
    success400: 'rgba(5, 148, 79, 0.4)',
    success300: 'rgba(5, 148, 79, 0.3)',
    success200: 'rgba(5, 148, 79, 0.2)',
    success100: 'rgba(5, 148, 79, 0.1)',
};
export const warning = {
    warning900: 'rgba(255, 192, 67, 0.9)',
    warning800: 'rgba(255, 192, 67, 0.8)',
    warning700: 'rgba(255, 192, 67, 0.7)',
    warning600: 'rgba(255, 192, 67, 0.6)',
    warning500: 'rgba(255, 192, 67, 0.5)',
    warning400: 'rgba(255, 192, 67, 0.4)',
    warning300: 'rgba(255, 192, 67, 0.3)',
    warning200: 'rgba(255, 192, 67, 0.2)',
    warning100: 'rgba(255, 192, 67, 0.1)',
};
export const error = {
    error900: 'rgba(240, 82, 83, 0.9)',
    error800: 'rgba(240, 82, 83, 0.8)',
    error700: 'rgba(240, 82, 83, 0.7)',
    error600: 'rgba(240, 82, 83, 0.6)',
    error500: 'rgba(240, 82, 83, 0.5)',
    error400: 'rgba(240, 82, 83, 0.4)',
    error300: 'rgba(240, 82, 83, 0.3)',
    error200: 'rgba(240, 82, 83, 0.2)',
    error100: 'rgba(240, 82, 83, 0.1)',
};

export const extended = {
    backgroundPrimary: 'rgb(255, 255, 255)',
    backgroundSecondary: gray.gray100,
    backgroundTertiary: gray.gray200,
    contentTertiary: gray.gray600,
    contentSecondary: gray.gray700,
    contentPrimary: gray.gray900,
    borderPrimary: gray.gray400,
    borderSecondary: gray.gray600,
    borderDisabled: gray.gray200,
};

export const palette = {
    foundation,
    gray,
    accent,
    success,
    warning,
    error,
    extended,
} as const;
