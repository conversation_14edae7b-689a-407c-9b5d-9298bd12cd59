import createCache from '@emotion/cache';

const isBrowser = typeof window !== 'undefined';

export function createEmotionCache() {
    if (isBrowser) {
        const insertionPoint =
            document.querySelector<HTMLElement>(
                'meta[name="emotion-insertion-point"]',
            ) || undefined;
        return createCache({ key: 'mui-style', insertionPoint });
    }

    return createCache({ key: 'mui-style' });
}
