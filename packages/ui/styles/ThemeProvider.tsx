import CssBaseline from '@mui/material/CssBaseline';
import { ThemeProvider as MuiThemeProvider } from '@mui/material/styles';
import React, { ReactNode } from 'react';
import { theme } from './theme';

interface Props {
    children: ReactNode;
}

export function ThemeProvider({ children }: Props) {
    return (
        <MuiThemeProvider theme={theme}>
            <CssBaseline />
            {children}
        </MuiThemeProvider>
    );
}
