import { OverrideComponent } from './components';
import { palette } from './palette';

export function buttonOverrides(): OverrideComponent<'MuiButton'> {
    const subtleDefaultStyle = {
        backgroundColor: palette.gray.gray200,
        color: palette.foundation.primary,
        '&:hover': {
            backgroundColor: palette.gray.gray300,
        },
        '&:disabled': {
            backgroundColor: palette.gray.gray100,
        },
    };

    return {
        variants: [
            {
                props: { variant: 'subtle' },
                style: subtleDefaultStyle,
            },
            {
                props: { variant: 'subtle', color: 'inherit' },
                style: subtleDefaultStyle,
            },
            {
                props: { variant: 'subtle', color: 'primary' },
                style: subtleDefaultStyle,
            },
            {
                props: { variant: 'subtle', color: 'secondary' },
                style: {
                    backgroundColor: palette.gray.gray600,
                    color: palette.foundation.primary,
                    '&:hover': {
                        backgroundColor: palette.gray.gray500,
                    },
                    '&:disabled': {
                        backgroundColor: palette.gray.gray700,
                    },
                },
            },
            {
                props: { variant: 'subtle', color: 'error' },
                style: {
                    backgroundColor: palette.error.error100,
                    color: palette.foundation.error,
                    '&:hover': {
                        backgroundColor: palette.error.error200,
                    },
                    '&:disabled': {
                        backgroundColor: palette.gray.gray200,
                    },
                },
            },
            {
                props: { variant: 'subtle', color: 'info' },
                style: {
                    backgroundColor: palette.accent.accent100,
                    color: palette.foundation.accent,
                    '&:hover': {
                        backgroundColor: palette.accent.accent200,
                    },
                    '&:disabled': {
                        backgroundColor: palette.gray.gray200,
                    },
                },
            },
        ],
        defaultProps: {
            disableElevation: true,
        },
        styleOverrides: {
            root: {
                borderRadius: 10,
                textTransform: 'none',
                fontSize: 14,
                fontWeight: 600,
            },
            sizeLarge: {
                borderRadius: 16,
                fontSize: 16,
                minHeight: 56,
            },
            containedInfo: {
                backgroundColor: palette.foundation.accent,
                color: palette.foundation.white,
                '&:hover': {
                    backgroundColor: palette.accent.accent900,
                },
                '&:disabled': {
                    backgroundColor: palette.gray.gray100,
                },
            },
        },
    };
}
