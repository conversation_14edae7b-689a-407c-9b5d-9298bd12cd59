import Dialog from '@mui/material/Dialog';
import IconButton from '@mui/material/IconButton';
import { styled } from '@mui/material/styles';

export const StyledCallingCodeSelector = styled(IconButton, {
    name: 'CallingCodeSelector',
})(() => ({
    padding: 0,
}));

export const StyledImgFlag = styled('img', { name: 'CallingCodeSelectorFlag' })(
    ({ theme }) => ({
        height: 24,
        width: 24,
        borderRadius: 12,
        objectFit: 'cover',
        boxShadow: theme.shadows[1],
    }),
);

export const StyledDialog = styled(Dialog, { name: 'CallingCodeDialog' })(
    () => ({
        '.MuiDialog-paper': {
            height: '100%',
        },
        '.MuiListItemIcon-root': {
            minWidth: 38,
        },
        '.MuiListItemText-root': {
            margin: 0,
        },
        '.MuiList-root': {
            overflow: 'auto',
            maxHeight: '100%',
            width: '100%',
        },
        '.MuiListItemButton-root': {
            borderRadius: 12,
        },
        '.StyledDialog-listHolder': {
            maxHeight: '100%',
        },
    }),
);
