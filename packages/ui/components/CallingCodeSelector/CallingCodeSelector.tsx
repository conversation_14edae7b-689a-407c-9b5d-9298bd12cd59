import CloseIcon from '@mui/icons-material/Close';
import LanguageIcon from '@mui/icons-material/Language';
import SearchIcon from '@mui/icons-material/Search';
import Box from '@mui/material/Box';
import CircularProgress from '@mui/material/CircularProgress';
import Divider from '@mui/material/Divider';
import Grid from '@mui/material/Grid';
import IconButton from '@mui/material/IconButton';
import InputAdornment from '@mui/material/InputAdornment';
import List from '@mui/material/List';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import Stack from '@mui/material/Stack';
import TextField from '@mui/material/TextField';
import { Theme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import Fuse from 'fuse.js';
import { ChangeEvent, useMemo, useRef, useState, useTransition } from 'react';
import { useDialog, useEvent } from '@bookr-technologies/hooks';
import { useI18n } from '@bookr-technologies/i18n';
import countries from '../../assets/countries.json';
import {
    StyledCallingCodeSelector,
    StyledDialog,
    StyledImgFlag,
} from './styles';
import { CallingCodeSelectorProps } from './types';

export function CallingCodeSelector({
    value,
    onChange,
}: CallingCodeSelectorProps) {
    const searchInputRef = useRef<HTMLInputElement>();
    const [filteredCountries, setFilteredCountries] =
        useState<typeof countries>(countries);
    const [isTransitioning, startTransition] = useTransition();
    const dialog = useDialog();
    const { t } = useI18n();
    const isFullscreen = useMediaQuery((theme: Theme) =>
        theme.breakpoints.down('sm'),
    );

    const fuseInstance = useMemo(
        () =>
            new Fuse(countries, {
                minMatchCharLength: 2,
                includeScore: true,
                includeMatches: true,
                keys: ['name', 'callingCodes', 'cca2', 'cca3'],
            }),
        [],
    );

    const items$ = useMemo(
        () =>
            filteredCountries.reduce((acc, country) => {
                const { callingCodes, id, flag, name } = country;

                callingCodes.forEach((code) =>
                    acc.push(
                        <ListItemButton
                            key={`${id}-${code}`}
                            selected={value?.callingCode === code}
                            onClick={() => {
                                onChange?.({ ...country, callingCode: code });
                                dialog.close();
                                setFilteredCountries(countries);
                            }}
                        >
                            <ListItemIcon>
                                <StyledImgFlag
                                    src={flag}
                                    alt={name}
                                    loading={'lazy'}
                                />
                            </ListItemIcon>
                            <ListItemText
                                primary={code}
                                secondary={name}
                                primaryTypographyProps={{
                                    variant: 'subhead',
                                    fontWeight: 800,
                                }}
                            />
                        </ListItemButton>,
                    ),
                );

                return acc;
            }, [] as JSX.Element[]),
        [filteredCountries, onChange, dialog, value?.callingCode],
    );

    const handleSearch = useEvent((event: ChangeEvent<HTMLInputElement>) =>
        startTransition(() => {
            const query = event.target.value;

            setFilteredCountries(() => {
                if (!query || query.length < 2) {
                    return countries;
                }
                return fuseInstance
                    .search(query, { limit: 24 })
                    .map(({ item }) => item);
            });
        }),
    );

    const handleOpen = useEvent(() => {
        dialog.open();
        setTimeout(() => searchInputRef.current?.focus(), 100);
    });

    return (
        <>
            <StyledCallingCodeSelector onClick={handleOpen} size={'small'}>
                {value?.flag ? (
                    <StyledImgFlag
                        src={value.flag}
                        alt={value.name}
                        loading={'lazy'}
                    />
                ) : (
                    <LanguageIcon />
                )}
            </StyledCallingCodeSelector>

            <StyledDialog
                fullWidth
                fullScreen={isFullscreen}
                maxWidth={'xs'}
                {...dialog.props}
            >
                <Stack p={2} gap={2} maxHeight={'100%'}>
                    <Grid container flexWrap={'nowrap'} gap={2}>
                        <TextField
                            InputProps={{
                                startAdornment: (
                                    <InputAdornment position="start">
                                        {isTransitioning ? (
                                            <Box
                                                display={'flex'}
                                                alignItems={'center'}
                                                width={24}
                                                p={0.25}
                                            >
                                                <CircularProgress size={20} />
                                            </Box>
                                        ) : (
                                            <SearchIcon />
                                        )}
                                    </InputAdornment>
                                ),
                            }}
                            onChange={handleSearch}
                            label={t('searchByTyping')}
                            inputRef={searchInputRef}
                            fullWidth
                        />
                        {isFullscreen ? (
                            <IconButton onClick={dialog.close}>
                                <CloseIcon />
                            </IconButton>
                        ) : null}
                    </Grid>
                    <Divider />
                    <Grid
                        container
                        flexGrow={1}
                        className={'StyledDialog-listHolder'}
                    >
                        <List dense disablePadding>
                            {items$}
                        </List>
                    </Grid>
                </Stack>
            </StyledDialog>
        </>
    );
}
