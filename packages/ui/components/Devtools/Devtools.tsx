import dynamic from 'next/dynamic';
import { getRuntimeConfig } from '@bookr-technologies/config';

const ReactQueryDevtools = dynamic(
    async () => {
        const { ReactQueryDevtools } = await import(
            '@tanstack/react-query-devtools'
        );
        return { default: ReactQueryDevtools };
    },
    { ssr: false },
);

export function Devtools() {
    const { production } = getRuntimeConfig();

    return (
        <>
            {!production ? (
                <ReactQueryDevtools
                    toggleButtonProps={{
                        style: {
                            opacity: 0.3,
                        },
                    }}
                />
            ) : null}
        </>
    );
}
