import { clsx } from 'clsx';
import { SVGProps } from 'react';

interface LogoProps extends SVGProps<SVGSVGElement> {
    icon?: boolean;
    color?: string;
    textColor?: string;
    iconColor?: string;
}

export function Logo({
    icon,
    color,
    textColor = '#111',
    iconColor = '#2F80FB',
    ...rest
}: LogoProps) {
    const width = icon ? 28 : 126;
    rest.width = rest.width || width;
    rest.className = clsx('Logo-root', rest.className);

    return (
        <svg
            height="30"
            viewBox={`0 0 ${width} 30`}
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...rest}
        >
            <path
                d="M97.1384 1.63867H101.702L101.693 16.7673L108.869 9.2049H114.548L107.546 16.874L114.686 28.2539H109.461L104.131 19.7805L101.693 22.441V28.2539H97.1289L97.1384 1.64058V1.63867Z"
                fill={color ?? textColor}
            />
            <path
                d="M116.398 9.20281H120.684V11.8996C121.171 10.8018 121.855 10.0433 122.739 9.624C123.621 9.20471 124.642 8.99316 125.805 8.99316H126.92V13.0545H125.281C124.004 13.0545 122.964 13.4567 122.164 14.2628C121.362 15.069 120.962 16.1954 120.962 17.6419V28.2518H116.398V9.20281Z"
                fill={color ?? textColor}
            />
            <path
                d="M85.726 28.2504C80.3848 28.2504 76.0391 23.8821 76.0391 18.5134C76.0391 13.1446 80.3848 8.77637 85.726 8.77637C91.0671 8.77637 95.4148 13.1446 95.4148 18.5134C95.4148 23.8821 91.069 28.2504 85.726 28.2504ZM85.726 12.8911C82.6411 12.8911 80.1326 15.4125 80.1326 18.5134C80.1326 21.6142 82.6411 24.1356 85.726 24.1356C88.8108 24.1356 91.3193 21.6142 91.3193 18.5134C91.3193 15.4125 88.8089 12.8911 85.726 12.8911Z"
                fill={color ?? textColor}
            />
            <path
                d="M64.64 28.2504C59.2989 28.2504 54.9531 23.8821 54.9531 18.5134C54.9531 13.1446 59.2989 8.77637 64.64 8.77637C69.9812 8.77637 74.3288 13.1446 74.3288 18.5134C74.3288 23.8821 69.9831 28.2504 64.64 28.2504ZM64.64 12.8911C61.5552 12.8911 59.0467 15.4125 59.0467 18.5134C59.0467 21.6142 61.5552 24.1356 64.64 24.1356C67.7249 24.1356 70.2334 21.6142 70.2334 18.5134C70.2334 15.4125 67.723 12.8911 64.64 12.8911Z"
                fill={color ?? textColor}
            />
            <path
                d="M43.5482 8.77037C41.4644 8.77037 39.5361 9.4317 37.9529 10.5676V1.63867H33.8594V28.2501H37.9529V26.4529C39.5361 27.5887 41.4644 28.2501 43.5482 28.2501C48.8931 28.2501 53.237 23.8838 53.237 18.5112C53.237 13.1386 48.8931 8.77227 43.5482 8.77227V8.77037ZM43.5482 24.1334C40.4633 24.1334 37.9529 21.6101 37.9529 18.5093C37.9529 15.4084 40.4633 12.8851 43.5482 12.8851C46.633 12.8851 49.1434 15.4084 49.1434 18.5093C49.1434 21.6101 46.633 24.1334 43.5482 24.1334Z"
                fill={color ?? textColor}
            />
            <path
                d="M25.0718 17.835L22.516 15.1553C24.467 13.5982 25.7222 11.1912 25.7222 8.49628C25.7222 3.8117 21.9301 0 17.2696 0H0.921875V30H19.8975C23.8451 30 27.057 26.7734 27.057 22.8054C27.057 20.8805 26.3005 19.1271 25.0718 17.835ZM5.66199 4.76463H17.2696C19.3154 4.76463 20.9821 6.43796 20.9821 8.49628C20.9821 9.84372 20.2672 11.0273 19.2017 11.6829L18.6063 11.0577L18.5817 11.033L18.2745 10.709L18.2574 10.6918C17.1539 9.62645 15.6542 8.97084 14.0065 8.97084C13.8662 8.97084 13.7259 8.97656 13.5875 8.98418C12.0043 9.09281 10.5841 9.8056 9.55458 10.8957L9.255 11.1969L5.66389 14.839V4.76463H5.66199ZM19.8975 25.2354H5.66199V21.6066L8.79995 18.4239L12.0232 15.1572L12.9618 14.2043C13.2177 13.9165 13.5913 13.7355 14.0065 13.7355C14.3554 13.7355 14.6739 13.8632 14.9204 14.0785L14.9242 14.0823L15.1043 14.271L16.9776 16.2359H16.9795L17.6924 16.983L19.1543 18.5153L21.6077 21.0863L21.6514 21.1321C22.0647 21.5685 22.3169 22.1593 22.3169 22.8054C22.3169 24.1452 21.2304 25.2354 19.8975 25.2354Z"
                fill={color ?? iconColor}
            />
        </svg>
    );
}
