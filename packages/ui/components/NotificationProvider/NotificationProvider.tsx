import { SnackbarProvider } from 'notistack';
import { PropsWithChildren } from 'react';
import { StyledMaterialDesignContent } from './styles';

export function NotificationProvider({ children }: PropsWithChildren) {
    return (
        <SnackbarProvider
            dense
            preventDuplicate
            autoHideDuration={3000}
            maxSnack={4}
            anchorOrigin={{ horizontal: 'center', vertical: 'bottom' }}
            Components={{
                warning: StyledMaterialDesignContent,
                error: StyledMaterialDesignContent,
                info: StyledMaterialDesignContent,
                success: StyledMaterialDesignContent,
                default: StyledMaterialDesignContent,
            }}
        >
            {children}
        </SnackbarProvider>
    );
}
