import { alpha, styled } from '@mui/material/styles';
import { MaterialDesignContent } from 'notistack';
import { palette } from '../../styles';

export const StyledMaterialDesignContent = styled(MaterialDesignContent)(
    ({ theme }) => ({
        '.notistack-Snackbar': {
            minWidth: 0,
        },
        '&.notistack-MuiContent': {
            backgroundColor: alpha(palette.extended.backgroundPrimary, 0.8),
            backdropFilter: 'blur(10px) saturate(240%)',
            color: palette.extended.contentSecondary,
            boxShadow: '0px 11px 20px rgba(0, 0, 0, 0.05)',
            borderRadius: 12,
            padding: theme.spacing(1, 2),
            display: 'flex',
            alignItems: 'center',
            flexDirection: 'row',
            flexWrap: 'nowrap',
            '& > div': {
                padding: 0,
                fontWeight: 600,
                fontSize: 12,
                lineHeight: '15px',
            },
        },

        '&.notistack-MuiContent-info': {
            '& > div > svg': {
                color: palette.accent.accent900,
            },
        },

        '&.notistack-MuiContent-warning': {
            '& > div > svg': {
                color: palette.warning.warning900,
            },
        },

        '&.notistack-MuiContent-error': {
            '& > div > svg': {
                color: palette.error.error900,
            },
        },

        '&.notistack-MuiContent-success': {
            '& > div > svg': {
                color: palette.success.success900,
            },
        },
    }),
);
