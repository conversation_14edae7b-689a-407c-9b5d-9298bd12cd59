import InputAdornment from '@mui/material/InputAdornment';
import TextField from '@mui/material/TextField';
import { useFormikContext } from 'formik';
import PhoneNumber, { AsYouType } from 'libphonenumber-js';
import { useEffect, useId, useState } from 'react';
import { useEvent } from '@bookr-technologies/hooks';
import countries from '../../../assets/countries.json';
import { CallingCodeSelector, Country } from '../../CallingCodeSelector';
import { FormikPhoneNumberProps } from './types';

export function FormikPhoneNumber({
    onChange,
    onBlur,
    ...rest
}: FormikPhoneNumberProps) {
    const [callingCode, setCallingCode] = useState<Country>();
    const id = useId();
    const formik = useFormikContext();
    const meta = formik.getFieldMeta(rest.name);
    const helpers = formik.getFieldHelpers(rest.name);

    const parsePhoneNumber = useEvent((value: string) => {
        if (!value) {
            return '';
        }

        const opts = {
            defaultCallingCode: callingCode?.callingCode?.replace(/^\+/, ''),
        };
        const phoneNumber = PhoneNumber(value, opts);

        return (
            phoneNumber?.formatInternational() ??
            new AsYouType(opts).input(value)
        );
    });

    const handleCallingCodeDetection = useEvent((value) => {
        const phoneNumber = PhoneNumber(value);
        if (phoneNumber) {
            const callingCode = `+${phoneNumber.countryCallingCode}`;
            const country = countries.find(({ callingCodes }) =>
                callingCodes.includes(callingCode),
            );

            if (country) {
                setCallingCode({
                    ...country,
                    callingCode,
                });
            }
        }
    });

    const handleChange = useEvent((event: any) => {
        event.target.value = parsePhoneNumber(event.target.value);
        if (
            !callingCode ||
            !event.target.value.startsWith(callingCode.callingCode)
        ) {
            handleCallingCodeDetection(event.target.value);
        }

        formik.handleChange(event);
        if (onChange) {
            onChange(event);
        }
    });

    const handleBlur = useEvent((event: any) => {
        event.target.value = parsePhoneNumber(event.target.value);

        formik.handleBlur(event);
        if (onBlur) {
            onBlur(event);
        }
    });

    const handleCallingCode = useEvent((country: Country) => {
        const phoneNumber = PhoneNumber(String(meta.value ?? ''), {
            defaultCallingCode: callingCode?.callingCode?.replace(/^\+/, ''),
        });
        const value = parsePhoneNumber(
            `${country.callingCode}${phoneNumber?.nationalNumber ?? ''}`,
        );

        setCallingCode(country);
        helpers.setValue(value);
    });

    useEffect(() => {
        if (meta.value) {
            handleCallingCodeDetection(meta.value);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return (
        <TextField
            id={`${id}-field-${rest.name}`}
            onChange={handleChange}
            onBlur={handleBlur}
            value={meta.value}
            error={meta.touched && !!meta.error}
            helperText={meta.touched ? meta.error : undefined}
            InputProps={{
                startAdornment: (
                    <InputAdornment position="start">
                        <CallingCodeSelector
                            value={callingCode}
                            onChange={handleCallingCode}
                        />
                    </InputAdornment>
                ),
            }}
            {...rest}
        />
    );
}
