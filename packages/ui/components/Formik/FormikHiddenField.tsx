import { useFormikContext } from 'formik';
import { useEffect } from 'react';

interface Props {
    name: string;
    value: any;
}

export function FormikHiddenField({ name, value }: Props) {
    const formik = useFormikContext();

    useEffect(() => {
        formik.setFieldValue(name, value);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [name, value]);

    return null;
}
