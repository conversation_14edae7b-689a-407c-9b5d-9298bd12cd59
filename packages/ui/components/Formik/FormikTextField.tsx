import TextField, { TextFieldProps } from '@mui/material/TextField';
import { useFormikContext } from 'formik';
import { useId } from 'react';
import { useEvent } from '@bookr-technologies/hooks';

export interface FormikTextFieldProps
    extends Omit<TextFieldProps, 'name' | 'value'> {
    name: string;
    readOnly?: boolean;
}

export function FormikTextField({
    onChange,
    onBlur,
    readOnly,
    ...rest
}: FormikTextFieldProps) {
    const formik = useFormikContext();
    const id = useId();

    const meta = formik.getFieldMeta(rest.name);

    const handleChange = useEvent((event: any) => {
        formik.handleChange(event);
        if (onChange) {
            onChange(event);
        }
    });

    const handleBlur = useEvent((event: any) => {
        formik.handleBlur(event);
        if (onBlur) {
            onBlur(event);
        }
    });

    if (readOnly) {
        rest.InputProps = {
            ...rest.InputProps,
            readOnly,
        };
    }

    return (
        <TextField
            id={`${id}-field-${rest.name}`}
            onChange={handleChange}
            onBlur={handleBlur}
            value={meta.value}
            error={meta.touched && !!meta.error}
            helperText={meta.touched ? meta.error : undefined}
            {...rest}
        />
    );
}
