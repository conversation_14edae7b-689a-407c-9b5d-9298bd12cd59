import Avatar from '@mui/material/Avatar';
import FormControl from '@mui/material/FormControl';
import FormHelperText from '@mui/material/FormHelperText';
import Grid from '@mui/material/Grid';
import InputLabel from '@mui/material/InputLabel';
import MenuItem from '@mui/material/MenuItem';
import Select, { SelectProps } from '@mui/material/Select';
import Typography from '@mui/material/Typography';
import { useFormikContext } from 'formik';
import { useId } from 'react';
import { useEvent } from '@bookr-technologies/hooks';
import { AvailableLanguages } from '@bookr-technologies/i18n';

export interface FormikLanguageProps
    extends Omit<SelectProps, 'name' | 'value' | 'children'> {
    name: string;
}

export function FormikLanguage({
    onChange,
    label,
    ...rest
}: FormikLanguageProps) {
    const id = useId();
    const formik = useFormikContext();
    const meta = formik.getFieldMeta(rest.name);
    const hasError = meta.touched && !!meta.error;

    const handleChange = useEvent((event: any, node: any) => {
        formik.handleChange(event);
        if (onChange) {
            onChange(event, node);
        }
    });

    return (
        <FormControl error={hasError}>
            <InputLabel id={`${id}-field-${rest.name}-label`}>
                {label}
            </InputLabel>
            <Select
                id={`${id}-field-${rest.name}`}
                onChange={handleChange}
                value={meta.value}
                error={hasError}
                sx={{
                    '.MuiSelect-select > .MuiGrid-root': {
                        marginTop: 0.5,
                    },
                }}
                {...rest}
            >
                {AvailableLanguages.map((language) => (
                    <MenuItem value={language.code} key={language.code}>
                        <Grid container alignItems={'center'} gap={1}>
                            <Avatar
                                src={language.flag}
                                sx={{ width: 24, height: 24 }}
                            />
                            <Typography variant={'subhead'} fontWeight={600}>
                                {language.name}
                            </Typography>
                        </Grid>
                    </MenuItem>
                ))}
            </Select>
            {hasError && <FormHelperText>{meta.error}</FormHelperText>}
        </FormControl>
    );
}
