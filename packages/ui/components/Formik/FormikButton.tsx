import LoadingButton, { LoadingButtonProps } from '@mui/lab/LoadingButton';
import { useFormikContext } from 'formik';
import { MouseEvent } from 'react';
import { useEvent } from '@bookr-technologies/hooks';

interface FormikButtonProps extends LoadingButtonProps {
    validateDirty?: boolean;
}

export function FormikButton({
    onClick,
    loading,
    disabled,
    type,
    validateDirty,
    ...rest
}: FormikButtonProps) {
    const { isSubmitting, isValid, submitForm, dirty } = useFormikContext();

    const handleClick = useEvent(
        async (event: MouseEvent<HTMLButtonElement>) => {
            if (onClick) {
                onClick(event);
            }

            if (type !== 'submit') {
                await submitForm();
            }
        },
    );

    return (
        <LoadingButton
            onClick={handleClick}
            loading={loading || isSubmitting}
            type={type}
            disabled={
                disabled ||
                isSubmitting ||
                !isValid ||
                (validateDirty && !dirty)
            }
            {...rest}
        />
    );
}
