import PhotoCameraIcon from '@mui/icons-material/PhotoCamera';
import Avatar, { AvatarProps } from '@mui/material/Avatar';
import Badge from '@mui/material/Badge';
import CircularProgress from '@mui/material/CircularProgress';
import IconButton from '@mui/material/IconButton';
import { styled } from '@mui/material/styles';
import { useEffect, useMemo, useState } from 'react';
import { ErrorCode } from '@bookr-technologies/core';
import {
    useEvent,
    useLoading,
    useNotification,
    usePicker,
} from '@bookr-technologies/hooks';
import { useI18n } from '@bookr-technologies/i18n';
import { palette } from '../styles';

interface AvatarUploaderProps extends Omit<AvatarProps, 'onChange'> {
    size?: number;
    onChange?: (file: File) => void;
}

const StyledButton = styled(IconButton)(({ theme }) => ({
    color: theme.palette.info.contrastText,
    backgroundColor: theme.palette.info.main,
    borderWidth: 2,
    borderColor: palette.gray.gray100,
    borderStyle: 'solid',
    width: 24,
    height: 24,
    fontSize: 14,
    '&:hover': {
        backgroundColor: theme.palette.info.dark,
    },
}));

export function AvatarUploader({
    onChange,
    size,
    src,
    ...rest
}: AvatarUploaderProps) {
    const { t } = useI18n();
    const picker = usePicker();
    const notification = useNotification();
    const loading = useLoading();
    const [source, setSource] = useState(src);
    const avatarStyle = useMemo(
        (): AvatarProps['sx'] => ({
            width: size,
            height: size,
            backgroundColor: palette.gray.gray100,
        }),
        [size],
    );

    const handlePicker = useEvent(async () => {
        try {
            loading.start();
            const file = await picker.pick({
                accept: 'image/*',
            });
            if (onChange) {
                await onChange(file);
            }
        } catch (error) {
            if (ErrorCode.match(error, 'cancelled')) {
                return;
            }

            notification.error(
                t('somethingWentWrongError', {
                    reason: t('errors.unableToSelectImage'),
                }),
            );
        } finally {
            loading.stop();
        }
    });

    useEffect(() => {
        setSource(src);
    }, [src]);

    return (
        <Badge
            overlap="circular"
            anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
            badgeContent={
                !loading.state ? (
                    <StyledButton onClick={handlePicker}>
                        <PhotoCameraIcon fontSize={'inherit'} />
                    </StyledButton>
                ) : null
            }
        >
            <Avatar
                sx={avatarStyle}
                src={!loading.state ? source : undefined}
                {...rest}
            >
                {loading.state ? (
                    <CircularProgress size={24} color={'info'} />
                ) : null}
            </Avatar>
        </Badge>
    );
}
