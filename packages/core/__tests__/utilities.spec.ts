import { num } from '../utilities';

describe('core#utilities', () => {
    it('should correctly parse a number', () => {
        expect(num(0)).toBe(0);
        expect(num(0, 1)).toBe(0);
        expect(num(null)).toBe(0);
        expect(num(null, 1)).toBe(1);
        expect(num(undefined)).toBe(0);
        expect(num(undefined, 1)).toBe(1);
        expect(num('')).toBe(0);
        expect(num('', 1)).toBe(1);
        expect(num('not_a_number_of_course')).toBe(0);
        expect(num('not_a_number_of_course', 1)).toBe(1);
        expect(num(101)).toBe(101);
        expect(num('101')).toBe(101);
    });
});
