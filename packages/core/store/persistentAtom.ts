import { atom, WritableAtom } from 'jotai';
import { value } from '../utilities';
import { PersistentStore } from './PersistentStore';

export interface PersistentState<T, E> {
    data: T;
    error?: E | null;
    loading: boolean;
}

export function persistentAtom<T, E = unknown>(
    key: string,
): WritableAtom<
    PersistentState<T | null, E>,
    [update: T | ((prev: T) => T)],
    void
>;
export function persistentAtom<T, E = unknown>(
    key: string,
    initialValue: T,
): WritableAtom<PersistentState<T, E>, [update: T | ((prev: T) => T)], void>;

export function persistentAtom<T = null, E = unknown>(
    key: string,
    initialValue?: T,
) {
    const baseAtom = atom<PersistentState<T | null, E>>({
        data: null,
        loading: true,
        error: null,
    });

    baseAtom.onMount = (setValue) => {
        setValue((prev) => ({ ...prev, loading: true }));

        PersistentStore.getItem(key)
            .then((data: any) =>
                setValue((prev) => ({
                    ...prev,
                    data: data ?? initialValue ?? null,
                })),
            )
            .catch((error) => setValue((prev) => ({ ...prev, error })))
            .finally(() => setValue((prev) => ({ ...prev, loading: false })));
    };

    return atom(
        (get) => get(baseAtom),
        (get, set, update) => {
            const state = get(baseAtom);
            try {
                state.data = value(update, state.data) as T;
            } catch (e) {
                state.error = e as E;
            } finally {
                state.loading = false;
            }

            set(baseAtom, { ...state });

            PersistentStore.setItem(key, state.data).catch(() => {
                // Ignore
            });
        },
    );
}
