import { isNil } from './checkers';

export const num = (value: any, defaultValue = 0) => {
    const n = Number(value);
    const isNan = Number.isNaN(n);
    const isValueNil = isNil(value);
    const isValueEmpty = !value && value !== 0;

    return isNan || isValueNil || isValueEmpty ? defaultValue : n;
};

export const formatNumber = (value: any, locale: string) => {
    const formatter = new Intl.NumberFormat(locale);

    return formatter.format(num(value));
};

export const formatCurrency = (
    value: any,
    currency: string,
    locale: string,
) => {
    const formatter = new Intl.NumberFormat(locale, {
        style: 'currency',
        currency,
    });

    return formatter.format(num(value));
};

export const clamp = (value: number, min: number, max: number) => {
    return Math.min(Math.max(value, min), max);
};
