const ErrorCodeSymbol = Symbol('ErrorCode');

export class ErrorCode extends Error {
    public [ErrorCodeSymbol] = true;

    constructor(
        public code: string,
        public message: string = '',
    ) {
        super(message || code);
    }

    public static from(code: string) {
        return new ErrorCode(code);
    }

    public static is(error: any): error is ErrorCode {
        return error instanceof ErrorCode || error?.[ErrorCodeSymbol];
    }

    public static match(error: any, code: string) {
        return ErrorCode.is(error) && error.code === code;
    }
}
