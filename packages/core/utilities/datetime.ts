import addMinutes from 'date-fns/addMinutes';
import getDay from 'date-fns/getDay';
import enGB from 'date-fns/locale/en-GB';
import enUS from 'date-fns/locale/en-US';
import ro from 'date-fns/locale/ro';
import parse from 'date-fns/parse';

export enum Weekday {
    Sunday = 'sunday',
    Monday = 'monday',
    Tuesday = 'tuesday',
    Wednesday = 'wednesday',
    Thursday = 'thursday',
    Friday = 'friday',
    Saturday = 'saturday',
}

export const Weekdays: Weekday[] = [
    Weekday.Sunday,
    Weekday.Monday,
    Weekday.Tuesday,
    Weekday.Wednesday,
    Weekday.Thursday,
    Weekday.Friday,
    Weekday.Saturday,
];

export function getWeekday(date: Date): Weekday {
    return Weekdays[getDay(date)];
}

export function isWeekdayEqual(a?: any, b?: any): boolean {
    return a && String(a).toLowerCase() === String(b).toLowerCase();
}

/**
 * Convert time string to index
 * @example
 * timeToIndex('00:00') // 10000
 * timeToIndex('10:00') // 11000
 * timeToIndex('10:30') // 10030
 * @param time
 */
export function timeToIndex(time: Date | string): number {
    if (time instanceof Date) {
        return Number(
            `1${time.getHours().toString().padStart(2, '0')}${time
                .getMinutes()
                .toString()
                .padStart(2, '0')}`,
        );
    }

    return Number('1' + time.replace(':', ''));
}

export class Duration {
    public static seconds(n: number): number {
        return n * 1000;
    }

    public static minutes(n: number): number {
        return n * this.seconds(60);
    }
}

export const locales: Record<
    'ro' | 'en' | 'en-us' | 'en-gb' | 'ro-ro' | string,
    Locale
> = {
    ro,
    en: enUS,
    'en-us': enUS,
    'en-gb': enGB,
    'ro-ro': ro,
};

export function getLocale(locale: string): Locale {
    return locales[String(locale).toLowerCase()] || enUS;
}

export function parseTime(time: string, relativeDate?: Date): Date {
    return parse(time, 'HH:mm', relativeDate || new Date());
}

export function getTimezoneOffset(date?: Date): number {
    return (date || new Date()).getTimezoneOffset();
}

export function utcToLocal(date: Date): Date {
    return addMinutes(date, -getTimezoneOffset(date));
}

export function date(value: any): Date {
    if (!value) {
        return new Date();
    }

    if (value instanceof Date) {
        return value;
    }

    return new Date(value);
}
