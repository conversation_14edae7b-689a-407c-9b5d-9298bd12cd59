export function castArray<T = any[]>(value: any): T {
    if (Array.isArray(value)) {
        return value as T;
    }

    return [value] as T;
}

type QueryValueStrategy = 'first';

export function queryValue<T = any>(
    value: T,
    strategy: QueryValueStrategy = 'first',
): T extends Array<infer P> ? P : T {
    if (Array.isArray(value)) {
        switch (strategy) {
            case 'first':
                return value[0];
        }
    }

    return value as any;
}
