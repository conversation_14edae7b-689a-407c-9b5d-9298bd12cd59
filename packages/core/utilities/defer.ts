import { Optional } from '@bookr-technologies/types';

interface Resolver<T> {
    (value: PromiseLike<T> | T): void;
}

interface Rejector {
    (reason?: any): void;
}

export class Defer<T = any> {
    private readonly __promise: Promise<T>;
    private resolvers: Set<Optional<Resolver<T>>> = new Set();
    private rejectors: Set<Optional<Rejector>> = new Set();

    constructor(resolve$?: Resolver<T>, reject$?: Rejector) {
        this.__promise = new Promise<T>((resolve, reject) => {
            this.resolvers.add(resolve$).add(resolve);
            this.rejectors.add(reject$).add(reject);
        });

        this.resolve = this.resolve.bind(this);
        this.reject = this.reject.bind(this);
    }

    public wait(): Promise<T> {
        return this.__promise;
    }

    /**
     * Resolve promise
     * @param value
     */
    public resolve(value?: T | PromiseLike<T>): void {
        this.resolvers.forEach((fn) => fn?.(value as T));
    }

    /**
     * Reject promise
     * @param reason
     */
    public reject(reason?: any): void {
        this.rejectors.forEach((fn) => fn?.(reason));
    }

    /**
     * Resolve the defer and wait right away
     * @param value
     */
    public resolveAndWait(value?: T | PromiseLike<T>): Promise<T> {
        this.resolve(value);
        return this.wait();
    }
}
