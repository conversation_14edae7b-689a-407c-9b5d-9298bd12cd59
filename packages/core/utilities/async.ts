import { value, Value } from './value';

export function parallel<T>(
    values: Iterable<T | PromiseLike<T>>,
): Promise<Awaited<T>[]> {
    const filteredValues = Array.from(values).filter(Boolean);

    return Promise.all(filteredValues);
}

interface RetryOptions {
    maxRetries?: number;
    delay?: number;
}

export async function retry<T>(
    callback: () => T | Promise<T>,
    options: RetryOptions = {},
) {
    const { maxRetries = 10, delay = 1000 } = options;
    let counter = 0;

    while (counter < maxRetries) {
        try {
            return await callback();
        } catch (error) {
            counter++;
            if (counter === maxRetries) {
                throw error;
            }
            await new Promise((resolve) => setTimeout(resolve, delay));
        }
    }

    return null;
}

export async function silentPromise<T>(promise: Value<Promise<T>>) {
    try {
        return await value(promise);
    } catch (error) {
        return null;
    }
}

export async function tryPromise<T, E extends Error>(
    promise: Value<Promise<T>>,
): Promise<[T | null, E | null]> {
    let result: T | null = null;
    let err = null;

    try {
        result = await value(promise);
    } catch (error) {
        err = error;
    }

    return [result, err as any];
}
