import { Optional } from '@bookr-technologies/types';
import { isNil } from './checkers';

export const str = (value: any, defaultValue = '') =>
    !isNil(value) ? String(value) : defaultValue;

export function readMore(text: string, maxLength: number) {
    if (text.length > maxLength) {
        return `${text
            .replace(/\r/g, '')
            .split('\n')
            .filter(Boolean)
            .join('\n')
            .substring(0, maxLength)}...`;
    }

    return text;
}

export function displayNameInitials(name: string) {
    const [firstName, lastName] = str(name).split(' ');

    if (firstName && lastName) {
        return `${firstName[0]}${lastName[0]}`.toUpperCase();
    }

    return firstName.slice(0, 2).toUpperCase();
}

export function displayFirstNameAndFirstLetterOfLastName(name: string) {
    const names = name.split(/\s+/);
    names[1] = !!names?.[1] ? names[1].slice(0, 1) + '.' : '';
    return names.join(' ');
}

const DefaultImages = [
    'https://d230pchl7el2vc.cloudfront.net/default_image.png',
    'https://firebasestorage.googleapis.com/v0/b/bookr-api.appspot.com/o/default_image.png?alt=media',
];

export function profilePicture(url: Optional<string>) {
    return url && !DefaultImages.includes(url) ? url : undefined;
}

export function capitalize(text: string) {
    return text.charAt(0).toUpperCase() + text.slice(1);
}

export function titleCase(text: string) {
    return text
        .split(/[\s_-]+/)
        .map((word) => capitalize(word.toLowerCase()))
        .join(' ');
}

export function cleanSlashes(...args: string[]) {
    return str(args.join('/'))
        .split('/')
        .filter(Boolean)
        .join('/')
        .replace(/^(http:|https:)?\//, '$1//');
}

export function pluralize(
    count: number,
    {
        zero,
        one,
        many,
        any,
    }: {
        zero?: string;
        one?: string;
        many?: string;
        any?: string;
    },
) {
    if (count === 0) {
        return zero ?? any ?? '';
    }

    if (count === 1) {
        return one ?? any ?? '';
    }

    return many ?? any ?? '';
}
