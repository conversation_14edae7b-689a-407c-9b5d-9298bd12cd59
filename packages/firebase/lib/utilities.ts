import { FirebaseError } from 'firebase/app';

export enum FirebaseErrorCode {
    WrongPassword = 'auth/wrong-password',
    TooManyRequests = 'auth/too-many-requests',
    AuthOperationNotAllowed = 'auth/operation-not-allowed',
    AuthSessionCookieRevoked = 'auth/session-cookie-revoked',
}

export function isFirebaseError(error: unknown): error is FirebaseError {
    const errName = (error as Error).constructor.name;
    return (
        error instanceof FirebaseError ||
        (errName.startsWith('Firebase') && errName.endsWith('Error'))
    );
}

export function isFirebaseErrorCode(
    error: unknown,
    code: FirebaseErrorCode,
): boolean {
    return isFirebaseError(error) && error.code === code;
}

export function matchFirebaseError(
    error: FirebaseError,
    map: Partial<Record<'default' | FirebaseErrorCode, any>>,
): any | null {
    if (!Object.values(FirebaseErrorCode).includes(error.code as any)) {
        console.warn(`Unknown Firebase error code: ${error.code}`);
    }

    if (map.hasOwnProperty(error.code)) {
        return map[error.code as FirebaseErrorCode];
    }

    if (map.default) {
        return map.default;
    }

    return null;
}
