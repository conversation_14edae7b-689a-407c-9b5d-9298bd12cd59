import * as firebaseRemoteConfig from 'firebase/remote-config';
import { getApp } from './app';

export function getRemoteConfig() {
    return firebaseRemoteConfig.getRemoteConfig(getApp());
}

export async function fetchAndActivateRemoteConfig() {
    return firebaseRemoteConfig.fetchAndActivate(getRemoteConfig());
}

export function getValue(key: string) {
    return firebaseRemoteConfig.getValue(getRemoteConfig(), key);
}

export async function getValueAsync(key: string) {
    await firebaseRemoteConfig.ensureInitialized(getRemoteConfig());
    return getValue(key);
}
