import * as firebaseAuth from 'firebase/auth';
import { track, TrackEvents } from '@bookr-technologies/analytics';
import { UserForms } from '@bookr-technologies/sdk';
import { getApp } from './app';

export function getAuth() {
    return firebaseAuth.getAuth(getApp());
}

export async function signInWithEmailAndPassword(
    email: string,
    password: string,
) {
    const user = await firebaseAuth.signInWithEmailAndPassword(
        getAuth(),
        email,
        password,
    );

    track(TrackEvents.SignIn, {
        method: 'email',
        email: user.user.email ?? 'N/A',
    });

    return user;
}

export function createUserWithEmailAndPassword(
    email: string,
    password: string,
) {
    return firebaseAuth.createUserWithEmailAndPassword(
        getAuth(),
        email,
        password,
    );
}

export async function signOut() {
    await firebaseAuth.signOut(getAuth());
}

export async function signInWithGoogle() {
    const provider = new firebaseAuth.GoogleAuthProvider();
    const user = await firebaseAuth.signInWithPopup(getAuth(), provider);

    track(TrackEvents.SignIn, {
        method: 'social',
        provider: 'google',
        email: user.user.email ?? 'N/A',
    });

    return user;
}

export async function signInWithFacebook() {
    const provider = new firebaseAuth.FacebookAuthProvider();
    const user = await firebaseAuth.signInWithPopup(getAuth(), provider);

    track(TrackEvents.SignIn, {
        method: 'social',
        provider: 'facebook',
        email: user.user.email ?? 'N/A',
    });

    return user;
}

export async function updateUserProfile(input: UserForms.CreateUserData) {
    const user = getAuth().currentUser;
    if (!user) {
        throw new Error('User not found');
    }

    await firebaseAuth.updateProfile(user, {
        displayName: input.displayName,
        photoURL: input.photoURL,
    });
}

export async function sendVerificationEmail(user?: firebaseAuth.User) {
    const firebaseUser = user ?? getAuth().currentUser;
    if (!firebaseUser) {
        throw new Error('User not found');
    }

    await firebaseAuth.sendEmailVerification(firebaseUser);
}

export async function sendPasswordResetEmail(email: string) {
    await firebaseAuth.sendPasswordResetEmail(getAuth(), email);
}

export async function changePassword(
    email: string,
    oldPassword: string,
    newPassword: string,
) {
    const auth = getAuth();
    await firebaseAuth.signInWithEmailAndPassword(auth, email, oldPassword);
    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    await firebaseAuth.updatePassword(auth.currentUser!, newPassword);
}
