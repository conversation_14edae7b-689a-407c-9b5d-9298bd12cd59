import {
    FirebaseApp,
    getApp as getFirebaseApp,
    getApps,
    initializeApp,
} from 'firebase/app';

const apps = getApps();
let appInstance: FirebaseApp | null = apps.length > 0 ? getFirebaseApp() : null;

export function getApp(): FirebaseApp {
    if (!appInstance) {
        appInstance = initializeApp({
            apiKey: 'AIzaSyABnTzuA_UCrj3poKssKiX9oGF7sJMFEiI',
            authDomain: 'bookr-api.firebaseapp.com',
            projectId: 'bookr-api',
            storageBucket: 'bookr-api.appspot.com',
            messagingSenderId: '393065803535',
            appId: '1:393065803535:web:3e8203d5c227a79a03187a',
            measurementId: 'G-0K6BPRYR67',
        });
    }

    return appInstance;
}
