import { notFound, unauthorized } from '@hapi/boom';
import hoursToMilliseconds from 'date-fns/hoursToMilliseconds';
import { signInWithCustomToken } from 'firebase/auth';
import { Optional } from '@bookr-technologies/types';
import { FirebaseErrorCode, getAuth, isFirebaseErrorCode } from '../lib';
import { Session } from '../types/Session';
import { getAdminApp } from './app';

const app = getAdminApp();

async function obtainCookieSession(
    session: Session,
    token: string,
    expiresInDays: number,
) {
    const expiresIn = hoursToMilliseconds(expiresInDays * 24);
    const [{ uid }, cookie] = await Promise.all([
        app.auth().verifyIdToken(token, true),
        app.auth().createSessionCookie(token, { expiresIn }),
    ]);

    session.authSession = cookie;

    return uid;
}

export async function createCookieSession(
    session: Session,
    token: string,
    expiresInDays = 14,
) {
    if (!session.authSession) {
        await obtainCookieSession(session, token, expiresInDays);
    }

    setAuthToken(session, token);

    return session.authSession;
}

function setAuthToken(session: Session, token: string) {
    session.firebaseAuthToken = token;
}

function getAuthToken(session: Session): string {
    return session.firebaseAuthToken;
}

async function getAuthTokenByCookieSession(session: Session, uid: string) {
    let token = getAuthToken(session);
    const isValid = await isTokenValid(token);

    if (!token || !isValid) {
        const customToken = await app.auth().createCustomToken(uid);
        const { user } = await signInWithCustomToken(getAuth(), customToken);
        token = await user.getIdToken();
        setAuthToken(session, token);
    }

    return token;
}

export async function isTokenValid(token: Optional<string>) {
    if (!token) {
        return false;
    }

    const verify = await app.auth().verifyIdToken(token);
    const now = Date.now() / 1000;

    return verify.exp - 120 > now;
}

async function verifyCookieSession(session: Session): Promise<string> {
    const cookieSession = session.authSession;

    try {
        const { uid } = await app
            .auth()
            .verifySessionCookie(cookieSession, true);
        return uid;
    } catch (e) {
        if (
            isFirebaseErrorCode(e, FirebaseErrorCode.AuthSessionCookieRevoked)
        ) {
            delete session.authSession;
            await createCookieSession(session, getAuthToken(session));
            return verifyCookieSession(session);
        }

        throw e;
    }
}

export async function decodeCookieSession(session: Session) {
    const cookieSession = session.authSession;

    if (!cookieSession) {
        throw notFound('No session found');
    }

    try {
        try {
            const claims = session.claims;
            if (claims) {
                const validSession = await isTokenValid(claims.token);
                if (validSession) {
                    return claims;
                }
            }
        } catch (error) {
            // ignore
        }

        const uid = await verifyCookieSession(session);
        const token = await getAuthTokenByCookieSession(session, uid);
        session.claims = { uid, token };

        return { uid, token };
    } catch (error) {
        throw unauthorized('Invalid session');
    }
}
