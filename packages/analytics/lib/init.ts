import { getClientConfig } from '@bookr-technologies/config';
import { isAnalyticsEnabled } from './isAnalyticsEnabled';
import { getCookieBannerConsent, setConsent } from './setConsent';
import { facebookInit } from './vendors/facebook/init';
import { firebaseInit } from './vendors/firebase/init';
import { mixpanelInit } from './vendors/mixpanel/init';

const { analytics } = getClientConfig();

export function initAnalytics() {
    if (!isAnalyticsEnabled()) {
        return;
    }

    if (isAnalyticsEnabled('mixpanel') && analytics?.mixpanel?.token) {
        mixpanelInit(analytics.mixpanel.token);
    }

    if (isAnalyticsEnabled('firebaseAnalytics')) {
        firebaseInit();
    }

    if (
        isAnalyticsEnabled('facebookPixel') &&
        analytics?.facebookPixel?.pixelId
    ) {
        facebookInit(analytics.facebookPixel.pixelId);
    }

    // noinspection JSIgnoredPromiseFromCall
    setConsent({ consent: getCookieBannerConsent() });
}
