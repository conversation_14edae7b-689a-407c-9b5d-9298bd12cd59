import {
    ClientRuntimeConfig,
    getClientConfig,
} from '@bookr-technologies/config';

const { analytics } = getClientConfig();

type Services = keyof Omit<ClientRuntimeConfig['analytics'], 'enabled'>;

export function isAnalyticsEnabled<K extends Services>(service?: K) {
    if (!service) {
        return analytics.enabled;
    }

    return analytics.enabled && analytics?.[service]?.enabled;
}
