import * as firebaseAnalytics from 'firebase/analytics';
import mixpanel from 'mixpanel-browser';
import { getAnalytics } from '@bookr-technologies/firebase';
import { isAnalyticsEnabled } from './isAnalyticsEnabled';
import { TrackEvent, TrackMap } from './types';
import ReactPixel from './vendors/facebook/pixel';
import { translateToFacebookEvent } from './vendors/facebook/translate';
import { translateToFirebaseEvent } from './vendors/firebase/translate';

interface TrackOptions {
    mixpanel?: boolean;
    firebaseAnalytics?: boolean;
    facebookPixel?: boolean;
}

export enum TrackEvents {
    PageView = 'page_view',
    SignUp = 'sign_up',
    SignIn = 'sign_in',
    BookedAppointment = 'booked_appointment',
    SubscribedToWaitingList = 'subscribed_to_waiting_list',
    ForgotPassword = 'forgot_password',
    PhoneCallAppointment = 'phone_call_appointment',
    ListBusinessClicked = 'list_business_clicked',
}

export type TrackEventsMap = TrackMap<
    | TrackEvent<
          TrackEvents.PageView,
          {
              pathname: string;
              title?: string;
          }
      >
    | TrackEvent<
          TrackEvents.SignUp,
          {
              method: 'email' | 'social';
              email: string;
          }
      >
    | TrackEvent<
          TrackEvents.SignIn,
          {
              method: 'email' | 'social';
              email: string;
              provider?: string;
          }
      >
    | TrackEvent<
          TrackEvents.BookedAppointment | TrackEvents.SubscribedToWaitingList,
          {
              accountType: string;
              businessName: string;
              serviceId: string | number;
              serviceName: string;
              servicePrice: string;
              staffId: string;
              staffName: string;
              platform: string;
          }
      >
    | TrackEvent<
          TrackEvents.ForgotPassword,
          {
              email: string;
          }
      >
    | TrackEvent<
          TrackEvents.PhoneCallAppointment,
          {
              businessId: string;
              businessName: string;
              phoneNumber: string;
              formattedPhoneNumber?: string;
          }
      >
    | TrackEvent<TrackEvents.ListBusinessClicked, { from: string }>
>;

function ignore(func: () => void) {
    try {
        func();
    } catch (e) {
        // Nothing to do
    }
}

export function track<K extends keyof TrackEventsMap>(
    event: K,
    properties?: TrackEventsMap[K],
    options?: TrackOptions,
) {
    // Skip tracking on the server
    if (typeof window === 'undefined') {
        return;
    }

    const eventName = String(event);
    const eventProperties = properties ?? {};

    if (process.env.NODE_ENV === 'development') {
        console.groupCollapsed(`[Analytics] ${eventName}`);
        console.log('Options:', options);
        console.log('Properties:', eventProperties);
        Object.entries(eventProperties ?? {}).forEach(([key, value]) =>
            console.log(`${key}:`, value),
        );
        console.groupEnd();
    }

    if (options?.mixpanel !== false && isAnalyticsEnabled('mixpanel')) {
        ignore(() => mixpanel.track(eventName, eventProperties));
    }

    if (
        options?.firebaseAnalytics !== false &&
        isAnalyticsEnabled('firebaseAnalytics')
    ) {
        ignore(() => {
            const args = translateToFirebaseEvent(
                eventName as K,
                eventProperties as TrackEventsMap[K],
            );
            if (args) {
                firebaseAnalytics.logEvent(getAnalytics(), ...args);
            }
        });
    }

    if (
        options?.facebookPixel !== false &&
        isAnalyticsEnabled('facebookPixel')
    ) {
        ignore(() => {
            const data = translateToFacebookEvent(
                eventName as K,
                eventProperties as TrackEventsMap[K],
            );

            if (!data) {
                return;
            }

            const [name, props, custom] = data;
            if (custom) {
                ReactPixel.trackCustom(name, props);
            } else {
                ReactPixel.track(name, props);
            }
        });
    }
}
