/**
 * @var {import('react-facebook-pixel')} ReactPixel
 */
const ReactPixel =
    typeof window !== 'undefined'
        ? // eslint-disable-next-line @typescript-eslint/no-var-requires
          require('react-facebook-pixel')['default']
        : {
              init: () => {
                  // do nothing
              },
              track: () => {
                  // do nothing
              },
              trackCustom: () => {
                  // do nothing
              },
          };

export default ReactPixel;
