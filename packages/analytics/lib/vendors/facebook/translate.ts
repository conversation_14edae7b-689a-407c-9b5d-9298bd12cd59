import { Nullable } from '@bookr-technologies/types';
import { TrackEvents, TrackEventsMap } from '../../track';

export function translateToFacebookEvent<K extends keyof TrackEventsMap>(
    event: K,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    props: TrackEventsMap[K],
): Nullable<[key: string, props?: Record<string, any>, custom?: boolean]> {
    if (event === TrackEvents.PageView) {
        return ['PageView'];
    }

    return null;
}
