import * as firebaseAnalytics from 'firebase/analytics';
import { Nullable } from '@bookr-technologies/types';
import { TrackEvents, TrackEventsMap } from '../../track';

const passThrough = (event: TrackEvents) =>
    [
        TrackEvents.BookedAppointment,
        TrackEvents.SignIn,
        TrackEvents.SignUp,
    ].includes(event);

export function translateToFirebaseEvent<K extends keyof TrackEventsMap>(
    event: K,
    props: TrackEventsMap[K],
): Nullable<
    [
        key: firebaseAnalytics.EventNameString | string,
        props: Record<string, any>,
    ]
> {
    if (event === TrackEvents.PageView) {
        const { pathname, title } =
            props as TrackEventsMap[TrackEvents.PageView];

        return [
            'page_view',
            {
                page_title: title ?? document.title,
                page_location: pathname,
                page_path: pathname,
            },
        ];
    }

    if (passThrough(event)) {
        return [event, props];
    }

    return null;
}
