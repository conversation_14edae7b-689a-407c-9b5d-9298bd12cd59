import * as firebaseAnalytics from 'firebase/analytics';
import <PERSON><PERSON> from 'js-cookie';
import mixpanel from 'mixpanel-browser';
import { silentPromise } from '@bookr-technologies/core';
import { getAnalytics } from '@bookr-technologies/firebase';
import { Optional } from '@bookr-technologies/types';
import { isAnalyticsEnabled } from './isAnalyticsEnabled';
import ReactPixel from './vendors/facebook/pixel';

export enum ConsentStatus {
    Granted = 'granted',
    Denied = 'denied',
    Unknown = 'unknown',
}

interface ConsentOptions {
    consent: Optional<ConsentStatus>;
}

export async function setConsent({ consent }: ConsentOptions) {
    if (
        !isAnalyticsEnabled('firebaseAnalytics') ||
        !consent ||
        consent === ConsentStatus.Unknown
    ) {
        return;
    }

    await silentPromise(async () => {
        if (isAnalyticsEnabled('firebaseAnalytics')) {
            firebaseAnalytics.setAnalyticsCollectionEnabled(
                getAnalytics(),
                consent === ConsentStatus.Granted,
            );
            firebaseAnalytics.setConsent({
                ad_storage: consent,
                analytics_storage: consent,
                functionality_storage: consent,
                security_storage: consent,
                personalization_storage: consent,
            });
        }

        if (isAnalyticsEnabled('facebookPixel')) {
            if (consent === ConsentStatus.Granted) {
                ReactPixel.grantConsent();
            } else {
                ReactPixel.revokeConsent();
            }
        }

        if (isAnalyticsEnabled('mixpanel')) {
            if (
                consent === ConsentStatus.Granted &&
                !mixpanel.has_opted_in_tracking()
            ) {
                mixpanel.opt_in_tracking();
            } else if (
                consent === ConsentStatus.Denied &&
                !mixpanel.has_opted_out_tracking()
            ) {
                mixpanel.opt_out_tracking();
            }
        }
    });
}

const cookieBannerConsentName = 'cookieBannerConsent';

export function setCookieBannerConsent(status: ConsentStatus) {
    Cookie.set(cookieBannerConsentName, status, {
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'none',
    });
}

export function getCookieBannerConsent(): ConsentStatus {
    return (
        (Cookie.get(cookieBannerConsentName) as ConsentStatus) ||
        ConsentStatus.Unknown
    );
}
