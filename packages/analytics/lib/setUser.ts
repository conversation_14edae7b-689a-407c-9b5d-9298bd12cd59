import * as Sentry from '@sentry/nextjs';
import * as firebaseAnalytics from 'firebase/analytics';
import mixpanel from 'mixpanel-browser';
import { getAnalytics } from '@bookr-technologies/firebase';
import { isAnalyticsEnabled } from './isAnalyticsEnabled';

interface AnalyticsUser {
    uid: string;
    email?: string;
    phone?: string;
    displayName?: string;
    createdAt?: string;
}

export async function setUser(user: AnalyticsUser) {
    if (isAnalyticsEnabled('sentry')) {
        Sentry.setUser({ id: user.uid });
    }

    if (isAnalyticsEnabled('mixpanel')) {
        await mixpanel.identify(user.uid);
        await mixpanel.people.set({
            $email: user.email,
            $phone: user.phone,
            $name: user.displayName,
            $created: user.createdAt,
        });
    }

    if (isAnalyticsEnabled('firebaseAnalytics')) {
        firebaseAnalytics.setUserId(getAnalytics(), user.uid, { global: true });
    }
}
