{"name": "@bookr-technologies/analytics", "version": "0.1.0", "main": "index.ts", "types": "./index.ts", "license": "MIT", "scripts": {"lint": "TIMING=1 eslint \"**/*.ts*\""}, "dependencies": {"@sentry/nextjs": "^7.52.1", "firebase": "^10.0.0", "js-cookie": "^3.0.5", "mixpanel-browser": "^2.47.0", "react-facebook-pixel": "^1.0.4"}, "devDependencies": {"@bookr-technologies/eslint-config-custom": "*", "@bookr-technologies/tsconfig": "*", "@bookr-technologies/config": "*", "@types/js-cookie": "^3.0.3", "@types/mixpanel-browser": "^2.38.1"}}