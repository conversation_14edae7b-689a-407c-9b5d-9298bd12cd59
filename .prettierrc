{"tabWidth": 4, "printWidth": 80, "semi": true, "singleQuote": true, "jsxSingleQuote": false, "trailingComma": "all", "bracketSpacing": true, "bracketSameLine": false, "arrowParens": "always", "endOfLine": "lf", "proseWrap": "always", "importOrder": ["^@bookr-technologies/(.*)$", "^[\\~/]", "^[./]"], "importOrderSeparation": false, "plugins": ["@trivago/prettier-plugin-sort-imports"], "overrides": [{"files": ["*.js", "*.jsx"], "options": {"parser": "babel-flow"}}, {"files": ["*.yml", "*.yaml"], "options": {"tabWidth": 2}}]}