{"$schema": "https://docs.renovatebot.com/renovate-schema.json", "extends": ["config:base"], "platformAutomerge": true, "automergeStrategy": "squash", "packageRules": [{"matchUpdateTypes": ["minor", "patch"], "matchCurrentVersion": "!/^0/", "excludeDepNames": ["connect-redis"], "matchDepTypes": ["dependencies", "devDependencies"], "automerge": true}, {"extends": ["monorepo:material-ui", "monorepo:nextjs", "monorepo:turbo", "monorepo:storybook"], "matchUpdateTypes": ["patch", "minor"], "automerge": true}]}