/* eslint-disable @typescript-eslint/no-var-requires */
const { withSentryConfig } = require('@sentry/nextjs');
const pkg = require('./package.json');
const {
    getPublicRuntimeConfig,
    getServerRuntimeConfig,
} = require('@bookr-technologies/config/factory');

const withPWAFactory = require('next-pwa');
const filterBookrDeps = (dep) => dep.startsWith('@bookr-technologies');
const transpilePackages = Object.keys(pkg.dependencies).filter(filterBookrDeps);

const serverRuntimeConfig = getServerRuntimeConfig();
const publicRuntimeConfig = getPublicRuntimeConfig();

/**
 * @type {import("next").NextConfig}
 */
const nextConfig = {
    transpilePackages,
    serverRuntimeConfig,
    publicRuntimeConfig,
    output: 'standalone',
    reactStrictMode: true,
    images: {
        remotePatterns: [
            {
                protocol: 'https',
                hostname: new URL(publicRuntimeConfig.urls.cdn).hostname,
                pathname: '/**',
                port: '',
            },
            {
                protocol: 'https',
                hostname: 'firebasestorage.googleapis.com',
                pathname: '/**',
                port: '',
            },
        ],
    },
    eslint: {
        ignoreDuringBuilds: true,
    },
    productionBrowserSourceMaps: serverRuntimeConfig.env !== 'production',
    modularizeImports: {
        '@mui/icons-material': {
            transform: '@mui/icons-material/{{member}}',
        },
        '@mui/lab': {
            transform: '@mui/lab/{{member}}',
        },
        '@mui/material': {
            transform: '@mui/material/{{member}}',
        },
        lodash: {
            transform: 'lodash/{{member}}',
        },
    },
    i18n: {
        locales: ['ro', 'en'],
        defaultLocale: 'ro',
        localeDetection: false,
        domains: [
            { domain: 'bookr.ro', defaultLocale: 'ro' },
            // { domain: 'bookr.ae', defaultLocale: 'en' },
            // { domain: 'bookr.ie', defaultLocale: 'en-ie' },
        ],
    },
};

module.exports = withSentryConfig(
    withPWAFactory({
        dest: 'public',
        disable: !serverRuntimeConfig.pwa.enabled,
    })(nextConfig),
    {
        silent: true,
        org: 'bookr-technologies',
        project: 'bookr-nextjs',
    },
    {
        widenClientFileUpload: true,
        transpileClientSDK: true,
        tunnelRoute: '/monitoring',
        hideSourceMaps: true,
        disableLogger: true,
    },
);
