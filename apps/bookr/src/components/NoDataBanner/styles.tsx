import Paper from '@mui/material/Paper';
import { styled } from '@mui/material/styles';
import Image, { ImageProps } from 'next/image';
import { clsx } from '@bookr-technologies/ui/utils';

function BannerImageComponent({ className, ...rest }: ImageProps) {
    return (
        // eslint-disable-next-line jsx-a11y/alt-text
        <Image
            priority
            className={clsx(className, 'NoDataBanner-image')}
            {...rest}
        />
    );
}

export const BannerImage = styled(BannerImageComponent)(({ theme }) => ({
    zIndex: 1,
    position: 'absolute',
    right: 0,
    bottom: 0,
    maxHeight: '100%',
    width: '40%',
    minWidth: 425,
    objectFit: 'contain',
    objectPosition: 'right bottom',

    [theme.breakpoints.down('md')]: {
        display: 'none',
    },
}));

export const StyledBanner = styled(Paper)(({ theme }) => ({
    overflow: 'hidden',
    position: 'relative',
    minHeight: 246,
    [theme.breakpoints.down('md')]: {
        minHeight: 0,
    },
    '.NoDataBanner-content': {
        maxWidth: 'calc(100% - 250px)',
        [theme.breakpoints.down('md')]: {
            maxWidth: '100%',
        },
    },
}));
