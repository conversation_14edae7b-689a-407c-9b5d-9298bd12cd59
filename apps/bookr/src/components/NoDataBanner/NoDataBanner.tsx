import { PaperProps } from '@mui/material/Paper';
import Stack from '@mui/material/Stack';
import Typography, { TypographyProps } from '@mui/material/Typography';
import { Theme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import { ImageProps } from 'next/image';
import { PropsWithChildren } from 'react';
import { BannerImage, StyledBanner } from './styles';

interface Props extends PaperProps {
    title: string;
    subtitle?: string;
    src?: ImageProps['src'];
    alt?: ImageProps['alt'];
    titleProps?: TypographyProps;
    subtitleProps?: TypographyProps;
}

export function NoDataBanner({
    title,
    subtitle,
    children,
    titleProps,
    subtitleProps,
    src,
    alt,
    ...rest
}: PropsWithChildren<Props>) {
    const hideImage = useMediaQuery((theme: Theme) =>
        theme.breakpoints.down('md'),
    );

    return (
        <StyledBanner {...rest}>
            <Stack
                p={5}
                alignItems={'flex-start'}
                gap={3}
                zIndex={2}
                className={'NoDataBanner-content'}
            >
                <Stack gap={1}>
                    <Typography
                        variant={'largeTitle'}
                        fontWeight={800}
                        {...titleProps}
                    >
                        {title}
                    </Typography>
                    {subtitle ? (
                        <Typography
                            variant={'body'}
                            color={'textSecondary'}
                            fontWeight={600}
                            {...subtitleProps}
                        >
                            {subtitle}
                        </Typography>
                    ) : null}
                </Stack>
                {children}
            </Stack>
            {src && !hideImage ? (
                <BannerImage src={src} alt={alt || 'banner'} />
            ) : null}
        </StyledBanner>
    );
}
