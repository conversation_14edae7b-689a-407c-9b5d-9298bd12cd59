import Box from '@mui/material/Box';
import CircularProgress from '@mui/material/CircularProgress';
import Grid from '@mui/material/Grid';
import Paper from '@mui/material/Paper';
import Typography from '@mui/material/Typography';
import { ElementType, ReactNode } from 'react';
import { isValidElementType } from 'react-is';
import { useI18n } from '@bookr-technologies/i18n';
import { DisplayContentProps } from './types';

const getErrorMessage = (error: unknown, defaultMessage: string) => {
    if (typeof error === 'string') {
        return error;
    }

    if (error instanceof Error) {
        return error.message;
    }

    return defaultMessage;
};

const content = (
    Component: ReactNode | ElementType | undefined,
    disabled: boolean | undefined,
    children: ReactNode,
) => {
    if (disabled) {
        return null;
    }

    if (Component) {
        return isValidElementType(Component) ? <Component /> : Component;
    }

    return (
        <Grid container justifyContent={'center'}>
            <Paper sx={{ p: 4 }}>{children}</Paper>
        </Grid>
    );
};

export function DisplayContent({
    children,
    loading,
    error,
    noResults,
    slots,
    disabledSlots,
}: DisplayContentProps) {
    const { t } = useI18n();
    if (loading) {
        return content(
            slots?.Loading,
            disabledSlots?.Loading,
            <Box fontSize={0}>
                <CircularProgress color={'info'} size={24} />
            </Box>,
        );
    }

    if (error) {
        return content(
            slots?.Error,
            disabledSlots?.Error,
            <Typography variant={'subhead'} fontWeight={600} color={'error'}>
                {getErrorMessage(error, t('somethingWentWrong'))}
            </Typography>,
        );
    }

    if (noResults) {
        return content(
            slots?.NoResults,
            disabledSlots?.NoResults,
            <Typography
                variant={'subhead'}
                fontWeight={600}
                color={'textSecondary'}
            >
                {t('noResults')}
            </Typography>,
        );
    }

    return <>{children}</>;
}
