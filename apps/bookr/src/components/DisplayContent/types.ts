import { ElementType, ReactNode } from 'react';

type DisplayContentSlots = 'Loading' | 'Error' | 'NoResults';

export interface DisplayContentProps {
    children: ReactNode;

    loading?: boolean;
    error?: boolean | string | Error;
    noResults?: boolean;

    slots?: Partial<Record<DisplayContentSlots, ReactNode | ElementType>>;
    disabledSlots?: Partial<Record<DisplayContentSlots, boolean>>;
}
