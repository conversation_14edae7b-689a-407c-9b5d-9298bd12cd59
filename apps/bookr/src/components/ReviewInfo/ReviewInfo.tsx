import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { useI18n } from '@bookr-technologies/i18n';
import { ReviewStars } from '~/components/ReviewStars';
import { ReviewInfoProps } from './types';

export function ReviewInfo({
    stars,
    averageRating,
    noOfReviews,
    noContrast,
    stackProps,
    ...rest
}: ReviewInfoProps) {
    const { t, locale } = useI18n();
    const rating = Math.max(0, averageRating ?? 0);
    const reviews = Math.max(0, noOfReviews ?? 0);

    return (
        <Stack {...stackProps}>
            {stars ? <ReviewStars value={rating ?? 0} /> : null}
            <Grid container alignItems={'center'} gap={0.5}>
                <Typography
                    variant={'footnote'}
                    fontWeight={600}
                    lineHeight={'20px'}
                    {...rest}
                >
                    {t('Rating', { averageRating: rating })}
                </Typography>
                {noOfReviews ? (
                    <>
                        <Typography
                            variant={'footnote'}
                            fontWeight={600}
                            lineHeight={'20px'}
                            {...rest}
                            color={noContrast ? rest.color : 'textSecondary'}
                        >
                            &bull;
                        </Typography>
                        <Typography
                            variant={'footnote'}
                            fontWeight={600}
                            lineHeight={'20px'}
                            {...rest}
                            color={noContrast ? rest.color : 'textSecondary'}
                        >
                            {t('No. of Reviews', {
                                noOfReviews: reviews,
                                locale,
                            })}
                        </Typography>
                    </>
                ) : null}
            </Grid>
        </Stack>
    );
}
