import Script from 'next/script';
import { isAnalyticsEnabled } from '@bookr-technologies/analytics';

export function AnalyticsScripts() {
    return (
        <>
            {isAnalyticsEnabled('hubspot') ? (
                <Script
                    id="hs-script-loader"
                    async
                    defer
                    src="https://js-eu1.hs-scripts.com/25760055.js"
                />
            ) : null}

            {isAnalyticsEnabled('googleTagManager') ? (
                <>
                    <Script
                        async
                        src="https://www.googletagmanager.com/gtag/js?id=UA-212462732-1"
                    />
                    <Script id={'gtag-ga'}>
                        {`
                            window.dataLayer = window.dataLayer || [];
                            function gtag() { dataLayer.push(arguments); }
                            gtag('js', new Date());
                            gtag('config', 'UA-212462732-1');
                        `}
                    </Script>

                    <Script id={'gtag-gtm'}>
                        {`(function(w, d, s, l, i) {
  w[l] = w[l] || [];
  w[l].push({ "gtm.start": new Date().getTime(), event: "gtm.js" });
  var f = d.getElementsByTagName(s)[0],
    j = d.createElement(s),
    dl = l !== "dataLayer" ? "&l=" + l : "";
  j.async = true;
  j.src = "https://www.googletagmanager.com/gtm.js?id=" + i + dl;
  f.parentNode.insertBefore(j, f);
})(window, document, "script", "dataLayer", "GTM-5FPSJ8T");`}
                    </Script>
                </>
            ) : null}

            <Script id={'getreditus-affiliate-marketing'}>
                {`
                (function (w, d, s, p, t) { w.gr = w.gr || function () { w.gr.ce = null; w.gr.q = w.gr.q || []; w.gr.q.push(arguments); }; p = d.getElementsByTagName(s)[0]; t = d.createElement(s); t.async = true; t.src = "https://script.getreditus.com/v2.js"; p.parentNode.insertBefore(t, p); })(window, document, "script"); gr("initCustomer", "57f4a557-c343-4590-a80e-1bc0e0255851"); gr("track", "pageview");
                `}
            </Script>
        </>
    );
}
