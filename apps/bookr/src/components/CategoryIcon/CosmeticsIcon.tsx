import { SVGProps } from 'react';
import { clsx } from '@bookr-technologies/ui/utils';

export function CosmeticsIcon({ ...rest }: SVGProps<SVGSVGElement>) {
    rest.className = clsx(
        rest.className,
        'CategoryIcon-root',
        'CategoryIcon-cosmetics',
    );

    return (
        <svg
            width="32"
            height="32"
            viewBox="0 0 32 32"
            fill="currentColor"
            xmlns="http://www.w3.org/2000/svg"
            {...rest}
        >
            <path d="M25.7667 10.2557C25.6112 10.2557 25.4834 10.2224 25.3834 10.1557C25.2834 10.089 25.2 9.98902 25.1334 9.85568L24.2 7.65568L21.8334 6.62235C21.6778 6.53346 21.5723 6.43902 21.5167 6.33902C21.4612 6.23902 21.4334 6.12235 21.4334 5.98902C21.4334 5.85568 21.4612 5.73346 21.5167 5.62235C21.5723 5.51124 21.6778 5.42235 21.8334 5.35568L24.2 4.35568L25.1334 2.25568C25.2 2.12235 25.2834 2.02235 25.3834 1.95568C25.4834 1.88902 25.6112 1.85568 25.7667 1.85568C25.9 1.85568 26.0223 1.88902 26.1334 1.95568C26.2445 2.02235 26.3334 2.12235 26.4 2.25568L27.3334 4.35568L29.7 5.35568C29.8334 5.42235 29.9334 5.51124 30 5.62235C30.0667 5.73346 30.1 5.85568 30.1 5.98902C30.1 6.12235 30.0667 6.23902 30 6.33902C29.9334 6.43902 29.8334 6.53346 29.7 6.62235L27.3334 7.65568L26.4 9.85568C26.3334 9.98902 26.2445 10.089 26.1334 10.1557C26.0223 10.2224 25.9 10.2557 25.7667 10.2557ZM25.7667 30.1224C25.6334 30.1224 25.5112 30.089 25.4 30.0224C25.2889 29.9557 25.2 29.8557 25.1334 29.7224L24.2 27.6224L21.8667 26.6224C21.7112 26.5335 21.6056 26.439 21.55 26.339C21.4945 26.239 21.4667 26.1224 21.4667 25.989C21.4667 25.8557 21.4945 25.7335 21.55 25.6224C21.6056 25.5112 21.7112 25.4224 21.8667 25.3557L24.2 24.3557L25.1334 22.1224C25.2 21.989 25.2834 21.889 25.3834 21.8224C25.4834 21.7557 25.6112 21.7224 25.7667 21.7224C25.9 21.7224 26.0223 21.7557 26.1334 21.8224C26.2445 21.889 26.3334 21.989 26.4 22.1224L27.3334 24.3557L29.6667 25.3557C29.8 25.4224 29.9 25.5112 29.9667 25.6224C30.0334 25.7335 30.0667 25.8557 30.0667 25.989C30.0667 26.1224 30.0334 26.239 29.9667 26.339C29.9 26.439 29.8 26.5335 29.6667 26.6224L27.3334 27.6224L26.4 29.7224C26.3334 29.8557 26.2389 29.9557 26.1167 30.0224C25.9945 30.089 25.8778 30.1224 25.7667 30.1224ZM11.2 24.2557C10.9556 24.2557 10.7223 24.189 10.5 24.0557C10.2778 23.9224 10.1112 23.7446 10 23.5224L7.96672 19.1557L3.56672 17.189C3.32227 17.0557 3.13338 16.8835 3.00005 16.6724C2.86672 16.4612 2.80005 16.2335 2.80005 15.989C2.80005 15.7446 2.86672 15.5168 3.00005 15.3057C3.13338 15.0946 3.32227 14.9223 3.56672 14.789L7.96672 12.8224L10 8.48902C10.1112 8.24457 10.2778 8.05013 10.5 7.90568C10.7223 7.76124 10.9556 7.68902 11.2 7.68902C11.4223 7.68902 11.6445 7.76124 11.8667 7.90568C12.0889 8.05013 12.2667 8.23346 12.4 8.45568L14.4667 12.8224L18.8 14.789C19.0667 14.9223 19.2667 15.0946 19.4 15.3057C19.5334 15.5168 19.6 15.7446 19.6 15.989C19.6 16.2335 19.5334 16.4612 19.4 16.6724C19.2667 16.8835 19.0667 17.0557 18.8 17.189L14.4667 19.1557L12.4 23.5224C12.2667 23.7668 12.0889 23.9501 11.8667 24.0724C11.6445 24.1946 11.4223 24.2557 11.2 24.2557ZM11.2 20.6224L12.8 17.4224L16.0667 15.989L12.8 14.5557L11.2 11.3557L9.63338 14.5557L6.33338 15.989L9.63338 17.4224L11.2 20.6224Z" />
        </svg>
    );
}
