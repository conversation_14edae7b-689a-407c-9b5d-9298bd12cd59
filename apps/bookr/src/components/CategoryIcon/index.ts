import { ComponentType, SVGProps } from 'react';
import { CategoriesNameEnum } from '@bookr-technologies/sdk';
import { BarberIcon } from './BarberIcon';
import { BodyRemodelingIcon } from './BodyRemodelingIcon';
import { BookrIcon } from './BookrIcon';
import { CoachingIcon } from './CoachingIcon';
import { ConsultancyIcon } from './ConsultancyIcon';
import { CosmeticsIcon } from './CosmeticsIcon';
import { DentistryIcon } from './DentistryIcon';
import { DermatologyIcon } from './DermatologyIcon';
import { GynecologyIcon } from './GynecologyIcon';
import { HairstylingIcon } from './HairstylingIcon';
import { ImplantIcon } from './ImplantIcon';
import { MakeupIcon } from './MakeupIcon';
import { ManicureIcon } from './ManicureIcon';
import { MassageIcon } from './MassageIcon';
import { MentoringIcon } from './MentoringIcon';
import { NutritionDieteticsIcon } from './NutritionDieteticsIcon';
import { OphthalmologyIcon } from './OphthalmologyIcon';
import { OrlIcon } from './OrlIcon';
import { PermanentHairRemovalIcon } from './PermanentHairRemovalIcon';
import { PersonalTrainerIcon } from './PersonalTrainerIcon';
import { PetGroomingIcon } from './PetGroomingIcon';
import { PlasticSurgeryIcon } from './PlasticSurgeryIcon';
import { PodiatryIcon } from './PodiatryIcon';
import { PsychologistIcon } from './PsychologistIcon';
import { SurgeryIcon } from './SurgeryIcon';

export { categoryIcons } from './assets';

export const categoryComponents: Record<
    CategoriesNameEnum,
    ComponentType<SVGProps<SVGSVGElement>>
> = {
    [CategoriesNameEnum.BARBER]: BarberIcon,
    [CategoriesNameEnum.BODY_REMODELING]: BodyRemodelingIcon,
    [CategoriesNameEnum.COACHING]: CoachingIcon,
    [CategoriesNameEnum.CONSULTANCY]: ConsultancyIcon,
    [CategoriesNameEnum.COSMETICS]: CosmeticsIcon,
    [CategoriesNameEnum.COWORKING]: BookrIcon,
    [CategoriesNameEnum.DENTISTRY]: DentistryIcon,
    [CategoriesNameEnum.DERMATOLOGY]: DermatologyIcon,
    [CategoriesNameEnum.EVENT]: BookrIcon,
    [CategoriesNameEnum.GYNECOLOGY]: GynecologyIcon,
    [CategoriesNameEnum.HAIRSTYLING]: HairstylingIcon,
    [CategoriesNameEnum.IMPLANT]: ImplantIcon,
    [CategoriesNameEnum.MAKEUP]: MakeupIcon,
    [CategoriesNameEnum.MANICURE]: ManicureIcon,
    [CategoriesNameEnum.MASSAGE]: MassageIcon,
    [CategoriesNameEnum.MENTORING]: MentoringIcon,
    [CategoriesNameEnum.NUTRITION_DIETETICS]: NutritionDieteticsIcon,
    [CategoriesNameEnum.OPHTHALMOLOGY]: OphthalmologyIcon,
    [CategoriesNameEnum.ORL]: OrlIcon,
    [CategoriesNameEnum.OTHER]: BookrIcon,
    [CategoriesNameEnum.PERMANENT_HAIR_REMOVAL]: PermanentHairRemovalIcon,
    [CategoriesNameEnum.PERSONAL_TRAINER]: PersonalTrainerIcon,
    [CategoriesNameEnum.PET_GROOMING]: PetGroomingIcon,
    [CategoriesNameEnum.PHOTOGRAPHY]: BookrIcon,
    [CategoriesNameEnum.PLASTIC_SURGERY]: PlasticSurgeryIcon,
    [CategoriesNameEnum.PODIATRY]: PodiatryIcon,
    [CategoriesNameEnum.PSYCHOLOGIST]: PsychologistIcon,
    [CategoriesNameEnum.SPORT]: BookrIcon,
    [CategoriesNameEnum.SURGERY]: SurgeryIcon,
    [CategoriesNameEnum.TATTOOS]: BookrIcon,
    [CategoriesNameEnum.VIDEOGRAPHY]: BookrIcon,
};

export {
    BarberIcon,
    BodyRemodelingIcon,
    BookrIcon,
    CoachingIcon,
    ConsultancyIcon,
    CosmeticsIcon,
    DentistryIcon,
    DermatologyIcon,
    GynecologyIcon,
    HairstylingIcon,
    ImplantIcon,
    MakeupIcon,
    ManicureIcon,
    MassageIcon,
    MentoringIcon,
    NutritionDieteticsIcon,
    OphthalmologyIcon,
    OrlIcon,
    PermanentHairRemovalIcon,
    PersonalTrainerIcon,
    PetGroomingIcon,
    PlasticSurgeryIcon,
    PodiatryIcon,
    PsychologistIcon,
    SurgeryIcon,
};
