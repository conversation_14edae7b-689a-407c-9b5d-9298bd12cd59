import { SVGProps } from 'react';
import { clsx } from '@bookr-technologies/ui/utils';

export function PetGroomingIcon({ ...rest }: SVGProps<SVGSVGElement>) {
    rest.className = clsx(
        rest.className,
        'CategoryIcon-root',
        'CategoryIcon-petGrooming',
    );

    return (
        <svg
            width="32"
            height="32"
            viewBox="0 0 32 32"
            fill="currentColor"
            xmlns="http://www.w3.org/2000/svg"
            {...rest}
        >
            <path d="M6.46671 29.3557V16.189H9.13337V26.689H18.4667V20.2557L21.9667 16.7557C22.6112 16.1112 23.1112 15.389 23.4667 14.589C23.8223 13.789 24 12.9334 24 12.0223C24 11.1334 23.8167 10.289 23.45 9.48899C23.0834 8.68899 22.5889 7.96677 21.9667 7.32233L21.1334 6.45566L16.9 10.689H11.5667L10.1334 12.1223L8.23337 10.2557L10.4667 8.02233H15.8L21.1334 2.689L23.8667 5.42233C24.7556 6.31122 25.4445 7.31677 25.9334 8.43899C26.4223 9.56122 26.6667 10.7557 26.6667 12.0223C26.6667 13.289 26.4223 14.4834 25.9334 15.6057C25.4445 16.7279 24.7556 17.7334 23.8667 18.6223L21.1334 21.3557V29.3557H6.46671ZM13.0334 23.1223L6.10004 16.189C5.8556 15.9446 5.66671 15.6557 5.53337 15.3223C5.40004 14.989 5.33337 14.6446 5.33337 14.289C5.33337 13.9334 5.40004 13.5946 5.53337 13.2723C5.66671 12.9501 5.8556 12.6668 6.10004 12.4223L8.90004 9.58899L13.0334 13.689C13.6556 14.3112 14.1389 15.0279 14.4834 15.839C14.8278 16.6501 15 17.5001 15 18.389C15 19.2779 14.8334 20.1279 14.5 20.939C14.1667 21.7501 13.6778 22.4779 13.0334 23.1223Z" />
        </svg>
    );
}
