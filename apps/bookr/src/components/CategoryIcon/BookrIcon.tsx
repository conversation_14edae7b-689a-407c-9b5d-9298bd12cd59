import { SVGProps } from 'react';
import { clsx } from '@bookr-technologies/ui/utils';

export function BookrIcon({ ...rest }: SVGProps<SVGSVGElement>) {
    rest.className = clsx(
        rest.className,
        'CategoryIcon-root',
        'CategoryIcon-bookr',
    );

    return (
        <svg
            width="32"
            height="32"
            viewBox="0 0 32 32"
            fill="currentColor"
            xmlns="http://www.w3.org/2000/svg"
            {...rest}
        >
            <path d="M22.5151 17.6813L21.0126 16.1132C22.1596 15.2021 22.8975 13.7935 22.8975 12.2165C22.8975 9.4751 20.6681 7.24454 17.9283 7.24454H8.31775V24.8002H19.4732C21.794 24.8002 23.6822 22.912 23.6822 20.59C23.6822 19.4635 23.2374 18.4375 22.5151 17.6813ZM11.1044 10.0327H17.9283C19.131 10.0327 20.1108 11.012 20.1108 12.2165C20.1108 13.005 19.6906 13.6975 19.0642 14.0812L18.7142 13.7154L18.6997 13.7009L18.5191 13.5113L18.5091 13.5013C17.8603 12.8778 16.9786 12.4942 16.01 12.4942C15.9275 12.4942 15.845 12.4975 15.7637 12.502C14.8329 12.5655 13.998 12.9827 13.3928 13.6206L13.2167 13.7968L11.1055 15.9281V10.0327H11.1044ZM19.4732 22.012H11.1044V19.8885L12.9491 18.026L14.8441 16.1144L15.3958 15.5567C15.5463 15.3883 15.7659 15.2824 16.01 15.2824C16.2151 15.2824 16.4024 15.3571 16.5473 15.4831L16.5495 15.4853L16.6554 15.5958L17.7567 16.7456H17.7578L18.1769 17.1828L19.0363 18.0795L20.4787 19.584L20.5043 19.6108C20.7473 19.8662 20.8955 20.2119 20.8955 20.59C20.8955 21.374 20.2568 22.012 19.4732 22.012Z" />
        </svg>
    );
}
