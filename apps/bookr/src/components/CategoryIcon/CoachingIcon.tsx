import { SVGProps } from 'react';
import { clsx } from '@bookr-technologies/ui/utils';

export function CoachingIcon({ ...rest }: SVGProps<SVGSVGElement>) {
    rest.className = clsx(
        rest.className,
        'CategoryIcon-root',
        'CategoryIcon-coaching',
    );

    return (
        <svg
            width="32"
            height="32"
            viewBox="0 0 32 32"
            fill="currentColor"
            xmlns="http://www.w3.org/2000/svg"
            {...rest}
        >
            <path d="M14.6667 25.3557C12.4445 25.3557 10.5556 24.5779 9.00008 23.0223C7.44453 21.4668 6.66675 19.5779 6.66675 17.3557C6.66675 17.1112 6.67786 16.8668 6.70008 16.6223C6.7223 16.3779 6.75564 16.1335 6.80008 15.889C6.66675 15.9335 6.53341 15.9668 6.40008 15.989C6.26675 16.0112 6.13341 16.0223 6.00008 16.0223C5.06675 16.0223 4.27786 15.7001 3.63341 15.0557C2.98897 14.4112 2.66675 13.6223 2.66675 12.689C2.66675 11.7557 2.9723 10.9668 3.58341 10.3223C4.19453 9.6779 4.96675 9.35568 5.90008 9.35568C6.63341 9.35568 7.29453 9.56124 7.88341 9.97235C8.4723 10.3835 8.88897 10.9112 9.13341 11.5557C9.86675 10.889 10.7056 10.3557 11.6501 9.95568C12.5945 9.55568 13.6001 9.35568 14.6667 9.35568H28.0001C28.3779 9.35568 28.6945 9.48346 28.9501 9.73902C29.2056 9.99457 29.3334 10.3112 29.3334 10.689V13.3557C29.3334 13.7335 29.2056 14.0501 28.9501 14.3057C28.6945 14.5612 28.3779 14.689 28.0001 14.689H22.6667V17.3557C22.6667 19.5779 21.889 21.4668 20.3334 23.0223C18.7779 24.5779 16.889 25.3557 14.6667 25.3557ZM6.00008 14.0223C6.37786 14.0223 6.69453 13.8946 6.95008 13.639C7.20564 13.3835 7.33341 13.0668 7.33341 12.689C7.33341 12.3112 7.20564 11.9946 6.95008 11.739C6.69453 11.4835 6.37786 11.3557 6.00008 11.3557C5.6223 11.3557 5.30564 11.4835 5.05008 11.739C4.79453 11.9946 4.66675 12.3112 4.66675 12.689C4.66675 13.0668 4.79453 13.3835 5.05008 13.639C5.30564 13.8946 5.6223 14.0223 6.00008 14.0223ZM14.6667 22.0223C15.9556 22.0223 17.0556 21.5668 17.9667 20.6557C18.8779 19.7446 19.3334 18.6446 19.3334 17.3557C19.3334 16.0668 18.8779 14.9668 17.9667 14.0557C17.0556 13.1446 15.9556 12.689 14.6667 12.689C13.3779 12.689 12.2779 13.1446 11.3667 14.0557C10.4556 14.9668 10.0001 16.0668 10.0001 17.3557C10.0001 18.6446 10.4556 19.7446 11.3667 20.6557C12.2779 21.5668 13.3779 22.0223 14.6667 22.0223ZM14.6667 20.0223C15.4001 20.0223 16.0279 19.7612 16.5501 19.239C17.0723 18.7168 17.3334 18.089 17.3334 17.3557C17.3334 16.6223 17.0723 15.9946 16.5501 15.4723C16.0279 14.9501 15.4001 14.689 14.6667 14.689C13.9334 14.689 13.3056 14.9501 12.7834 15.4723C12.2612 15.9946 12.0001 16.6223 12.0001 17.3557C12.0001 18.089 12.2612 18.7168 12.7834 19.239C13.3056 19.7612 13.9334 20.0223 14.6667 20.0223Z" />
        </svg>
    );
}
