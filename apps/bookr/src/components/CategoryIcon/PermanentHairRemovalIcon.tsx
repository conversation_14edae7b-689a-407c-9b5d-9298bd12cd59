import { SVGProps } from 'react';
import { clsx } from '@bookr-technologies/ui/utils';

export function PermanentHairRemovalIcon({ ...rest }: SVGProps<SVGSVGElement>) {
    rest.className = clsx(
        rest.className,
        'CategoryIcon-root',
        'CategoryIcon-permanentHairRemoval',
    );

    return (
        <svg
            width="32"
            height="32"
            viewBox="0 0 32 32"
            fill="currentColor"
            xmlns="http://www.w3.org/2000/svg"
            {...rest}
        >
            <path d="M4 28.0223V8.02234C4 6.91123 4.38889 5.96678 5.16667 5.18901C5.94444 4.41123 6.88889 4.02234 8 4.02234H13.3333C14.4444 4.02234 15.3889 4.41123 16.1667 5.18901C16.9444 5.96678 17.3333 6.91123 17.3333 8.02234V28.0223H4ZM13.3333 20.0223H14.6667V13.3557H13.3333V20.0223ZM6.66667 25.3557H14.6667V22.689H13.3333C12.6 22.689 11.9722 22.4279 11.45 21.9057C10.9278 21.3835 10.6667 20.7557 10.6667 20.0223V13.3557C10.6667 12.6223 10.9278 11.9946 11.45 11.4723C11.9722 10.9501 12.6 10.689 13.3333 10.689H14.6667V8.02234C14.6667 7.64456 14.5389 7.32789 14.2833 7.07234C14.0278 6.81678 13.7111 6.68901 13.3333 6.68901H8C7.62222 6.68901 7.30556 6.81678 7.05 7.07234C6.79444 7.32789 6.66667 7.64456 6.66667 8.02234V25.3557ZM22.9333 18.1223C22.3556 18.1223 21.7889 18.0446 21.2333 17.889C20.6778 17.7335 20.1333 17.5335 19.6 17.289L20.4333 14.7557C20.8778 14.9557 21.3167 15.1223 21.75 15.2557C22.1833 15.389 22.5889 15.4557 22.9667 15.4557C23.2333 15.4557 23.5 15.4112 23.7667 15.3223C24.0333 15.2335 24.3111 15.1001 24.6 14.9223C25.1333 14.5446 25.6667 14.289 26.2 14.1557C26.7333 14.0223 27.2444 13.9557 27.7333 13.9557C28.2889 13.9557 28.8611 14.0279 29.45 14.1723C30.0389 14.3168 30.5889 14.5112 31.1 14.7557L30.2667 17.289C29.7556 17.1112 29.2833 16.9557 28.85 16.8223C28.4167 16.689 28.0444 16.6223 27.7333 16.6223C27.4667 16.6223 27.1722 16.6723 26.85 16.7723C26.5278 16.8723 26.1889 17.0446 25.8333 17.289C25.3667 17.6001 24.8944 17.8168 24.4167 17.939C23.9389 18.0612 23.4444 18.1223 22.9333 18.1223ZM22.9667 12.9223C22.3889 12.9223 21.8111 12.8446 21.2333 12.689C20.6556 12.5335 20.1111 12.3335 19.6 12.089L20.4333 9.55567C21.0111 9.80012 21.5 9.97789 21.9 10.089C22.3 10.2001 22.6556 10.2557 22.9667 10.2557C23.2333 10.2557 23.5 10.2168 23.7667 10.139C24.0333 10.0612 24.3111 9.92234 24.6 9.72234C25.1556 9.34456 25.6944 9.08901 26.2167 8.95567C26.7389 8.82234 27.2444 8.75567 27.7333 8.75567C28.2889 8.75567 28.8444 8.82789 29.4 8.97234C29.9556 9.11678 30.5222 9.31123 31.1 9.55567L30.2667 12.089C29.6889 11.889 29.2 11.7279 28.8 11.6057C28.4 11.4835 28.0444 11.4223 27.7333 11.4223C27.4444 11.4223 27.15 11.4668 26.85 11.5557C26.55 11.6446 26.2111 11.8223 25.8333 12.089C25.4333 12.3779 24.9833 12.589 24.4833 12.7223C23.9833 12.8557 23.4778 12.9223 22.9667 12.9223ZM22.9667 23.3223C22.3889 23.3223 21.8167 23.2446 21.25 23.089C20.6833 22.9335 20.1333 22.7335 19.6 22.489L20.4333 19.9557C20.9222 20.1779 21.3778 20.3501 21.8 20.4723C22.2222 20.5946 22.6111 20.6557 22.9667 20.6557C23.2333 20.6557 23.5 20.6168 23.7667 20.539C24.0333 20.4612 24.3111 20.3223 24.6 20.1223C25.1111 19.7668 25.6556 19.5168 26.2333 19.3723C26.8111 19.2279 27.3222 19.1557 27.7667 19.1557C28.3222 19.1557 28.8889 19.2335 29.4667 19.389C30.0444 19.5446 30.5889 19.7334 31.1 19.9557L30.2667 22.489C29.6889 22.289 29.1944 22.1279 28.7833 22.0057C28.3722 21.8835 28.0222 21.8223 27.7333 21.8223C27.4222 21.8223 27.1056 21.8723 26.7833 21.9723C26.4611 22.0723 26.1444 22.2446 25.8333 22.489C25.4556 22.7557 25.0167 22.9612 24.5167 23.1057C24.0167 23.2501 23.5 23.3223 22.9667 23.3223Z" />
        </svg>
    );
}
