import { SVGProps } from 'react';
import { clsx } from '@bookr-technologies/ui/utils';

export function DentistryIcon({ ...rest }: SVGProps<SVGSVGElement>) {
    rest.className = clsx(
        rest.className,
        'CategoryIcon-root',
        'CategoryIcon-denistry',
    );

    return (
        <svg
            width="32"
            height="32"
            viewBox="0 0 32 32"
            fill="currentColor"
            xmlns="http://www.w3.org/2000/svg"
            {...rest}
        >
            <path d="M21.8584 4.08014C23.2248 4.08014 24.3863 4.5584 25.3428 5.51492C26.2994 6.47145 26.7776 7.63294 26.7776 8.9994C26.7776 9.21413 26.763 9.50206 26.7337 9.86319C26.7044 10.2243 26.6605 10.6489 26.6019 11.1369L24.9915 22.9373C24.8743 23.7767 24.4937 24.4355 23.8495 24.9138C23.2053 25.392 22.5123 25.6312 21.7705 25.6312C21.3215 25.6312 20.8872 25.5384 20.4675 25.353C20.0478 25.1676 19.6915 24.8601 19.3987 24.4306L16.2656 19.8628C16.2266 19.8628 16.1875 19.8432 16.1485 19.8042C16.1095 19.7652 16.0509 19.7456 15.9728 19.7456L15.68 19.9213L12.6347 24.3428C12.3419 24.7918 11.9759 25.1187 11.5367 25.3237C11.0975 25.5287 10.6436 25.6312 10.1751 25.6312C9.43332 25.6312 8.74521 25.392 8.11079 24.9138C7.47636 24.4355 7.11034 23.7669 7.01274 22.908L5.43155 11.1369C5.35346 10.6489 5.30466 10.2243 5.28514 9.86319C5.26562 9.50206 5.25586 9.21413 5.25586 8.9994C5.25586 7.63294 5.72924 6.47145 6.676 5.51492C7.62276 4.5584 8.76961 4.08014 10.1166 4.08014C10.8583 4.08014 11.4537 4.17286 11.9027 4.35831C12.3517 4.54376 12.7714 4.75361 13.1618 4.98786C13.5327 5.20259 13.928 5.3978 14.3477 5.57349C14.7674 5.74917 15.3189 5.83702 16.0021 5.83702C16.6853 5.83702 17.2368 5.74917 17.6565 5.57349C18.0762 5.3978 18.4715 5.20259 18.8424 4.98786C19.2328 4.75361 19.6525 4.54376 20.1015 4.35831C20.5505 4.17286 21.1361 4.08014 21.8584 4.08014ZM21.8584 6.86186C21.5655 6.86186 21.302 6.9009 21.0678 6.97899C20.8335 7.05707 20.5895 7.1742 20.3357 7.33036C19.6525 7.72078 18.9937 8.03311 18.3592 8.26736C17.7248 8.50161 16.9391 8.61874 16.0021 8.61874C15.0651 8.61874 14.2843 8.50161 13.6596 8.26736C13.0349 8.03311 12.381 7.72078 11.6977 7.33036C11.444 7.1742 11.1951 7.05707 10.9511 6.97899C10.7071 6.9009 10.4386 6.86186 10.1458 6.86186C9.56021 6.86186 9.05267 7.07171 8.62321 7.49141C8.19375 7.91111 7.98878 8.41377 8.0083 8.9994C8.02782 9.23365 8.04246 9.4923 8.05222 9.77535C8.06198 10.0584 8.08638 10.3561 8.12543 10.6684L9.64805 21.5904C9.66757 21.8051 9.75542 21.9661 9.91159 22.0735C10.0678 22.1809 10.2239 22.2345 10.3801 22.2345C10.4777 22.2345 10.5753 22.2053 10.6729 22.1467C10.7705 22.0881 10.8681 22.0101 10.9657 21.9124L13.3961 18.3401C13.6889 17.8911 14.0695 17.5544 14.538 17.3299C15.0065 17.1054 15.4946 16.9932 16.0021 16.9932C16.5096 16.9932 16.9928 17.1103 17.4515 17.3446C17.9103 17.5788 18.2958 17.9204 18.6081 18.3694L21.0385 21.9417C21.1166 22.0589 21.2093 22.1418 21.3167 22.1906C21.424 22.2394 21.5265 22.2638 21.6241 22.2638C21.7607 22.2638 21.9072 22.2053 22.0633 22.0881C22.2195 21.971 22.3171 21.8246 22.3561 21.6489L23.8788 10.6684C23.9178 10.278 23.9471 9.94128 23.9666 9.65823C23.9861 9.37517 23.9959 9.15556 23.9959 8.9994C23.9959 8.41377 23.786 7.91111 23.3663 7.49141C22.9466 7.07171 22.444 6.86186 21.8584 6.86186Z" />
        </svg>
    );
}
