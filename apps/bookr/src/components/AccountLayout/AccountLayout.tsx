import Container from '@mui/material/Container';
import Grid from '@mui/material/Grid';
import { Theme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import { PropsWithChildren } from 'react';
import { Content } from './Content';
import { Sidebar } from './Sidebar';

export function AccountLayout({ children }: PropsWithChildren) {
    const isDownMd = useMediaQuery<Theme>((theme) =>
        theme.breakpoints.down('md'),
    );

    return (
        <Container
            disableGutters={isDownMd}
            sx={{ flexGrow: 1, display: 'flex' }}
        >
            <Grid
                container
                gap={{ md: 2.5 }}
                pt={{ md: 5 }}
                pb={3}
                flexWrap={'nowrap'}
                flexGrow={1}
                flexDirection={{
                    xs: 'column',
                    md: 'row',
                }}
            >
                <Sidebar />
                <Content>{children}</Content>
            </Grid>
        </Container>
    );
}
