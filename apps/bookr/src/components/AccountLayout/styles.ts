import List from '@mui/material/List';
import MuiTabs from '@mui/material/Tabs';
import { alpha, styled } from '@mui/material/styles';
import { palette } from '@bookr-technologies/ui';

export const StyledSidebar = styled(List)(({ theme }) => ({
    width: '100%',
    maxWidth: 240,

    '.MuiListItemIcon-root': {
        minWidth: 0,
        marginRight: theme.spacing(1),
    },

    '.MuiListItemButton-root': {
        padding: theme.spacing(2),
        borderRadius: 12,
        marginBottom: theme.spacing(1),
        '&.Mui-selected': {
            backgroundColor: palette.extended.backgroundPrimary,

            '.MuiListItemIcon-root': {
                color: palette.foundation.accent,
            },
        },
    },

    '.MuiListItemText-root': {
        margin: 0,
    },
}));

export const StyledTabs = styled(MuiTabs, { name: 'StyledTabs' })(
    ({ theme }) => ({
        backgroundColor: alpha(theme.palette.background.paper, 0.8),
        backdropFilter: 'blur(12px) saturate(180%)',
        borderRadius: 0,
        position: 'sticky',
        top: 'var(--header-height)',
        zIndex: 3,
        borderBottom: `1px solid ${theme.palette.divider}`,
        '&, .MuiTab-root': {
            minHeight: 0,
        },
        '.MuiTab-root': {
            padding: theme.spacing(2, 2),
            minWidth: 0,
            fontSize: theme.typography.subhead.fontSize,
            fontWeight: 600,
            textTransform: 'capitalize',
            color: theme.palette.text.secondary,
            '&.Mui-selected': {
                color: theme.palette.text.primary,
                '.MuiSvgIcon-root': {
                    color: theme.palette.info.main,
                },
            },
        },
        '.MuiTabs-indicator': {
            display: 'none',
        },
    }),
);
