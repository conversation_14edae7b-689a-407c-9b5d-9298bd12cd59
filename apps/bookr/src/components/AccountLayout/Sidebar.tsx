import LockIcon from '@mui/icons-material/Lock';
import LogoutIcon from '@mui/icons-material/Logout';
import PersonIcon from '@mui/icons-material/Person';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import Tab from '@mui/material/Tab';
import { TypographyProps } from '@mui/material/Typography';
import { Theme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import Link from 'next/link';
import { NextRouter, useRouter } from 'next/router';
import { MouseEvent, useMemo } from 'react';
import { useI18n } from '@bookr-technologies/i18n';
import { signOut } from '~/lib/session';
import { StyledSidebar, StyledTabs } from './styles';

const primaryProps: TypographyProps<'span', { component?: 'span' }> = {
    variant: 'subhead',
    fontWeight: 600,
};

const buildSections = (router: NextRouter) => {
    const linkProps = (href: string) => ({
        href,
        selected: router.asPath === href,
        onClick: (e: MouseEvent) => {
            e.preventDefault();
            router.push(href);
        },
    });

    return [
        {
            label: 'personalDetails',
            icon: <PersonIcon />,
            ...linkProps('/account/profile'),
        },
        {
            label: 'security',
            icon: <LockIcon />,
            ...linkProps('/account/security'),
        },
        // {
        //     label: 'feedback',
        //     icon: <FavoriteIcon />,
        //     ...linkProps('/account/feedback'),
        // },
        {
            label: 'signOut',
            icon: <LogoutIcon />,
            href: null,
            selected: false,
            async onClick(e: MouseEvent) {
                e.preventDefault();
                await signOut();
                await router.push('/');
            },
        },
    ] as const;
};

export function Sidebar() {
    const { t } = useI18n();
    const router = useRouter();
    const sections = useMemo(() => buildSections(router), [router]);
    const isDownMd = useMediaQuery<Theme>((theme) =>
        theme.breakpoints.down('md'),
    );

    if (isDownMd) {
        return (
            <StyledTabs
                value={sections.findIndex(({ href }) => router.asPath === href)}
                variant="scrollable"
                scrollButtons="auto"
            >
                {sections.map(({ label, icon, onClick, href }, index) => (
                    <Tab
                        key={label}
                        label={t(label)}
                        icon={icon}
                        value={index}
                        iconPosition={'start'}
                        onClick={onClick}
                        {...(href ? { component: 'a', href } : {})}
                    />
                ))}
            </StyledTabs>
        );
    }

    return (
        <StyledSidebar disablePadding>
            {sections.map(({ label, icon, selected, href, onClick }) => (
                <ListItemButton
                    key={label}
                    selected={selected}
                    {...(href ? { LinkComponent: Link, href } : { onClick })}
                >
                    <ListItemIcon>{icon}</ListItemIcon>
                    <ListItemText
                        primary={t(label)}
                        primaryTypographyProps={primaryProps}
                    />
                </ListItemButton>
            ))}
        </StyledSidebar>
    );
}
