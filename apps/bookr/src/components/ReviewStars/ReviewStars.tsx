import StarRateIcon from '@mui/icons-material/StarRateRounded';
import { RatingProps } from '@mui/material/Rating';
import { StyledRating } from './styles';

export function ReviewStars({ ...rest }: RatingProps) {
    return (
        <StyledRating
            precision={0.5}
            readOnly
            icon={<StarRateIcon fontSize="inherit" />}
            emptyIcon={<StarRateIcon fontSize="inherit" />}
            {...rest}
        />
    );
}
