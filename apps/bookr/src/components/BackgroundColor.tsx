import GlobalStyles from '@mui/material/GlobalStyles';
import { useMemo } from 'react';

interface Props {
    color: string;
}

export function BackgroundColor({ color }: Props) {
    return useMemo(() => {
        return (
            <GlobalStyles
                styles={{
                    body: {
                        backgroundColor: color,
                    },
                }}
            />
        );
    }, [color]);
}
