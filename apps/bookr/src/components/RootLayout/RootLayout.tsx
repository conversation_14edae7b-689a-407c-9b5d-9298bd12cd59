import Stack from '@mui/material/Stack';
import { Plus_Jakarta_Sans } from 'next/font/google';
import Head from 'next/head';
import { isAnalyticsEnabled } from '@bookr-technologies/analytics';
import { CookieBanner } from '~/components/CookieBanner';
import { Footer } from '~/components/Footer';
import { Header } from '~/components/Header';
import { StyledRootLayout } from './styles';
import { RootLayoutProps } from './types';

const font = Plus_Jakarta_Sans({
    subsets: ['latin'],
});

export function RootLayout({ children }: RootLayoutProps) {
    return (
        <StyledRootLayout suppressHydrationWarning className={font.className}>
            <Head>
                <meta name="theme-color" content={'#ffffff'} />
            </Head>
            {isAnalyticsEnabled() ? <CookieBanner /> : null}

            <Header />
            <Stack flexGrow={1} minHeight={'80vh'}>
                {children}
            </Stack>
            <Footer />
        </StyledRootLayout>
    );
}
