import HighlightOffIcon from '@mui/icons-material/HighlightOff';
import Button from '@mui/material/Button';
import Dialog, { DialogProps } from '@mui/material/Dialog';
import ListItem from '@mui/material/ListItem';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { Theme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import { useEvent } from '@bookr-technologies/hooks';
import { useI18n } from '@bookr-technologies/i18n';
import { useDeleteUserMutation } from '~/api';
import { StyledList } from '~/components/DeleteAccountDialog/styles';
import { signOut } from '~/lib/session';

export function DeleteAccountDialog({ ...rest }: DialogProps) {
    const { t } = useI18n();
    const deleteUser = useDeleteUserMutation();
    const isFullscreen = useMediaQuery((theme: Theme) =>
        theme.breakpoints.down('sm'),
    );

    const handleClose = useEvent(() => rest.onClose?.({}, 'escapeKeyDown'));
    const handleDelete = useEvent(async () => {
        handleClose();
        await deleteUser.mutateAsync();
        await signOut();
        window.location.href = '/';
    });

    return (
        <Dialog fullScreen={isFullscreen} fullWidth maxWidth={'sm'} {...rest}>
            <Stack p={4} gap={3}>
                <Stack gap={1}>
                    <Typography variant={'title1'} fontWeight={800}>
                        {t('deleteAccount')}
                    </Typography>
                    <Typography
                        variant={'subhead'}
                        fontWeight={600}
                        color={'textSecondary'}
                    >
                        {t('deleteAccountMessage')}
                    </Typography>
                </Stack>
                <StyledList>
                    {t('deleteAccountDetails').map((detail) => (
                        <ListItem alignItems={'flex-start'} key={detail}>
                            <ListItemIcon>
                                <HighlightOffIcon color={'error'} />
                            </ListItemIcon>
                            <ListItemText
                                primary={detail}
                                primaryTypographyProps={{
                                    variant: 'subhead',
                                    fontWeight: 600,
                                    color: 'textSecondary',
                                    lineHeight: '24px',
                                }}
                            />
                        </ListItem>
                    ))}
                </StyledList>
                <Stack gap={1}>
                    <Button
                        variant={'subtle'}
                        size={'large'}
                        color={'primary'}
                        onClick={handleDelete}
                    >
                        {t('deleteAccount')}
                    </Button>
                    <Button
                        variant={'contained'}
                        size={'large'}
                        color={'info'}
                        onClick={handleClose}
                    >
                        {t('keepMyAccountActive')}
                    </Button>
                </Stack>
            </Stack>
        </Dialog>
    );
}
