import Slide from '@mui/material/Slide';
import { useAtom } from 'jotai';
import { useEffect, useState } from 'react';
import { useEvent } from '@bookr-technologies/hooks';
import { setAccessToken } from '~/api';
import { AuthFormCreatePasswordStep } from './AuthFormCreatePasswordStep';
import { AuthFormDefaultStep } from './AuthFormDefaultStep';
import { AuthFormHeader } from './AuthFormHeader';
import { AuthFormLoginStep } from './AuthFormLoginStep';
import { AuthFormSetupStep } from './AuthFormSetupStep';
import { AuthFormSignUpStep } from './AuthFormSignUpStep';
import { AuthFormSteps } from './AuthFormSteps';
import { FormFooter } from './FormFooter';
import { emailAtom } from './store';
import { StyledAuthForm } from './styles';

interface Props {
    required?: boolean;
    skipSetup?: boolean;
    defaultStep?: AuthFormSteps;

    onClose?(): void;

    onSuccess?(): void;
}

export function AuthForm({
    skipSetup,
    onClose,
    onSuccess,
    required,
    defaultStep,
}: Props) {
    const [step, setStep] = useState(defaultStep || AuthFormSteps.Default);
    const [, setEmail] = useAtom(emailAtom);

    const handleBack = useEvent(() => setStep(AuthFormSteps.Default));
    const handleSuccess = useEvent(async (token: string) => {
        await setAccessToken(token);
        await onSuccess?.();
    });

    useEffect(
        () => () => {
            setStep(defaultStep || AuthFormSteps.Default);
            setEmail('');
        },
        [defaultStep, setEmail],
    );

    return (
        <StyledAuthForm>
            <AuthFormHeader
                onClose={onClose}
                onBack={
                    ![AuthFormSteps.Default, AuthFormSteps.Setup].includes(step)
                        ? handleBack
                        : undefined
                }
                required={required}
            />
            <Slide
                in={step === AuthFormSteps.Default}
                direction={'left'}
                unmountOnExit
                exit={false}
            >
                <AuthFormDefaultStep
                    onClose={onClose}
                    onStep={setStep}
                    onSuccess={handleSuccess}
                />
            </Slide>

            <Slide
                in={step === AuthFormSteps.SignIn}
                direction={'left'}
                unmountOnExit
                exit={false}
            >
                <AuthFormLoginStep onStep={setStep} onSuccess={handleSuccess} />
            </Slide>

            <Slide
                in={step === AuthFormSteps.CreatePassword}
                direction={'left'}
                unmountOnExit
                exit={false}
            >
                <AuthFormCreatePasswordStep onStep={setStep} />
            </Slide>

            <Slide
                in={step === AuthFormSteps.SignUp}
                direction={'left'}
                unmountOnExit
                exit={false}
            >
                <AuthFormSignUpStep
                    skipSetup={skipSetup}
                    onStep={setStep}
                    onSuccess={handleSuccess}
                />
            </Slide>
            <Slide
                in={step === AuthFormSteps.Setup}
                direction={'left'}
                unmountOnExit
                exit={false}
            >
                <AuthFormSetupStep onStep={setStep} onSuccess={handleSuccess} />
            </Slide>
            <FormFooter />
        </StyledAuthForm>
    );
}
