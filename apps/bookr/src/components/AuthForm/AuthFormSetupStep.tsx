import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { DisplayContent } from 'bookr/src/components/DisplayContent';
import Image from 'next/image';
import { forwardRef } from 'react';
import { getClientConfig } from '@bookr-technologies/config';
import { getAuth } from '@bookr-technologies/firebase';
import { useEvent } from '@bookr-technologies/hooks';
import { useI18n } from '@bookr-technologies/i18n';
import { AccountTypeEnum } from '@bookr-technologies/sdk';
import { useUpdateUserMutation } from '~/api';
import { AuthFormSteps } from './AuthFormSteps';
import profileBusinessImage from './assets/profileBusinessImage.png';
import profileClientImage from './assets/profileClientImage.png';
import { StyledProfileButton } from './styles';

interface Props {
    onStep(step: number): void;
    onSuccess(token: string): void;
}

export const AuthFormSetupStep = forwardRef<HTMLDivElement, Props>(
    function AuthFormSetupStep({ onStep, onSuccess }, ref) {
        const updateUserMutation = useUpdateUserMutation();
        const { t } = useI18n();

        const handleClientClick = useEvent(() =>
            updateUserMutation
                .mutateAsync({ accountType: AccountTypeEnum.CLIENT })
                .then(async () => {
                    const user = getAuth().currentUser;
                    if (user) {
                        const token = await user.getIdToken();
                        if (token) {
                            onSuccess(token);
                            return;
                        }
                    }

                    onStep(AuthFormSteps.Default);
                }),
        );

        const handleBusinessClick = useEvent(() =>
            updateUserMutation
                .mutateAsync({
                    accountType: AccountTypeEnum.BUSINESS_OWNER,
                })
                .then(async () => {
                    const user = getAuth().currentUser;
                    if (user) {
                        const token = await user.getIdToken();
                        if (token) {
                            window.location.href =
                                getClientConfig().urls.dashboard;
                            return;
                        }
                    }

                    onStep(AuthFormSteps.Default);
                }),
        );

        return (
            <Stack flexGrow={1} px={4} ref={ref}>
                <Typography
                    variant={'largeTitle'}
                    fontWeight={800}
                    component={'h3'}
                    mb={1}
                >
                    {t('authDialogSetupTitle')}
                </Typography>
                <Typography
                    variant={'callout'}
                    fontWeight={600}
                    color={'textSecondary'}
                    component={'p'}
                >
                    {t('authDialogSetupSubtitle')}
                </Typography>

                <Stack spacing={3} my={3}>
                    <DisplayContent loading={updateUserMutation.isLoading}>
                        <StyledProfileButton onClick={handleClientClick}>
                            <Stack gap={1}>
                                <Typography variant={'title3'} fontWeight={800}>
                                    {t('accountClientName')}
                                </Typography>
                                <Typography
                                    variant={'footnote'}
                                    fontWeight={600}
                                    color={'textSecondary'}
                                >
                                    {t('accountClientDetails')}
                                </Typography>
                            </Stack>
                            <Image
                                className={'ProfileButton-typeImage'}
                                src={profileClientImage}
                                alt={'Client'}
                            />
                        </StyledProfileButton>

                        <StyledProfileButton onClick={handleBusinessClick}>
                            <Stack gap={1}>
                                <Typography variant={'title3'} fontWeight={800}>
                                    {t('accountBusinessName')}
                                </Typography>
                                <Typography
                                    variant={'footnote'}
                                    fontWeight={600}
                                    color={'textSecondary'}
                                >
                                    {t('accountBusinessDetails')}
                                </Typography>
                            </Stack>
                            <Image
                                className={'ProfileButton-typeImage'}
                                src={profileBusinessImage}
                                alt={'Business'}
                            />
                        </StyledProfileButton>
                    </DisplayContent>
                </Stack>
            </Stack>
        );
    },
);
