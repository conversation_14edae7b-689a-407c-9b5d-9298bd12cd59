import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { Form, Formik } from 'formik';
import { forwardRef } from 'react';
import { silentPromise, retry } from '@bookr-technologies/core';
import { getAuth, isFirebaseError } from '@bookr-technologies/firebase';
import { useEvent, useValidationSchema } from '@bookr-technologies/hooks';
import { DefaultLanguage, useI18n } from '@bookr-technologies/i18n';
import { AccountTypeEnum } from '@bookr-technologies/sdk';
import {
    FormikButton,
    FormikPhoneNumber,
    FormikTextField,
    FormikLanguage,
} from '@bookr-technologies/ui/components';
import { useCreateUserMutation } from '~/api';
import { AuthFormSteps } from './AuthFormSteps';

interface Props {
    skipSetup?: boolean;
    onStep(step: number): void;
    onSuccess(token: string): void;
}

const initialValues = {
    displayName: '',
    phoneNumber: '',
    language: DefaultLanguage.code,
};

export const AuthFormSignUpStep = forwardRef<HTMLDivElement, Props>(
    function AuthFormSignUpStep({ skipSetup, onStep, onSuccess }, ref) {
        const { t } = useI18n();
        const createUserMutation = useCreateUserMutation();

        const validation = useValidationSchema((yup) => ({
            displayName: yup.string().required(t('requiredField')),
            phoneNumber: yup.string().required(t('requiredField')),
            language: yup.string().required(t('requiredField')),
        }));

        const handleSubmit = useEvent(async (values: typeof initialValues) => {
            const user = await retry(() => {
                const value = getAuth().currentUser;
                if (!value?.uid) {
                    throw new Error('User not logged in');
                }

                return value;
            });

            if (!user?.uid || !user?.email) {
                onStep(AuthFormSteps.Default);
                return;
            }

            const input = {
                uid: user.uid,
                email: user.email,
                displayName: values.displayName,
                phoneNumber: values.phoneNumber,
                language: values.language,
                accountType: AccountTypeEnum.INVALID,
            };

            await silentPromise(createUserMutation.mutateAsync(input));

            try {
                const token = await user.getIdToken();

                onSuccess(token);
            } catch (error) {
                if (isFirebaseError(error)) {
                    onStep(AuthFormSteps.Default);
                }
            }

            if (!skipSetup) {
                onStep(AuthFormSteps.Setup);
            }
        });

        return (
            <Stack flexGrow={1} p={4} ref={ref}>
                <Typography
                    variant={'largeTitle'}
                    fontWeight={800}
                    component={'h3'}
                    mb={1}
                >
                    {t('authDialogSignupTitle')}
                </Typography>
                <Typography
                    variant={'callout'}
                    fontWeight={600}
                    color={'textSecondary'}
                    component={'p'}
                >
                    {t('authDialogSignupSubtitle')}
                </Typography>

                <Formik
                    initialValues={initialValues}
                    onSubmit={handleSubmit}
                    {...validation}
                >
                    <Stack component={Form} spacing={3} my={3}>
                        <Stack spacing={2}>
                            <FormikTextField
                                name={'displayName'}
                                label={t('displayName')}
                            />
                            <FormikPhoneNumber
                                name={'phoneNumber'}
                                label={t('phoneNumber')}
                            />
                            <FormikLanguage
                                name={'language'}
                                label={t('language')}
                            />
                        </Stack>

                        <FormikButton
                            type={'submit'}
                            variant={'contained'}
                            color={'info'}
                            size={'large'}
                            fullWidth
                            validateDirty
                        >
                            {t('continue')}
                        </FormikButton>
                    </Stack>
                </Formik>
            </Stack>
        );
    },
);
