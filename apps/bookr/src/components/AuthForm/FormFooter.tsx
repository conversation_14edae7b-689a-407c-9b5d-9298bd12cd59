import Link from '@mui/material/Link';
import Typography from '@mui/material/Typography';
import { useTheme } from '@mui/material/styles';

export function FormFooter() {
    const theme = useTheme();

    return (
        <Typography
            variant={'caption1'}
            fontWeight={600}
            align={'center'}
            color={'textSecondary'}
        >
            By continuing, you agree with our
            <br />
            <Link
                href={'/documents/privacy-policy.pdf'}
                color={theme.palette.info.main}
            >
                Privacy Policy
            </Link>{' '}
            and{' '}
            <Link
                href={'/documents/terms-and-conditions.pdf'}
                color={theme.palette.info.main}
            >
                Terms and Conditions
            </Link>
            🔒
        </Typography>
    );
}
