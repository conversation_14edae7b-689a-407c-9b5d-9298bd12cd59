import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import CloseIcon from '@mui/icons-material/Close';
import Grid from '@mui/material/Grid';
import IconButton from '@mui/material/IconButton';

interface Props {
    required?: boolean;

    onBack?(): void;

    onClose?(): void;
}

export function AuthFormHeader({ onBack, onClose, required }: Props) {
    if (!onBack && !onClose) {
        return <Grid mt={4} />;
    }

    return (
        <Grid
            container
            justifyContent={onBack ? 'space-between' : 'flex-end'}
            px={3}
            pt={2}
        >
            {onBack ? (
                <IconButton onClick={onBack}>
                    <ArrowBackIcon />
                </IconButton>
            ) : null}
            {!required && onClose ? (
                <IconButton onClick={onClose}>
                    <CloseIcon />
                </IconButton>
            ) : null}
        </Grid>
    );
}
