import Button from '@mui/material/Button';
import ButtonBase from '@mui/material/ButtonBase';
import Stack from '@mui/material/Stack';
import { styled } from '@mui/material/styles';
import { palette } from '@bookr-technologies/ui';

export const StyledAuthForm = styled(Stack, { name: 'StyledAuthForm' })(
    ({ theme }) => ({
        width: '100%',
        maxWidth: 600,
        paddingBottom: theme.spacing(4),
        overflow: 'hidden',
        flexGrow: 1,
    }),
);

export const StyledSocialButton = styled(Button, {
    name: 'StyledSocialButton',
})(({ theme }) => ({
    paddingRight: theme.spacing(7),
    paddingLeft: theme.spacing(7),
    fontWeight: 600,

    '.MuiButton-startIcon': {
        margin: 0,
        position: 'absolute',
        top: 0,
        left: 0,
        width: theme.spacing(7),
        height: theme.spacing(7),
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
    },
}));

export const StyledProfileButton = styled(ButtonBase)(({ theme }) => ({
    fontWeight: 'unset',
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
    backgroundColor: palette.extended.backgroundSecondary,
    borderRadius: 12,
    textAlign: 'left',
    padding: theme.spacing(3),
    position: 'relative',
    overflow: 'hidden',
    '.MuiStack-root': {
        position: 'relative',
        zIndex: 3,
        width: '74%',
    },
    '.ProfileButton-typeImage': {
        zIndex: 1,
        position: 'absolute',
        top: '50%',
        right: 0,
        transform: 'translateY(-50%)',
        height: '150%',
        maxWidth: '36%',
        objectFit: 'contain',
        objectPosition: 'right center',
        pointerEvents: 'none',
        [theme.breakpoints.down(500)]: {
            opacity: 0.2,
            maxWidth: '100%',
        },
    },
}));
