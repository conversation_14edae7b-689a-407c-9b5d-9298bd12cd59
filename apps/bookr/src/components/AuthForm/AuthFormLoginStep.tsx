import Grid from '@mui/material/Grid';
import Link from '@mui/material/Link';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { isAxiosError } from 'axios';
import { getUserConfigStep } from 'bookr/src/lib/utilities/user';
import { Form, Formik, FormikProps } from 'formik';
import { useAtom } from 'jotai';
import { forwardRef, useRef } from 'react';
import { setUser } from '@bookr-technologies/analytics';
import { str } from '@bookr-technologies/core';
import {
    FirebaseErrorCode,
    isFirebaseError,
    matchFirebaseError,
    signInWithEmailAndPassword,
} from '@bookr-technologies/firebase';
import { useEvent, useValidationSchema } from '@bookr-technologies/hooks';
import { useI18n } from '@bookr-technologies/i18n';
import {
    FormikButton,
    FormikHiddenField,
    FormikTextField,
} from '@bookr-technologies/ui/components';
import { bookrClient, setAccessToken, userQueries } from '~/api';
import { AuthFormSteps } from './AuthFormSteps';
import { emailAtom } from './store';
import { useForgotPassword } from './useForgotPassword';

interface Props {
    onSuccess(token: string): void;
    onStep(step: AuthFormSteps): void;
}

const initialValues = {
    email: '',
    password: '',
};

const TextFieldReadOnlyStyle = {
    '& .MuiInputBase-input': {
        padding: '16px',
    },
};

export const AuthFormLoginStep = forwardRef<HTMLDivElement, Props>(
    function AuthFormLoginStep({ onSuccess, onStep }, ref) {
        const formik = useRef<FormikProps<typeof initialValues>>(null);
        const { t } = useI18n();
        const handleForgotPassword = useForgotPassword(formik);

        const validation = useValidationSchema((yup) => ({
            password: yup.string().required(t('requiredField')),
        }));

        const [email] = useAtom(emailAtom);

        const handleSubmit = useEvent(
            async ({ password }: typeof initialValues) => {
                try {
                    const { user } = await signInWithEmailAndPassword(
                        email,
                        password,
                    );
                    const token = await user.getIdToken();
                    setAccessToken(token);
                    await setUser({
                        uid: user.uid,
                        email: str(user.email),
                        phone: str(user.phoneNumber),
                        displayName: str(user.displayName),
                        createdAt: user.metadata.creationTime,
                    });

                    let configStep = -1;
                    try {
                        const data = await userQueries.getUser(
                            bookrClient,
                            user.uid,
                        );
                        configStep = getUserConfigStep(data);
                    } catch (e) {
                        if (isAxiosError(e) && e.status === 404) {
                            configStep = AuthFormSteps.SignUp;
                        }
                    }

                    if (configStep > -1) {
                        onStep(AuthFormSteps.Setup);
                        return;
                    }

                    onSuccess(token);
                } catch (error) {
                    if (isFirebaseError(error)) {
                        formik.current?.setFieldError(
                            'password',
                            matchFirebaseError(error, {
                                [FirebaseErrorCode.WrongPassword]:
                                    t('wrongPassword'),
                                [FirebaseErrorCode.TooManyRequests]:
                                    t('tooManyRequests'),
                                default: t('somethingWentWrong'),
                            }),
                        );
                    } else {
                        formik.current?.setFieldError(
                            'password',
                            t('somethingWentWrong'),
                        );
                    }
                }
            },
        );

        return (
            <Stack flexGrow={1} px={4} ref={ref}>
                <Typography
                    variant={'largeTitle'}
                    fontWeight={800}
                    component={'h3'}
                    mb={1}
                >
                    {t('authDialogLoginTitle')}
                </Typography>
                <Typography
                    variant={'callout'}
                    fontWeight={600}
                    color={'textSecondary'}
                    component={'p'}
                >
                    {t('authDialogLoginSubtitle')}
                </Typography>

                <Formik
                    initialValues={initialValues}
                    onSubmit={handleSubmit}
                    innerRef={formik}
                    {...validation}
                >
                    <Stack component={Form} spacing={3} my={3}>
                        <Stack spacing={2}>
                            <FormikHiddenField value={email} name={'email'} />
                            <FormikTextField
                                name={'email'}
                                disabled
                                sx={TextFieldReadOnlyStyle}
                                autoComplete={'username'}
                            />
                            <FormikTextField
                                type={'password'}
                                name={'password'}
                                label={t('enterYourPassword')}
                                autoComplete={'new-password'}
                            />

                            <Grid
                                container
                                justifyContent={'flex-end'}
                                mb={3}
                                mt={1}
                            >
                                <Link
                                    variant={'caption'}
                                    color={'textSecondary'}
                                    fontWeight={700}
                                    underline={'hover'}
                                    onClick={handleForgotPassword}
                                >
                                    {t('didYouForgotYourPassword')}
                                </Link>
                            </Grid>
                        </Stack>

                        <FormikButton
                            type={'submit'}
                            variant={'contained'}
                            color={'info'}
                            size={'large'}
                            fullWidth
                            validateDirty
                        >
                            {t('continue')}
                        </FormikButton>
                    </Stack>
                </Formik>
            </Stack>
        );
    },
);
