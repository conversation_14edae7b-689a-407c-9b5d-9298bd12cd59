import { FormikProps } from 'formik';
import { useAtom } from 'jotai';
import { RefObject } from 'react';
import {
    captureError,
    track,
    TrackEvents,
} from '@bookr-technologies/analytics';
import {
    isFirebaseError,
    sendPasswordResetEmail,
} from '@bookr-technologies/firebase';
import { useEvent, useNotification } from '@bookr-technologies/hooks';
import { useI18n } from '@bookr-technologies/i18n';
import { AuthFormSteps } from './AuthFormSteps';
import { emailAtom } from './store';

export function useForgotPassword<T extends { email: string }>(
    formikRef: RefObject<FormikProps<T>>,
    onStep?: (step: AuthFormSteps) => void,
) {
    const { t } = useI18n();
    const [, setEmail] = useAtom(emailAtom);
    const notification = useNotification();

    return useEvent(async () => {
        if (!formikRef.current) {
            return;
        }

        const { values, setSubmitting, setFieldError, setTouched } =
            formikRef.current;

        setTouched({ email: true } as any);
        if (!values.email) {
            setFieldError('email', t('requiredField'));
            return;
        }

        try {
            setSubmitting(true);
            await sendPasswordResetEmail(formikRef.current.values.email);
            track(TrackEvents.ForgotPassword, { email: values.email });
            notification.success(t('forgotPasswordSuccess'));
            setEmail(values.email);
            onStep?.(AuthFormSteps.SignIn);
        } catch (error) {
            setFieldError('email', t('forgotPasswordError'));
            if (!isFirebaseError(error)) {
                captureError(error);
            }
        } finally {
            setSubmitting(false);
        }
    });
}
