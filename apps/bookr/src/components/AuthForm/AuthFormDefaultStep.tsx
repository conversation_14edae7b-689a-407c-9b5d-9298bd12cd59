import AppleIcon from '@mui/icons-material/Apple';
import FacebookIcon from '@mui/icons-material/Facebook';
import GoogleIcon from '@mui/icons-material/Google';
import Grid from '@mui/material/Grid';
import Link from '@mui/material/Link';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { UserCredential } from 'firebase/auth';
import { Form, Formik, FormikProps } from 'formik';
import { useAtom } from 'jotai';
import { forwardRef, useRef } from 'react';
import { setUser } from '@bookr-technologies/analytics';
import { str } from '@bookr-technologies/core';
import {
    signInWithFacebook,
    signInWithGoogle,
} from '@bookr-technologies/firebase';
import {
    RemoteConfigFlag,
    useEvent,
    useFeatureFlag,
    useValidationSchema,
} from '@bookr-technologies/hooks';
import { useI18n } from '@bookr-technologies/i18n';
import {
    FormikButton,
    FormikTextField,
} from '@bookr-technologies/ui/components';
import { bookrClient } from '~/api';
import { AuthFormSteps } from './AuthFormSteps';
import { emailAtom } from './store';
import { StyledSocialButton } from './styles';
import { useForgotPassword } from './useForgotPassword';

interface Props {
    onStep(step: number): void;

    onSuccess(token: string): void;

    onClose?(event: any): void;
}

const initialValues = {
    email: '',
};

export const AuthFormDefaultStep = forwardRef<HTMLDivElement, Props>(
    function AuthFormDefaultStep({ onStep, onSuccess }, ref) {
        const { t } = useI18n();
        const socialLoginEnabled = useFeatureFlag(
            RemoteConfigFlag.SocialLoginEnabled,
        );
        const validationProps = useValidationSchema(
            (yup) => ({
                email: yup
                    .string()
                    .required(t('requiredField'))
                    .email(t('invalidEmail')),
            }),
            [t],
        );

        const [, setEmail] = useAtom(emailAtom);
        const formikRef = useRef<FormikProps<typeof initialValues>>(null);
        const handleForgotPassword = useForgotPassword(formikRef, onStep);

        const handleSubmit = useEvent(
            async ({ email }: typeof initialValues) => {
                const { data } = await bookrClient.users.getMetadata({ email });
                setEmail(email);
                onStep(
                    data?.uid
                        ? AuthFormSteps.SignIn
                        : AuthFormSteps.CreatePassword,
                );
            },
        );

        const socialLogin = async (handler: () => Promise<UserCredential>) => {
            const { user } = await handler();

            await setUser({
                uid: user.uid,
                email: str(user.email),
                phone: str(user.phoneNumber),
                displayName: str(user.displayName),
                createdAt: user.metadata.creationTime,
            });

            if (onSuccess) {
                const token = await user.getIdToken();
                await onSuccess(token);
            }
        };

        const handleGoogleSignIn = useEvent(() =>
            socialLogin(signInWithGoogle),
        );
        const handleFacebookSignIn = useEvent(() =>
            socialLogin(signInWithFacebook),
        );

        return (
            <Stack flexGrow={1} px={4} ref={ref}>
                <Typography
                    variant={'largeTitle'}
                    fontWeight={800}
                    component={'h3'}
                >
                    {t('connectToBookr')}
                </Typography>
                <Typography
                    variant={'callout'}
                    fontWeight={600}
                    color={'textSecondary'}
                    paragraph
                >
                    {t('connectToBookrUsingYourEmail')}
                </Typography>

                <Formik
                    initialValues={initialValues}
                    onSubmit={handleSubmit}
                    innerRef={formikRef}
                    {...validationProps}
                >
                    <Stack component={Form} mb={3}>
                        <FormikTextField
                            label={'Enter your email'}
                            type={'email'}
                            name={'email'}
                        />
                        <Grid
                            container
                            justifyContent={'flex-end'}
                            mb={3}
                            mt={1}
                        >
                            <Link
                                variant={'caption'}
                                color={'textSecondary'}
                                fontWeight={700}
                                underline={'hover'}
                                onClick={handleForgotPassword}
                            >
                                {t('didYouForgotYourPassword')}
                            </Link>
                        </Grid>
                        <FormikButton
                            type={'submit'}
                            variant={'contained'}
                            color={'info'}
                            size={'large'}
                            fullWidth
                            validateDirty
                        >
                            {t('continue')}
                        </FormikButton>
                    </Stack>
                </Formik>

                {socialLoginEnabled?.asBoolean() ? (
                    <>
                        <Typography
                            variant={'caption1'}
                            fontWeight={600}
                            color={'textSecondary'}
                            mb={3}
                            align={'center'}
                        >
                            {t('or')}
                        </Typography>

                        <Stack spacing={2} mb={3}>
                            <StyledSocialButton
                                variant={'subtle'}
                                size={'large'}
                                startIcon={<GoogleIcon />}
                                onClick={handleGoogleSignIn}
                            >
                                {t('continueWithGoogle')}
                            </StyledSocialButton>
                            <StyledSocialButton
                                disabled
                                variant={'subtle'}
                                size={'large'}
                                startIcon={<AppleIcon />}
                            >
                                {t('continueWithApple')}
                            </StyledSocialButton>
                            <StyledSocialButton
                                disabled
                                variant={'subtle'}
                                size={'large'}
                                startIcon={<FacebookIcon />}
                                onClick={handleFacebookSignIn}
                            >
                                {t('continueWithFacebook')}
                            </StyledSocialButton>
                        </Stack>
                    </>
                ) : null}
            </Stack>
        );
    },
);
