import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { Form, Formik, FormikProps } from 'formik';
import { useAtom } from 'jotai';
import { forwardRef, useRef } from 'react';
import {
    createUserWithEmailAndPassword,
    FirebaseErrorCode,
    isFirebaseError,
    matchFirebaseError,
    sendVerificationEmail,
} from '@bookr-technologies/firebase';
import { useEvent, useValidationSchema } from '@bookr-technologies/hooks';
import { useI18n } from '@bookr-technologies/i18n';
import {
    FormikButton,
    FormikTextField,
} from '@bookr-technologies/ui/components';
import { AuthFormSteps } from './AuthFormSteps';
import { emailAtom } from './store';

interface Props {
    onStep(step: number): void;
}

const initialValues = {
    password: '',
    confirmPassword: '',
};

export const AuthFormCreatePasswordStep = forwardRef<HTMLDivElement, Props>(
    function AuthFormCreatePasswordStep({ onStep }, ref) {
        const formik = useRef<FormikProps<typeof initialValues>>(null);
        const { t } = useI18n();

        const validation = useValidationSchema((yup) => ({
            password: yup.string().required(t('requiredField')),
            confirmPassword: yup
                .string()
                .required(t('requiredField'))
                .oneOf([yup.ref('password'), ''], t('passwordsDontMatch')),
        }));

        const [email] = useAtom(emailAtom);
        const handleSubmit = useEvent(
            async ({ password }: typeof initialValues) => {
                try {
                    const { user } = await createUserWithEmailAndPassword(
                        email,
                        password,
                    );
                    await sendVerificationEmail(user);
                    onStep(AuthFormSteps.SignUp);
                } catch (error) {
                    if (isFirebaseError(error)) {
                        formik.current?.setFieldError(
                            'password',
                            matchFirebaseError(error, {
                                [FirebaseErrorCode.WrongPassword]:
                                    t('wrongPassword'),
                                [FirebaseErrorCode.TooManyRequests]:
                                    t('tooManyRequests'),
                                default: t('somethingWentWrong'),
                            }),
                        );
                    } else {
                        formik.current?.setFieldError(
                            'password',
                            t('somethingWentWrong'),
                        );
                    }
                }
            },
        );

        return (
            <Stack flexGrow={1} px={4} ref={ref}>
                <Typography
                    variant={'largeTitle'}
                    fontWeight={800}
                    component={'h3'}
                    mb={1}
                >
                    {t('authDialogCreatePasswordTitle')}
                </Typography>
                <Typography
                    variant={'callout'}
                    fontWeight={600}
                    color={'textSecondary'}
                    component={'p'}
                >
                    {t('authDialogCreatePasswordSubtitle')}
                </Typography>

                <Formik
                    initialValues={initialValues}
                    onSubmit={handleSubmit}
                    innerRef={formik}
                    {...validation}
                >
                    <Stack component={Form} spacing={3} my={3}>
                        <Stack spacing={2}>
                            <FormikTextField
                                type={'password'}
                                name={'password'}
                                label={t('createAPassword')}
                                autoComplete={'new-password'}
                            />

                            <FormikTextField
                                type={'password'}
                                name={'confirmPassword'}
                                label={t('confirmYourPassword')}
                                autoComplete={'new-password'}
                            />
                        </Stack>

                        <FormikButton
                            type={'submit'}
                            variant={'contained'}
                            color={'info'}
                            size={'large'}
                            fullWidth
                            validateDirty
                        >
                            {t('continue')}
                        </FormikButton>
                    </Stack>
                </Formik>
            </Stack>
        );
    },
);
