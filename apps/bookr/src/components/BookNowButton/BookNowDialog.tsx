/* eslint-disable @typescript-eslint/no-non-null-assertion */
import CircularProgress from '@mui/material/CircularProgress';
import Dialog from '@mui/material/Dialog';
import Grid from '@mui/material/Grid';
import { Breakpoint, Theme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import { useAtom } from 'jotai';
import { useMemo } from 'react';
import { str } from '@bookr-technologies/core';
import { useEvent } from '@bookr-technologies/hooks';
import { useBusinessQuery } from '~/api';
import { bookNowAtoms, BookNowDialogSteps } from './store';
import { BookNowDialogProps } from './types';
import { BookServiceVariant } from './variants/BookServiceVariant';
import { BookedSuccessfullyVariant } from './variants/BookedSuccessfullyVariant';
import { SelectServiceVariant } from './variants/SelectServiceVariant';
import { WrongDataVariant } from './variants/WrongDataVariant';

export function BookNowDialog({ ...rest }: BookNowDialogProps) {
    const [{ open, currentStep, businessId, staffId, serviceId }, setDialog] =
        useAtom(bookNowAtoms.dialog);
    const isFullscreen = useMediaQuery((theme: Theme) =>
        theme.breakpoints.down('sm'),
    );
    const businessQuery = useBusinessQuery(businessId);
    const validBusiness = businessId && businessId === businessQuery.data?.id;

    const staffMember = useMemo(
        () =>
            (businessQuery.data?.staffMembers ?? []).find(
                (member) => staffId && member.uid === staffId,
            ),
        [businessQuery.data?.staffMembers, staffId],
    );

    const service = useMemo(
        () =>
            (staffMember?.services ?? []).find(
                (service) => serviceId && service.id === serviceId,
            ),
        [serviceId, staffMember?.services],
    );

    const step = useMemo(() => {
        if (businessQuery.isLoading) {
            return BookNowDialogSteps.Loading;
        }

        if (businessQuery.isError) {
            return BookNowDialogSteps.Error;
        }

        if (validBusiness && staffId && staffMember && serviceId && service) {
            return BookNowDialogSteps.BookService;
        }

        if (validBusiness) {
            return BookNowDialogSteps.ListServices;
        }

        return BookNowDialogSteps.Unknown;
    }, [
        businessQuery.isError,
        businessQuery.isLoading,
        service,
        serviceId,
        staffId,
        staffMember,
        validBusiness,
    ]);

    const content = useMemo(() => {
        switch (currentStep || step) {
            case BookNowDialogSteps.BookService:
                return (
                    <BookServiceVariant
                        staffMember={staffMember!}
                        service={service!}
                        businessName={str(businessQuery.data?.name)}
                    />
                );
            case BookNowDialogSteps.ListServices:
                return (
                    <SelectServiceVariant
                        businessId={str(businessQuery.data?.id)}
                        businessName={str(businessQuery.data?.name)}
                        members={businessQuery.data?.staffMembers ?? []}
                    />
                );
            case BookNowDialogSteps.BookedSuccessfully:
                return <BookedSuccessfullyVariant />;
            case BookNowDialogSteps.Error:
                return <WrongDataVariant />;
            case BookNowDialogSteps.Loading:
                return (
                    <Grid container justifyContent={'center'} p={5}>
                        <CircularProgress size={32} />
                    </Grid>
                );
            default:
                return <WrongDataVariant />;
        }
    }, [
        businessQuery.data?.id,
        businessQuery.data?.name,
        businessQuery.data?.staffMembers,
        currentStep,
        service,
        staffMember,
        step,
    ]);

    const handleClose = useEvent(() => setDialog({ open: false }));

    const maxWidth =
        (
            {
                [BookNowDialogSteps.BookService]: 'sm',
            } as Partial<Record<BookNowDialogSteps, Breakpoint>>
        )[step] ?? 'sm';

    return (
        <Dialog
            open={open}
            onClose={handleClose}
            fullScreen={isFullscreen}
            fullWidth
            maxWidth={maxWidth}
            {...rest}
        >
            {content}
        </Dialog>
    );
}
