import { ButtonProps } from '@mui/material/Button';
import { DialogProps } from '@mui/material/Dialog';
import {
    AppointmentModel,
    ServiceModel,
    StaffMemberModel,
    UserModel,
} from '@bookr-technologies/sdk';

export type BookNowButtonProps = {
    businessId: string;
    staffId?: string;
    serviceId?: number;
    isSubscription?: boolean;
} & ButtonProps;

export type BookNowDialogProps = Omit<DialogProps, 'open' | 'onClose'>;

export type SelectServiceVariantProps = {
    businessName: string;
    businessId: string;
    members: UserModel[];
};

export type BookServiceVariantProps = {
    staffMember: StaffMemberModel;
    service: ServiceModel;
    businessName: string;
    disableBack?: boolean;
    onSubmit?: (appointment: AppointmentModel) => void;
};
