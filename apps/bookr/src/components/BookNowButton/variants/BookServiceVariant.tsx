import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import LoadingButton from '@mui/lab/LoadingButton';
import Button from '@mui/material/Button';
import CircularProgress from '@mui/material/CircularProgress';
import Grid from '@mui/material/Grid';
import IconButton from '@mui/material/IconButton';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { DateCalendar } from '@mui/x-date-pickers/DateCalendar';
import { DayCalendarSkeleton } from '@mui/x-date-pickers/DayCalendarSkeleton';
import addDays from 'date-fns/addDays';
import { useMemo, useState } from 'react';
import { track, TrackEvents } from '@bookr-technologies/analytics';
import {
    Duration,
    getTimezoneOffset,
    getWeekday,
    parseTime,
    retry,
    utcToLocal,
} from '@bookr-technologies/core';
import {
    useDialog,
    useEvent,
    useLoading,
    useNotification,
    useNow,
    useValueRef,
} from '@bookr-technologies/hooks';
import { useI18n } from '@bookr-technologies/i18n';
import { TimeslotModel } from '@bookr-technologies/sdk';
import {
    useAppointmentDaysThatCanBeBookedQuery,
    useAppointmentGetTimeslotsQuery,
    useCreateAppointmentMutation,
    useSubscribeToWaitingList,
    useUserQuery,
} from '~/api';
import { AuthDialog } from '~/components/AuthDialog';
import { useSession } from '~/components/SessionProvider';
import { createWorkingHoursMap } from '~/lib/utilities/workingHours';
import { useBookNowActions } from '../store';
import { BookServiceVariantProps } from '../types';
import { CalendarDay } from './CalendarDay';
import { Timeslots } from './Timeslots';
import {
    StyledCalendarHolder,
    StyledChoseTimeInstructions,
    StyledHeader,
} from './styles';

export function BookServiceVariant({
    service,
    staffMember,
    businessName,
    onSubmit,
    disableBack,
}: BookServiceVariantProps) {
    // State
    const [date, setDate] = useState<Date>(new Date());
    const [time, setTime] = useState<string>('');

    // Hooks
    const { t } = useI18n();
    const { selectSuccessStep, selectServicesStep } = useBookNowActions();
    const { session } = useSession();
    const bookNowLoading = useLoading();
    const waitingListLoading = useLoading();
    const authDialog = useDialog();
    const userQuery = useUserQuery();
    const subscribeToWaitingList = useSubscribeToWaitingList();
    const now = useNow(Duration.minutes(5));
    const sessionRef = useValueRef(session);
    const notification = useNotification();

    // API
    const createAppointmentMutation = useCreateAppointmentMutation();
    const timeslots = useAppointmentGetTimeslotsQuery({
        serviceId: service.id,
        staffId: staffMember.uid,
        date,
    });
    const availableDays = useAppointmentDaysThatCanBeBookedQuery({
        serviceId: service.id,
        staffId: staffMember.uid,
    });

    // Computed values
    const workingHours = useMemo(
        () => createWorkingHoursMap(staffMember.workingHours),
        [staffMember.workingHours],
    );
    const maxDate = useMemo(
        () => addDays(now, staffMember.maxFutureDaysAppointment),
        [now, staffMember.maxFutureDaysAppointment],
    );

    const isBookNowDisabled =
        timeslots.isLoading ||
        timeslots.isError ||
        !timeslots.data?.length ||
        !date ||
        !time;
    const isBookNowLoading = timeslots.isLoading || bookNowLoading.state;
    const trackData = {
        accountType: userQuery.data?.accountType ?? 'Unknown',
        businessName: businessName,
        serviceId: service.id,
        serviceName: service.name,
        servicePrice: `${service.price} ${service.currency}`,
        staffId: staffMember.uid,
        staffName: staffMember.displayName,
        platform: 'Web',
    };

    // Event handlers
    const handleChangeDate = useEvent((date: Date | null) => {
        setTime('');
        if (date) {
            setDate(date);
        }
    });

    const handleBookTime = useEvent(async () => {
        bookNowLoading.start();
        if (!session) {
            authDialog.open();
            await authDialog.lock();
        }

        try {
            await retry(
                () => {
                    if (!sessionRef.current) {
                        throw new Error('User not logged in');
                    }
                },
                { maxRetries: 10, delay: 500 },
            );

            const { appointment, success, error, webRedirectUrl } =
                await createAppointmentMutation.mutateAsync({
                    serviceId: service.id,
                    staffId: staffMember.uid,
                    timestamp: utcToLocal(parseTime(time, date)),
                    timezoneOffset: getTimezoneOffset(date),
                });

            if (!success || error) {
                notification.error(
                    t(`bookingError.${error}` as any, {
                        defaultValue: t('somethingWentWrong'),
                    }),
                );

                await Promise.all([
                    availableDays.refetch(),
                    timeslots.refetch(),
                ]);

                return;
            }

            onSubmit?.(appointment);
            selectSuccessStep(appointment);
            track(TrackEvents.BookedAppointment, trackData);

            if (webRedirectUrl) {
                const currentUrl = new URL(window.location.href);
                const params = new URLSearchParams(currentUrl.search);
                params.append('appointment', JSON.stringify(appointment));
                const redirectUrl = `${webRedirectUrl}?${params.toString()}`;

                window.open(redirectUrl, '_blank', 'noopener,noreferrer');
            }
        } catch (error) {
            bookNowLoading.stop();
            console.error(error);
            //TODO: Handle error
            return;
        } finally {
            bookNowLoading.stop();
        }
    });

    const handleTimeChange = useEvent((timeslot: TimeslotModel) =>
        setTime(timeslot.start),
    );

    const isDateDisabled = useEvent((date) => {
        const weekday = getWeekday(date);

        return !workingHours[weekday] || !workingHours[weekday].length;
    });

    const handleWaitingList = useEvent(async () => {
        try {
            await waitingListLoading.from(
                subscribeToWaitingList.mutateAsync(
                    {
                        staffId: staffMember.uid,
                        date,
                    },
                    {
                        onSuccess(added) {
                            if (added) {
                                track(
                                    TrackEvents.SubscribedToWaitingList,
                                    trackData,
                                );
                                notification.success(
                                    t('subscribedSuccessfullyToWaitingList'),
                                );
                            } else {
                                notification.error(
                                    t('couldNotSubscribeToWaitingList'),
                                );
                            }
                        },
                    },
                ),
            );
        } catch (e) {
            notification.error(t('somethingWentWrong'));
        }
    });

    return (
        <>
            <StyledHeader
                sx={{
                    flexDirection: 'column',
                    alignItems: 'flex-start',
                    gap: 1,
                    px: 4,
                }}
            >
                {!disableBack ? (
                    <IconButton onClick={selectServicesStep}>
                        <ArrowBackIcon />
                    </IconButton>
                ) : null}
                <Stack flexGrow={1}>
                    <Typography variant={'subhead'} fontWeight={600}>
                        {t('bookNowAt')}
                    </Typography>
                    <Typography variant={'title1'} fontWeight={800}>
                        {`${staffMember.displayName}, ${service.name}`}
                    </Typography>
                </Stack>
            </StyledHeader>

            <Stack
                px={{
                    xs: 2,
                    sm: 4,
                }}
                pb={5}
                gap={4}
                flexGrow={1}
                justifyContent={'space-between'}
            >
                <StyledCalendarHolder>
                    <DateCalendar
                        minDate={now}
                        maxDate={maxDate}
                        value={date}
                        onChange={handleChangeDate}
                        shouldDisableDate={isDateDisabled}
                        loading={availableDays.isLoading}
                        renderLoading={() => <DayCalendarSkeleton />}
                        slots={{ day: CalendarDay }}
                        slotProps={{
                            day: {
                                availableDays: availableDays.data ?? {},
                            } as any,
                        }}
                    />
                </StyledCalendarHolder>

                <Stack gap={2}>
                    <Typography variant={'title2'} fontWeight={800}>
                        {t('chooseTime')}
                    </Typography>

                    {timeslots.isLoading ? (
                        <Grid
                            container
                            alignItems={'center'}
                            justifyContent={'center'}
                        >
                            <CircularProgress size={24} />
                        </Grid>
                    ) : timeslots.error ? (
                        <StyledChoseTimeInstructions elevation={0}>
                            <Typography
                                variant={'callout'}
                                color={'textSecondary'}
                                fontWeight={600}
                            >
                                {t('timeslotsLoadErrorMessage')}
                            </Typography>
                            <Button onClick={() => timeslots.refetch()}>
                                {t('retry')}
                            </Button>
                        </StyledChoseTimeInstructions>
                    ) : timeslots.data?.length ? (
                        <Timeslots
                            value={time}
                            onChange={handleTimeChange}
                            timeslots={timeslots.data ?? []}
                            date={date}
                        />
                    ) : (
                        <StyledChoseTimeInstructions elevation={0}>
                            <Typography
                                variant={'callout'}
                                color={'textSecondary'}
                                fontWeight={600}
                            >
                                {t('noAvailableTimeslotsMessage')}
                            </Typography>
                        </StyledChoseTimeInstructions>
                    )}

                    {timeslots.data && timeslots.data.length > 0 ? (
                        <LoadingButton
                            variant={'contained'}
                            size={'large'}
                            fullWidth
                            onClick={handleBookTime}
                            disabled={isBookNowDisabled}
                            loading={isBookNowLoading}
                            sx={
                                isBookNowDisabled
                                    ? {}
                                    : { position: 'sticky', bottom: 20 }
                            }
                        >
                            {t('Book now')}
                        </LoadingButton>
                    ) : (
                        <LoadingButton
                            variant={'contained'}
                            size={'large'}
                            fullWidth
                            onClick={handleWaitingList}
                            loading={waitingListLoading.state}
                        >
                            {t('addMeToWaitingList')}
                        </LoadingButton>
                    )}
                </Stack>
            </Stack>
            <AuthDialog required {...authDialog.props} />
        </>
    );
}
