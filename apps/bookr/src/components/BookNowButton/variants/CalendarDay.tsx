import Badge from '@mui/material/Badge';
import Grid from '@mui/material/Grid';
import { PickersDay, PickersDayProps } from '@mui/x-date-pickers/PickersDay';
import format from 'date-fns/format';

export function CalendarDay(
    props: PickersDayProps<Date> & { availableDays?: Record<string, boolean> },
) {
    const { availableDays = {}, day, outsideCurrentMonth, ...rest } = props;
    const hasDays =
        !props.outsideCurrentMonth &&
        availableDays[format(props.day, 'yyyy-MM-dd')];

    return (
        <Grid
            container
            className={'CalendarDay-root'}
            justifyContent={'center'}
        >
            <Badge
                key={props.day.toString()}
                color={'info'}
                variant="dot"
                overlap={'circular'}
                invisible={!hasDays}
            >
                <PickersDay
                    {...rest}
                    outsideCurrentMonth={outsideCurrentMonth}
                    day={day}
                />
            </Badge>
        </Grid>
    );
}
