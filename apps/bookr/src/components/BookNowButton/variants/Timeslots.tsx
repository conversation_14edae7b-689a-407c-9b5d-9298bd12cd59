import Grid from '@mui/material/Grid';
import { useMemo } from 'react';
import { TimeslotModel } from '@bookr-technologies/sdk';
import { Timeslot } from './Timeslot';
import { TimeslotsProps } from './types';

export function Timeslots({
    timeslots,
    value,
    onChange,
    date,
}: TimeslotsProps) {
    const uniqSlots = useMemo(
        () =>
            Object.values(
                timeslots.reduce(
                    (acc, timeslot) => ({
                        ...acc,
                        [`${timeslot.start}-${timeslot.end}`]: timeslot,
                    }),
                    {} as Record<string, TimeslotModel>,
                ),
            ),
        [timeslots],
    );

    return (
        <Grid container spacing={1} mb={2}>
            {uniqSlots.map((timeslot) => (
                <Grid key={`${timeslot.start}-${timeslot.end}`} item xs={4}>
                    <Timeslot
                        timeslot={timeslot}
                        selected={value === timeslot.start}
                        onClick={onChange}
                        date={date}
                    />
                </Grid>
            ))}
        </Grid>
    );
}
