import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CloseIcon from '@mui/icons-material/Close';
import Button from '@mui/material/Button';
import Grid from '@mui/material/Grid';
import IconButton from '@mui/material/IconButton';
import ListItem, { ListItemProps } from '@mui/material/ListItem';
import ListItemText, { ListItemTextProps } from '@mui/material/ListItemText';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import format from 'date-fns/format';
import parseISO from 'date-fns/parseISO';
import { useAtom } from 'jotai';
import Link from 'next/link';
import { num, str } from '@bookr-technologies/core';
import { useEvent, useLocale } from '@bookr-technologies/hooks';
import { useI18n } from '@bookr-technologies/i18n';
import { RouteLinks } from '~/lib/routes';
import { bookNowAtoms } from '../store';
import { StyledAppointmentDetails } from './styles';

export function BookedSuccessfullyVariant() {
    const { t } = useI18n();
    const [locale] = useLocale();
    const [{ appointment }, setDialog] = useAtom(bookNowAtoms.dialog);
    const handleCloseDialog = useEvent(() => setDialog({ open: false }));

    const listItemProps: ListItemProps = {
        disableGutters: true,
        dense: true,
    };

    const listItemTextProps: ListItemTextProps = {
        primaryTypographyProps: {
            variant: 'subhead',
            fontWeight: 800,
        },
        secondaryTypographyProps: {
            variant: 'subhead',
            fontWeight: 600,
        },
    };

    return (
        <Stack p={3} flexGrow={1} justifyContent={'space-between'}>
            <Grid container justifyContent={'flex-end'}>
                <IconButton onClick={handleCloseDialog}>
                    <CloseIcon />
                </IconButton>
            </Grid>

            <Grid
                container
                direction={'column'}
                gap={2}
                justifyContent={'center'}
                alignItems={'center'}
            >
                <CheckCircleIcon sx={{ fontSize: 72 }} color={'info'} />
                <Stack>
                    <Typography
                        variant={'title2'}
                        fontWeight={800}
                        align={'center'}
                    >
                        {t('bookingHasBeenSuccessfullyMade')}
                    </Typography>
                </Stack>
            </Grid>

            <Stack gap={4} mt={4}>
                <Grid container direction={'column'} gap={1}>
                    <Typography variant={'callout'} fontWeight={600}>
                        {t('appointmentDetails')}:
                    </Typography>
                    <StyledAppointmentDetails>
                        <ListItem {...listItemProps}>
                            <ListItemText
                                primary={`${t('id')}:`}
                                secondary={`#${appointment?.id}`}
                                {...listItemTextProps}
                            />
                        </ListItem>
                        <ListItem {...listItemProps}>
                            <ListItemText
                                primary={`${t('staffMember')}:`}
                                secondary={str(
                                    appointment?.staff?.displayName,
                                    '-',
                                )}
                                {...listItemTextProps}
                            />
                        </ListItem>
                        <ListItem {...listItemProps}>
                            <ListItemText
                                primary={`${t('service')}:`}
                                secondary={str(appointment?.service?.name, '-')}
                                {...listItemTextProps}
                            />
                        </ListItem>
                        <ListItem {...listItemProps}>
                            <ListItemText
                                primary={`${t('price')}:`}
                                secondary={`${num(
                                    appointment?.service?.price,
                                ).toFixed(2)} ${str(
                                    appointment?.service?.currency,
                                )}`}
                                {...listItemTextProps}
                            />
                        </ListItem>
                        <ListItem {...listItemProps}>
                            <ListItemText
                                primary={`${t('dateAndTime')}:`}
                                secondary={format(
                                    parseISO(str(appointment?.dateTime)),
                                    'PPp',
                                    { locale },
                                )}
                                {...listItemTextProps}
                            />
                        </ListItem>
                        <ListItem {...listItemProps}>
                            <ListItemText
                                primary={`${t('creationDate')}:`}
                                secondary={format(
                                    parseISO(str(appointment?.createdAt)),
                                    'PPp',
                                    { locale },
                                )}
                                {...listItemTextProps}
                            />
                        </ListItem>
                        <ListItem {...listItemProps}>
                            <ListItemText
                                primary={`${t('recurring')}:`}
                                secondary={t(
                                    appointment?.recurrent ? 'yes' : 'no',
                                )}
                                {...listItemTextProps}
                            />
                        </ListItem>
                    </StyledAppointmentDetails>
                </Grid>
                <Stack gap={2}>
                    <Button
                        component={Link}
                        href={RouteLinks.appointmentsView(
                            appointment?.id ?? '',
                        )}
                        variant={'contained'}
                        size={'large'}
                        color={'info'}
                        onClick={handleCloseDialog}
                    >
                        {t('viewAppointment')}
                    </Button>

                    <Button
                        variant={'contained'}
                        size={'large'}
                        color={'primary'}
                        onClick={handleCloseDialog}
                    >
                        {t('close')}
                    </Button>
                </Stack>
            </Stack>
        </Stack>
    );
}
