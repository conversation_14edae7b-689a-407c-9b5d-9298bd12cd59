import CloseIcon from '@mui/icons-material/Close';
import IconButton from '@mui/material/IconButton';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { useI18n } from '@bookr-technologies/i18n';
import { Services } from '~/components/Services';
import { useBookNowActions } from '../store';
import { SelectServiceVariantProps } from '../types';
import { StyledHeader } from './styles';

export function SelectServiceVariant({
    businessName,
    businessId,
    members,
}: SelectServiceVariantProps) {
    const { t } = useI18n();
    const { closeDialog } = useBookNowActions();

    return (
        <>
            <StyledHeader>
                <Typography variant={'title2'} fontWeight={800}>
                    {t('bookNowAtBusiness', { businessName })}
                </Typography>
                <IconButton onClick={closeDialog}>
                    <CloseIcon />
                </IconButton>
            </StyledHeader>

            <Stack
                px={{
                    xs: 2,
                    sm: 3.5,
                }}
                pb={5}
            >
                <Services
                    businessId={businessId}
                    staffMembers={members}
                    hideExpand
                />
            </Stack>
        </>
    );
}
