import List from '@mui/material/List';
import Paper from '@mui/material/Paper';
import { alpha, styled } from '@mui/material/styles';
import { extended, palette } from '@bookr-technologies/ui/styles';

export const StyledHeader = styled('header', { name: 'Header' })(
    ({ theme }) => ({
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        position: 'sticky',
        top: 0,
        padding: theme.spacing(2, 4, 2, 5),
        zIndex: 3,
        backgroundColor: alpha(theme.palette.background.paper, 0.8),
        backdropFilter: 'blur(20px) saturate(180%)',
        [theme.breakpoints.down('sm')]: {
            borderRadius: 0,
            padding: theme.spacing(2),
        },
    }),
);

export const StyledChoseTimeInstructions = styled(Paper, {
    name: 'ChoseTimeInstructions',
})(({ theme }) => ({
    padding: theme.spacing(4),
    backgroundColor: extended.backgroundSecondary,
}));

export const StyledCalendarHolder = styled('div', { name: 'CalendarHolder' })(
    ({ theme }) => ({
        '&, .MuiDateCalendar-root, .MuiYearCalendar-root, .MuiDayCalendarSkeleton-root':
            {
                width: '100%',
            },
        '.MuiDayCalendarSkeleton-week': {
            justifyContent: 'space-around',
        },
        '.MuiYearCalendar-root': {
            paddingTop: theme.spacing(1),
        },
        '.MuiDayCalendar-weekDayLabel, .MuiPickersYear-yearButton': {
            fontWeight: 600,
            fontSize: 16,
        },
        '.MuiDayCalendar-weekDayLabel': {
            color: theme.palette.text.primary,
        },
        '.MuiDayCalendar-weekDayLabel, .CalendarDay-root': {
            width: 'calc(100% / 7)',
        },
        '.CalendarDay-root .MuiBadge-badge.MuiBadge-dot': {
            opacity: 0.7,
            width: 4,
            height: 4,
            minWidth: 4,
            top: '25%',
            right: '25%',
        },
        '.MuiPickersDay-root': {
            fontWeight: 600,
            fontSize: 13,
            zIndex: 1,
            backgroundColor: 'unset !important',
            border: 'none !important',
        },
        '.MuiPickersDay-root .MuiTouchRipple-root': {
            maxWidth: 36,
            height: 36,
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            bottom: 'auto',
        },
        '.MuiPickersDay-root:before': {
            content: '""',
            display: 'block',
            width: 36,
            height: 36,
            borderRadius: 18,
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            zIndex: -1,
            transition: theme.transitions.create([
                'background-color',
                'box-shadow',
            ]),
        },

        '.MuiPickersDay-root.MuiPickersDay-today:not(.Mui-selected)': {
            border: 'none',
            color: palette.foundation.accent,
            '&:before': {
                border: 'none !important',
            },
        },
        '.MuiPickersDay-root.Mui-selected': {
            color: palette.foundation.accent,
            '&:before': {
                boxShadow: '0 0 14px 0 rgba(0, 0, 0, 0.15)',
            },
        },
        '.MuiPickersDay-root.MuiPickersDay-today': {
            color: palette.foundation.accent,
            '&:before': {
                border: 'none !important',
            },
        },
        '.MuiPickersDay-root:hover': {
            '&:before': {
                backgroundColor: 'rgba(0, 0, 0, 0.04)',
            },
        },
        '.MuiPickersCalendarHeader-root': {
            backgroundColor: palette.extended.backgroundSecondary,
            borderRadius: 12,
            height: 48,
            maxHeight: 48,
            margin: 0,
        },
        '.MuiPickersCalendarHeader-label': {
            fontWeight: 800,
            fontSize: 17,
        },
    }),
);

export const StyledAppointmentDetails = styled(List, {
    name: 'AppointmentDetails',
})(({ theme }) => ({
    padding: theme.spacing(2),
    backgroundColor: palette.extended.backgroundSecondary,
    borderRadius: 12,
    '.MuiListItem-root': {},
    '.MuiListItemText-root ': {
        display: 'flex',
        alignItems: 'center',
        gap: theme.spacing(1),
        padding: 0,
        margin: 0,
    },
}));
