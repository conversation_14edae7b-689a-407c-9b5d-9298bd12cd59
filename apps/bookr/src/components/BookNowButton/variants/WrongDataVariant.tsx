import ErrorIcon from '@mui/icons-material/Error';
import Button from '@mui/material/Button';
import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { useSetAtom } from 'jotai';
import { useEvent } from '@bookr-technologies/hooks';
import { useI18n } from '@bookr-technologies/i18n';
import { bookNowAtoms } from '../store';

export function WrongDataVariant() {
    const { t } = useI18n();
    const setDialog = useSetAtom(bookNowAtoms.dialog);
    const handleCloseDialog = useEvent(() => setDialog({ open: false }));

    return (
        <Stack p={3} gap={4}>
            <Grid container gap={2} alignItems={'center'}>
                <ErrorIcon fontSize={'large'} color={'disabled'} />
                <Stack>
                    <Typography variant={'title2'} fontWeight={800}>
                        {t('somethingWentWrong')}
                    </Typography>
                    <Typography
                        variant={'callout'}
                        fontWeight={600}
                        color={'textSecondary'}
                    >
                        {t('somethingWentWrongDescription')}
                    </Typography>
                </Stack>
            </Grid>

            <Grid container justifyContent={'flex-end'}>
                <Button
                    variant={'contained'}
                    color={'error'}
                    onClick={handleCloseDialog}
                >
                    {t('close')}
                </Button>
            </Grid>
        </Stack>
    );
}
