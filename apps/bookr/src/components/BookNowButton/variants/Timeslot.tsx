import Button from '@mui/material/Button';
import format from 'date-fns/format';
import isBefore from 'date-fns/isBefore';
import { useMemo } from 'react';
import { parseTime, utcToLocal } from '@bookr-technologies/core';
import { useEvent, useLocale } from '@bookr-technologies/hooks';
import { TimeslotProps } from './types';

export function Timeslot({ timeslot, selected, onClick, date }: TimeslotProps) {
    const handleClick = useEvent(() => onClick(timeslot));
    const [locale] = useLocale();
    const start = useMemo(
        () => utcToLocal(parseTime(timeslot.start, date)),
        [date, timeslot.start],
    );
    const startText = useMemo(
        () => format(start, 'p', { locale }),
        [locale, start],
    );

    if (isBefore(start, new Date())) {
        return null;
    }

    return (
        <Button
            variant={'contained'}
            onClick={handleClick}
            color={selected ? 'primary' : 'inherit'}
            fullWidth
        >
            {startText}
        </Button>
    );
}
