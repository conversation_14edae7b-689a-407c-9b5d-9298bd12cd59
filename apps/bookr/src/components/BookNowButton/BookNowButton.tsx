import Button from '@mui/material/Button';
import { useSetAtom } from 'jotai';
import { useRouter } from 'next/router';
import { MouseEvent } from 'react';
import { useEvent } from '@bookr-technologies/hooks';
import { useI18n } from '@bookr-technologies/i18n';
import { RouteLinks } from '~/lib/routes';
import { bookNowAtoms } from './store';
import { BookNowButtonProps } from './types';

export function BookNowButton({
    children,
    businessId,
    serviceId,
    staffId,
    isSubscription,
    onClick,
    ...rest
}: BookNowButtonProps) {
    const { t } = useI18n();
    const setDialogState = useSetAtom(bookNowAtoms.dialog);
    const router = useRouter();
    const handleOpen = useEvent(async (e: MouseEvent<HTMLButtonElement>) => {
        if (isSubscription && serviceId) {
            await router.push(
                RouteLinks.newSubscription(serviceId, businessId),
            );
            return;
        }

        setDialogState({
            open: true,
            businessId,
            serviceId,
            staffId,
        });

        onClick?.(e);
    });

    if (!businessId) {
        return null;
    }

    return (
        <Button
            variant={'contained'}
            color={'primary'}
            onClick={handleOpen}
            {...rest}
        >
            {children ?? t('Book now')}
        </Button>
    );
}
