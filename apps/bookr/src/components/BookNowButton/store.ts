import { atom, useAtom } from 'jotai';
import { useEvent } from '@bookr-technologies/hooks';
import { AppointmentModel } from '@bookr-technologies/sdk';

export enum BookNowDialogSteps {
    Loading = 'loading',
    Error = 'error',
    BookService = 'book-service',
    ListServices = 'list-services',
    BookedSuccessfully = 'booked-successfully',
    Unknown = 'unknown',
}

interface BookNowDialogAtomType {
    open: boolean;
    currentStep?: BookNowDialogSteps;
    businessId?: string;
    staffId?: string;
    serviceId?: number;
    appointment?: AppointmentModel;
}

export const bookNowAtoms = {
    dialog: atom<BookNowDialogAtomType>({
        open: false,
    }),
};

export function useBookNowActions() {
    const [, setAtom] = useAtom(bookNowAtoms.dialog);
    const closeDialog = useEvent(() => setAtom({ open: false }));
    const openDialog = useEvent(
        (businessId: string, staffId?: string, serviceId?: number) => {
            setAtom({ open: true });
            setTimeout(
                () => setAtom({ open: true, businessId, staffId, serviceId }),
                300,
            );
        },
    );
    const selectServicesStep = useEvent(() =>
        setAtom((prev) => ({
            open: true,
            businessId: prev.businessId,
        })),
    );

    const selectSuccessStep = useEvent((appointment: AppointmentModel) => {
        setAtom((prev) => ({
            ...prev,
            appointment,
            currentStep: BookNowDialogSteps.BookedSuccessfully,
        }));
    });

    return {
        closeDialog,
        openDialog,
        selectServicesStep,
        selectSuccessStep,
    };
}
