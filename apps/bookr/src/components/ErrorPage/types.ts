import type { HttpStatusCode } from 'axios';

type ErrorPageVariant<K extends string, T> = { variant: K } & T;

export type ErrorPageDefaultProps = {
    title?: string;
    message?: string;
};

export type ErrorPageHttpProps = ErrorPageDefaultProps & {
    statusCode: HttpStatusCode;
};

export type ErrorPageProps =
    | ErrorPageVariant<'default', ErrorPageDefaultProps>
    | ErrorPageVariant<'http', ErrorPageHttpProps>;
