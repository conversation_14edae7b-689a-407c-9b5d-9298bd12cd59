import type { ErrorPageProps } from './types';
import { ErrorPageDefault } from './variants/ErrorPageDefault';
import { ErrorPageHttp } from './variants/ErrorPageHttp';

export function ErrorPage(props: ErrorPageProps) {
    switch (props.variant) {
        case 'http':
            return (
                <ErrorPageHttp
                    statusCode={props.statusCode}
                    title={props.title}
                    message={props.message}
                />
            );
        default:
            return (
                <ErrorPageDefault title={props.title} message={props.message} />
            );
    }
}

export default ErrorPage;
