import Container from '@mui/material/Container';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { useI18n } from '@bookr-technologies/i18n';
import { ErrorPageDefaultProps } from '../types';

export function ErrorPageDefault({ title, message }: ErrorPageDefaultProps) {
    const { t } = useI18n();

    return (
        <Container>
            <Stack
                gap={2}
                alignItems={'center'}
                justifyContent={'center'}
                py={10}
            >
                <Typography
                    component={'h1'}
                    variant={'title1'}
                    fontWeight={800}
                >
                    {title || t('somethingWentWrong')}
                </Typography>
                <Typography
                    variant={'body'}
                    color={'textSecondary'}
                    fontWeight={600}
                >
                    {message || t('somethingWentWrongDescription')}
                </Typography>
            </Stack>
        </Container>
    );
}
