import { HttpStatusCode } from 'axios';
import { useMemo } from 'react';
import { match } from '@bookr-technologies/core';
import { useI18n } from '@bookr-technologies/i18n';
import { ErrorPageHttpProps } from '../types';
import { ErrorPageDefault } from './ErrorPageDefault';

export function ErrorPageHttp({
    statusCode,
    title,
    message,
}: ErrorPageHttpProps) {
    const { t } = useI18n();

    const computedTitle = useMemo(
        () =>
            title ||
            match(statusCode, {
                [HttpStatusCode.NotFound]: t('errors.notFound'),
            }),
        [statusCode, t, title],
    );

    const computedMessage = useMemo(
        () =>
            message ||
            match(statusCode, {
                [HttpStatusCode.NotFound]: t('errors.notFoundMessage'),
            }),
        [message, statusCode, t],
    );

    return <ErrorPageDefault title={computedTitle} message={computedMessage} />;
}
