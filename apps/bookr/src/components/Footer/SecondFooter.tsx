import Container from '@mui/material/Container';
import Grid from '@mui/material/Grid';
import Link from '@mui/material/Link';
import Typography from '@mui/material/Typography';
import { hrefTarget } from '@bookr-technologies/core';
import { useI18n } from '@bookr-technologies/i18n';
import { LanguageSelector } from './LanguageSelector';

export function SecondFooter() {
    const year = new Date().getFullYear();
    const { t } = useI18n();

    return (
        <Container>
            <Grid container py={2} alignItems={'center'} rowSpacing={1}>
                <Grid
                    container
                    item
                    xs={12}
                    sm={6}
                    md={4}
                    columnGap={4}
                    justifyContent={{ xs: 'center', md: 'flex-start' }}
                >
                    <Link
                        variant={'footnote'}
                        fontWeight={600}
                        underline={'hover'}
                        {...hrefTarget('/documents/terms-and-conditions.pdf')}
                    >
                        {t('termsAndConditions')}
                    </Link>

                    <Link
                        variant={'footnote'}
                        fontWeight={600}
                        underline={'hover'}
                        {...hrefTarget('/documents/privacy-policy.pdf')}
                    >
                        {t('privacyPolicy')}
                    </Link>
                </Grid>
                <Grid
                    container
                    item
                    xs={12}
                    sm={6}
                    md={4}
                    justifyContent={{ xs: 'center', md: 'center' }}
                >
                    <Typography
                        variant={'footnote'}
                        fontWeight={600}
                        color={'textSecondary'}
                    >
                        &copy; {year} Bookr Technologies SA
                    </Typography>
                </Grid>
                <Grid
                    container
                    item
                    xs={12}
                    md={4}
                    justifyContent={{ xs: 'center', md: 'flex-end' }}
                >
                    <LanguageSelector />
                </Grid>
            </Grid>
        </Container>
    );
}
