import Container from '@mui/material/Container';
import Grid from '@mui/material/Grid';
import Link from '@mui/material/Link';
import Typography from '@mui/material/Typography';
import { useMemo } from 'react';
import { hrefTarget } from '@bookr-technologies/core';
import { useI18n } from '@bookr-technologies/i18n';
import { Logo } from '@bookr-technologies/ui/components';
import { getExternalLink } from '~/lib/links';

const getLinks = (lang: string) =>
    [
        // {
        //     title: 'footer.links.personal',
        //     links: [
        //         {
        //             title: 'footer.links.hairstyle',
        //             href: RouteLinks.search({
        //                 location,
        //                 category: 'hairstyle',
        //             }),
        //         },
        //         {
        //             title: 'footer.links.medical',
        //             href: RouteLinks.search({ location, category: 'medical' }),
        //         },
        //         {
        //             title: 'footer.links.massage',
        //             href: RouteLinks.search({ location, category: 'massage' }),
        //         },
        //         {
        //             title: 'footer.links.beauty',
        //             href: RouteLinks.search({ location, category: 'beauty' }),
        //         },
        //     ],
        // },
        {
            title: 'footer.links.forEveryone',
            links: [
                {
                    title: 'footer.links.blog',
                    href: getExternalLink(lang, 'blog'),
                },
                {
                    title: 'footer.links.aboutUs',
                    href: getExternalLink(lang, 'about'),
                },
                {
                    title: 'footer.links.faq',
                    href: getExternalLink(lang, 'faqs'),
                },
                {
                    title: 'footer.links.newsletter',
                    href: getExternalLink(lang, 'newsletter'),
                },
                {
                    title: 'footer.links.becomeBusiness',
                    href: getExternalLink(lang, 'business'),
                },
            ],
        },
        {
            title: 'footer.links.forBusiness',
            links: [
                {
                    title: 'footer.links.bookrBusiness',
                    href: getExternalLink(lang, 'business'),
                },
                {
                    title: 'footer.links.beauty',
                    href: getExternalLink(lang, 'beauty'),
                },
                {
                    title: 'footer.links.medical',
                    href: getExternalLink(lang, 'business'),
                },
                {
                    title: 'footer.links.pricing',
                    href: getExternalLink(lang, 'pricing'),
                },
            ],
        },
        // {
        //     title: 'footer.links.company',
        //     links: [
        //         // { title: 'footer.links.blog', href: '/blog' },
        //         // { title: 'footer.links.about', href: '#' },
        //         // { title: 'footer.links.faq', href: '#' },
        //         // { title: 'footer.links.community', href: '#' },
        //     ],
        // },
        {
            title: 'footer.links.socialMedia',
            links: [
                {
                    title: 'footer.links.instagram',
                    href: 'https://www.instagram.com/bookr.ro',
                },
                {
                    title: 'footer.links.linkedin',
                    href: 'https://www.linkedin.com/company/bookrapp',
                },
                {
                    title: 'footer.links.facebook',
                    href: 'https://www.facebook.com/BookrApp',
                },
                // { title: 'footer.links.twitter', href: '#' },
            ],
        },
    ] as const;

export function PrimaryFooter() {
    const { t, lang } = useI18n();
    const links = useMemo(() => getLinks(lang), [lang]);

    return (
        <Container>
            <Grid
                container
                justifyContent={'space-between'}
                py={{
                    xs: 4,
                    sm: 8,
                }}
                rowSpacing={4}
            >
                <Grid container flexDirection={'column'} item md={3}>
                    <Logo color={'#111'} />
                    <Typography
                        maxWidth={220}
                        variant={'subhead'}
                        fontWeight={600}
                        color={'textSecondary'}
                        mt={2}
                    >
                        {t('footerCaption')}
                    </Typography>
                </Grid>

                <Grid container item md={8} spacing={3}>
                    {links.map(({ title, links }) => (
                        <Grid
                            container
                            item
                            sm={6}
                            md
                            key={title}
                            flexDirection={'column'}
                            alignItems={'flex-start'}
                        >
                            <Typography variant={'callout'} fontWeight={800}>
                                {t(title)}
                            </Typography>
                            {links.map(({ title, href }) => (
                                <Link
                                    key={title}
                                    variant={'subhead'}
                                    fontWeight={600}
                                    color={'textSecondary'}
                                    mt={2}
                                    underline={'hover'}
                                    {...hrefTarget(href)}
                                >
                                    {t(title)}
                                </Link>
                            ))}
                        </Grid>
                    ))}
                </Grid>
            </Grid>
        </Container>
    );
}
