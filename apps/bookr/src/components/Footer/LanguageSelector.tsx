import LanguageIcon from '@mui/icons-material/Language';
import Button from '@mui/material/Button';
import { useEvent } from '@bookr-technologies/hooks';
import { useI18n } from '@bookr-technologies/i18n';

export function LanguageSelector() {
    const { t, locale, setLocale } = useI18n();

    const handleClick = useEvent(() => {
        setLocale(locale === 'ro' ? 'en' : 'ro');
    });
    return (
        <Button
            startIcon={<LanguageIcon color={'info'} />}
            onClick={handleClick}
        >
            {locale === 'ro' ? t('english') : t('romanian')}
        </Button>
    );
}
