import Button from '@mui/material/Button';
import { alpha, styled } from '@mui/material/styles';

export const StyledVirtualTourButton = styled(Button)({
    justifyContent: 'space-between',
    '.MuiButton-endIcon': {
        width: 22,
        minHeight: 1,
    },
});

export const StyledIframeContainer = styled('div')({
    position: 'relative',
    width: '100%',
    paddingBottom: '56.4%',
    background:
        'radial-gradient(ellipse at 50% bottom,#474d53 0,#232628 53%,#111213 100%)',
});

export const StyledIframe = styled('iframe')({
    position: 'absolute',
    top: -1,
    left: -1,
    width: 'calc(100% + 2px)',
    height: 'calc(100% + 2px)',
    border: 0,
    background:
        'radial-gradient(ellipse at 50% bottom,#474d53 0,#232628 53%,#111213 100%)',
});

export const StyledClose = styled(Button)(({ theme }) => ({
    position: 'absolute',
    top: 24,
    right: 24,
    zIndex: 1,
    width: 32,
    height: 32,
    minWidth: 0,
    borderRadius: 16,
    backgroundColor: alpha(theme.palette.background.default, 0.8),
    '&:hover': {
        backgroundColor: theme.palette.background.default,
    },
}));
