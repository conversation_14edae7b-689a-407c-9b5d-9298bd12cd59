import CloseIcon from '@mui/icons-material/Close';
import ViewInArIcon from '@mui/icons-material/ViewInAr';
import Dialog from '@mui/material/Dialog';
import { useState } from 'react';
import { useEvent } from '@bookr-technologies/hooks';
import { useI18n } from '@bookr-technologies/i18n';
import {
    StyledClose,
    StyledIframe,
    StyledIframeContainer,
    StyledVirtualTourButton,
} from './styles';
import { VirtualTourButtonProps } from './types';

export function VirtualTourButton({
    virtualTourUrl,
    children,
    ...rest
}: VirtualTourButtonProps) {
    const { t } = useI18n();

    const [open, setOpen] = useState(false);

    const handleClose = useEvent(() => setOpen(false));
    const handleOpen = useEvent(() => setOpen(true));

    return (
        <>
            <StyledVirtualTourButton
                variant={'contained'}
                color={'secondary'}
                onClick={handleOpen}
                startIcon={<ViewInArIcon />}
                endIcon={<></>}
                {...rest}
            >
                {children ?? t('Explore 3D virtual tour')}
            </StyledVirtualTourButton>

            <Dialog open={open} onClose={handleClose} fullWidth maxWidth={'md'}>
                <StyledIframeContainer>
                    <StyledIframe src={virtualTourUrl} />

                    <StyledClose size={'small'} onClick={handleClose}>
                        <CloseIcon fontSize={'small'} />
                    </StyledClose>
                </StyledIframeContainer>
            </Dialog>
        </>
    );
}
