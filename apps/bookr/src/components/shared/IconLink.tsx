import Link, { LinkProps } from '@mui/material/Link';
import { styled } from '@mui/material/styles';
import { ReactNode } from 'react';
import { palette } from '@bookr-technologies/ui';

const StyledLink = styled(<PERSON>, { name: '<PERSON>' })(({ theme }) => ({
    display: 'flex',
    alignItems: 'center',
    cursor: 'pointer',
    borderRadius: 8,
    backgroundColor: palette.extended.backgroundPrimary,
    transition: theme.transitions.create(['background-color']),
    '&:hover': {
        backgroundColor: palette.extended.backgroundSecondary,
    },
    '.IconLink-iconHolder': {
        width: 40,
        height: 40,
        backgroundColor: palette.gray.gray100,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: 8,
    },
    '.IconLink-content': {
        marginLeft: theme.spacing(1),
    },
}));

export const IconLink = ({
    children,
    icon,
    ...props
}: LinkProps & { icon: ReactNode }) => (
    <StyledLink
        variant={'subhead'}
        fontWeight={600}
        underline={'none'}
        {...props}
    >
        <div className={'IconLink-iconHolder'}>{icon}</div>
        {children ? (
            <span className={'IconLink-content'}>{children}</span>
        ) : null}
    </StyledLink>
);
