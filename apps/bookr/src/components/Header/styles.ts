import AppBar from '@mui/material/AppBar';
import Avatar from '@mui/material/Avatar';
import Button from '@mui/material/Button';
import Toolbar from '@mui/material/Toolbar';
import { styled } from '@mui/material/styles';
import Link from 'next/link';
import { palette } from '@bookr-technologies/ui/styles';

export const StyledHeader = styled(AppBar, { name: 'Header' })(({ theme }) => ({
    backgroundColor: palette.extended.backgroundPrimary,
    backdropFilter: 'blur(10px) saturate(240%)',
    '.MuiToolbar-root': {
        padding: theme.spacing(1.5, 0),
    },
}));

export const StyledBrandLink = styled(Link, { name: 'StyledBrandLink' })({
    display: 'inline-block',
    fontSize: 0,
});

export const StyledUserButton = styled(Button, { name: 'UserAvatar' })(
    ({ theme }) => ({
        padding: theme.spacing(0.625, 2, 0.625, 1),
    }),
);

export const StyledUserAvatar = styled(Avatar, { name: 'UserAvatar' })(
    ({ theme }) => ({
        width: theme.spacing(3.75),
        height: theme.spacing(3.75),
        fontSize: 14,
        fontWeight: 700,
    }),
);

export const StyledEmailVerificationToolbar = styled(Toolbar, {
    name: 'EmailVerification',
})(({ theme }) => ({
    backgroundColor: palette.accent.accent100,
    color: palette.accent.accent900,
    justifyContent: 'center',
    minHeight: 0,
    padding: `${theme.spacing(2)} !important`,
    [theme.breakpoints.down('sm')]: {
        '.MuiTypography-root': {
            fontSize: 13,
            lineHeight: '14px',
        },
    },
}));
