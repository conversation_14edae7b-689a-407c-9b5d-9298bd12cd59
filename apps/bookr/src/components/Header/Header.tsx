import Button from '@mui/material/Button';
import Container from '@mui/material/Container';
import GlobalStyles from '@mui/material/GlobalStyles';
import Grid from '@mui/material/Grid';
import Toolbar from '@mui/material/Toolbar';
import { Theme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import { useMemo, useRef, useState } from 'react';
import { track, TrackEvents } from '@bookr-technologies/analytics';
import { useDialog, useEvent, useWindowEvent } from '@bookr-technologies/hooks';
import { useI18n } from '@bookr-technologies/i18n';
import { AccountTypeEnum } from '@bookr-technologies/sdk';
import { Logo } from '@bookr-technologies/ui/components';
import { useUserQuery } from '~/api';
import { AuthDialog } from '~/components/AuthDialog';
import { MobileSearchBar, SearchBar } from '~/components/SearchBar';
import { useSession } from '~/components/SessionProvider';
import { EmailVerificationBanner } from './EmailVerificationBanner';
import { UserProfileDropdown } from './UserProfileDropdown';
import { StyledBrandLink, StyledHeader } from './styles';

export function Header() {
    const { t } = useI18n();
    const authDialog = useDialog();
    const { session } = useSession();
    const { data } = useUserQuery();
    const rootRef = useRef<HTMLDivElement>(null);
    const isUpXs = useMediaQuery<Theme>((theme) => theme.breakpoints.up('md'));
    const [height, setHeight] = useState(0);

    const globalStyles = useMemo(
        () => (
            <GlobalStyles
                styles={{
                    ':root': {
                        '--header-height': `${height}px`,
                    },
                }}
            />
        ),
        [height],
    );

    const handleTrackBusiness = useEvent(() => {
        track(TrackEvents.ListBusinessClicked, { from: 'header' });

        if (data?.accountType !== AccountTypeEnum.BUSINESS_OWNER) {
            window.location.href = 'https//dashboard.bookr.ro';
            return;
        }

        window.location.href = '/business';
    });

    useWindowEvent('resize', () => {
        const action = () => setHeight(rootRef.current?.offsetHeight ?? 0);

        action();
        setTimeout(action, 100);
    });

    return (
        <>
            {globalStyles}
            <StyledHeader
                ref={rootRef}
                className={'RootLayoutHeader--root'}
                variant={'outlined'}
                elevation={0}
                position={'sticky'}
                color={'primary'}
            >
                <Toolbar>
                    <Container>
                        <Grid
                            container
                            alignItems={'center'}
                            justifyContent={'space-between'}
                            flexWrap={'nowrap'}
                        >
                            <Grid
                                container
                                alignItems={'center'}
                                item
                                xs={1}
                                sm={3}
                            >
                                <StyledBrandLink href={'/'}>
                                    <Logo icon={!isUpXs} />
                                </StyledBrandLink>
                            </Grid>
                            {isUpXs ? (
                                <Grid
                                    item
                                    xs={6}
                                    container
                                    justifyContent={'center'}
                                >
                                    <SearchBar
                                        variant={'minimal'}
                                        maxWidth={500}
                                        className={'Header-searchBar'}
                                    />
                                </Grid>
                            ) : null}
                            <Grid
                                item
                                xs={12}
                                md={4}
                                lg={3}
                                container
                                justifyContent={'flex-end'}
                                gap={2}
                            >
                                <Button
                                    component={'a'}
                                    href={'/business'}
                                    variant={'outlined'}
                                    color={'info'}
                                    size={isUpXs ? 'medium' : 'small'}
                                    onClick={handleTrackBusiness}
                                >
                                    {t('listYourBusiness')}
                                </Button>

                                {session ? (
                                    <UserProfileDropdown />
                                ) : (
                                    <Button
                                        variant={'contained'}
                                        color={'info'}
                                        onClick={authDialog.open}
                                    >
                                        {t('getStarted')}
                                    </Button>
                                )}

                                {!isUpXs ? <MobileSearchBar /> : null}
                            </Grid>
                        </Grid>
                    </Container>
                </Toolbar>
                {session ? <EmailVerificationBanner /> : null}
            </StyledHeader>
            <AuthDialog {...authDialog.props} />
        </>
    );
}
