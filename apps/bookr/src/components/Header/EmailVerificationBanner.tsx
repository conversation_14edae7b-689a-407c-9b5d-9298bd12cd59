import Email from '@mui/icons-material/Email';
import Typography from '@mui/material/Typography';
import { useI18n } from '@bookr-technologies/i18n';
import { useUserQuery, useUserMetadataQuery } from '~/api';
import { StyledEmailVerificationToolbar } from './styles';

export function EmailVerificationBanner() {
    const { t } = useI18n();
    const user = useUserQuery({ skipConfiguredCheck: true });
    const userMetadata = useUserMetadataQuery(user.data?.email);

    if (
        userMetadata.isFetching ||
        !userMetadata.data ||
        userMetadata.data?.verified
    ) {
        return null;
    }

    return (
        <StyledEmailVerificationToolbar>
            <Email color={'inherit'} />
            <Typography
                color={'inherit'}
                variant={'subhead'}
                fontWeight={700}
                ml={1}
            >
                {t('verificationEmailHasBeenSent', {
                    email: user.data?.email ?? '',
                })}
            </Typography>
        </StyledEmailVerificationToolbar>
    );
}
