import AccountCircleIcon from '@mui/icons-material/AccountCircle';
import EventIcon from '@mui/icons-material/Event';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import LogoutIcon from '@mui/icons-material/Logout';
import PersonIcon from '@mui/icons-material/Person';
import Divider from '@mui/material/Divider';
import ListItemIcon from '@mui/material/ListItemIcon';
import Menu from '@mui/material/Menu';
import MenuItem from '@mui/material/MenuItem';
import { useRouter } from 'next/router';
import { MouseEvent } from 'react';
import { displayNameInitials, profilePicture } from '@bookr-technologies/core';
import { useDialog, useEvent } from '@bookr-technologies/hooks';
import { useI18n } from '@bookr-technologies/i18n';
import { useUserQuery } from '~/api';
import { signOut } from '~/lib/session';
import { StyledUserAvatar, StyledUserButton } from './styles';

const PaperProps = {
    elevation: 0,
    sx: {
        overflow: 'visible',
        filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.2))',
        mt: 1.5,
        '& .MuiList-root.MuiList-padding': {
            py: 1.5,
        },
        '& .MuiAvatar-root': {
            width: 32,
            height: 32,
            ml: -0.5,
            mr: 1,
        },
        '&:before': {
            content: '""',
            display: 'block',
            position: 'absolute',
            top: 0,
            right: 14,
            width: 10,
            height: 10,
            bgcolor: 'background.paper',
            transform: 'translateY(-50%) rotate(45deg)',
            zIndex: 0,
        },
    },
};

export function UserProfileDropdown() {
    const user = useUserQuery({ skipConfiguredCheck: true });
    const router = useRouter();
    const { t } = useI18n();
    const dialog = useDialog<Element>();
    const { photoURL } = user.data || {};

    const handleOpen = useEvent((event: MouseEvent) =>
        dialog.openWithContext(event.currentTarget),
    );

    const handleLogout = useEvent(async () => {
        await signOut();
        await router.push('/');
    });

    const handleProfile = useEvent(async () => {
        dialog.close();
        await router.push('/account/profile');
    });

    const handleAppointments = useEvent(async () => {
        dialog.close();
        await router.push('/appointments');
    });

    return (
        <>
            <StyledUserButton
                variant={'subtle'}
                endIcon={<KeyboardArrowDownIcon />}
                onClick={handleOpen}
            >
                <StyledUserAvatar src={profilePicture(photoURL)}>
                    {user.data ? (
                        displayNameInitials(user.data.displayName)
                    ) : (
                        <AccountCircleIcon />
                    )}
                </StyledUserAvatar>
            </StyledUserButton>

            <Menu
                anchorEl={dialog.context.current}
                id="user-profile-dropdown"
                slotProps={{
                    paper: PaperProps,
                }}
                transformOrigin={{ horizontal: 'right', vertical: 'top' }}
                anchorOrigin={{ horizontal: 76, vertical: 'bottom' }}
                {...dialog.props}
            >
                <MenuItem onClick={handleAppointments}>
                    <ListItemIcon>
                        <EventIcon fontSize="small" />
                    </ListItemIcon>
                    {t('Appointments')}
                </MenuItem>
                {/*<MenuItem>*/}
                {/*    <ListItemIcon>*/}
                {/*        <FavoriteIcon fontSize="small" />*/}
                {/*    </ListItemIcon>*/}
                {/*    {t('Favorites')}*/}
                {/*</MenuItem>*/}
                {/*<MenuItem>*/}
                {/*    <ListItemIcon>*/}
                {/*        <NotificationsIcon fontSize="small" />*/}
                {/*    </ListItemIcon>*/}
                {/*    {t('Notifications')}*/}
                {/*</MenuItem>*/}
                <Divider />
                <MenuItem onClick={handleProfile}>
                    <ListItemIcon>
                        <PersonIcon fontSize="small" />
                    </ListItemIcon>
                    {t('Account')}
                </MenuItem>
                {/*<MenuItem>*/}
                {/*    <ListItemIcon>*/}
                {/*        <MessageIcon fontSize="small" />*/}
                {/*    </ListItemIcon>*/}
                {/*    {t('Feedback')}*/}
                {/*</MenuItem>*/}
                {/*<MenuItem>*/}
                {/*    <ListItemIcon>*/}
                {/*        <StoreIcon fontSize="small" />*/}
                {/*    </ListItemIcon>*/}
                {/*    {t('Become a Business')}*/}
                {/*</MenuItem>*/}
                <MenuItem onClick={handleLogout}>
                    <ListItemIcon>
                        <LogoutIcon fontSize="small" />
                    </ListItemIcon>
                    {t('Logout')}
                </MenuItem>
            </Menu>
        </>
    );
}
