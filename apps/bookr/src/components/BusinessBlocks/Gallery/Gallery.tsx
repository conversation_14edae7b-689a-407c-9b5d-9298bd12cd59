/* eslint-disable @next/next/no-img-element */
import NavigateBeforeIcon from '@mui/icons-material/NavigateBefore';
import NavigateNextIcon from '@mui/icons-material/NavigateNext';
import IconButton from '@mui/material/IconButton';
import { TrackDetails, useKeenSlider } from 'keen-slider/react';
import { useMemo, useState } from 'react';
import { useEvent } from '@bookr-technologies/hooks';
import { clsx } from '@bookr-technologies/ui';
import { StyledGallery } from './styles';
import type { GalleryProps } from './types';

export function Gallery({ images }: GalleryProps) {
    const [details, setDetails] = useState<TrackDetails | null>(null);
    const [currentSlide, setCurrentSlide] = useState<number>(0);
    const [sliderRef, instanceRef] = useKeenSlider({
        loop: true,
        initial: 0,
        slides: {
            perView: 1,
            spacing: 0,
        },
        detailsChanged: (s) => setDetails(s.track.details),
        slideChanged(slider) {
            setCurrentSlide(slider.track.details.rel);
        },
        created() {
            setTimeout(() => window.dispatchEvent(new Event('resize')), 100);
        },
    });

    const scaleStyle = (idx: number) => {
        if (!details) {
            return {};
        }

        const slide = details.slides[idx];
        const scale_size = 0.25;
        const scale = 1 - (scale_size - scale_size * slide.portion);
        const rotate = 10 - slide.portion * 10;
        const opacity = slide.portion;

        return {
            opacity,
            transform: `scale(${scale}) rotate(${rotate}deg)`,
        };
    };

    const handleDotClick = useEvent((fn: () => void) => () => fn());
    const handlePrev = useEvent(() => instanceRef.current?.prev());
    const handleNext = useEvent(() => instanceRef.current?.next());

    const dots = useMemo(() => {
        const size = 5;
        const items = images.map((image, index) => ({ image, index }));

        if (items.length <= size) {
            return items;
        }

        const start = currentSlide - Math.floor(size / 2);
        const end = start + size;

        if (start < 0) {
            return items.slice(0, size);
        }

        if (end > items.length) {
            return items
                .slice(start)
                .concat(items.slice(0, size - (items.length - start)));
        }

        return items.slice(start, end);
    }, [currentSlide, images]);

    return (
        <StyledGallery elevation={0}>
            <div ref={sliderRef} className="Gallery-slider keen-slider">
                {images.map((image, index) => (
                    <div
                        key={index}
                        className="Gallery-slide keen-slider__slide"
                    >
                        <div
                            className={'Gallery-slideInner'}
                            style={scaleStyle(index)}
                        >
                            <img
                                src={image}
                                alt={`slide ${index}`}
                                className={'Gallery-image'}
                                loading={'lazy'}
                            />
                        </div>
                    </div>
                ))}
            </div>

            {images.length > 1 ? (
                <>
                    <IconButton
                        className={'Gallery-arrow Gallery-arrowPrev'}
                        onClick={handlePrev}
                    >
                        <NavigateBeforeIcon />
                    </IconButton>
                    <IconButton
                        className={'Gallery-arrow Gallery-arrowNext'}
                        onClick={handleNext}
                    >
                        <NavigateNextIcon />
                    </IconButton>

                    <nav className={'Gallery-dots'}>
                        {dots.map(({ index }) => (
                            <button
                                onClick={handleDotClick(() =>
                                    instanceRef.current?.moveToIdx(index),
                                )}
                                className={clsx('Gallery-dot', {
                                    active: index === currentSlide,
                                })}
                                key={index}
                            />
                        ))}
                    </nav>
                </>
            ) : null}
        </StyledGallery>
    );
}
