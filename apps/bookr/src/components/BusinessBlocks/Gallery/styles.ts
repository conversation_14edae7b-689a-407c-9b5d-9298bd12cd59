import Paper from '@mui/material/Paper';
import { styled } from '@mui/material/styles';

export const StyledGallery = styled(Paper, { name: 'Gallery' })(
    ({ theme }) => ({
        position: 'relative',
        flexGrow: 1,
        maxWidth: '100%',
        display: 'flex',
        overflow: 'hidden',
        width: '100%',

        [theme.breakpoints.down('sm')]: {
            borderRadius: 0,
        },
        '.Gallery-slider': {
            width: '100%',
            flex: '1 1 auto',
            position: 'relative',
        },
        '.Gallery-image': {
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            objectFit: 'cover',
            borderRadius: 12,
            [theme.breakpoints.down('sm')]: {
                borderRadius: 0,
            },
        },
        '.Gallery-slide': {
            display: 'flex',
        },
        '.Gallery-slideInner': {
            flex: '1 1 auto',
            [theme.breakpoints.down('md')]: {
                paddingBottom: '56%',
            },
        },
        '.Gallery-arrow': {
            position: 'absolute',
            top: '50%',
            transform: 'translateY(-50%)',
            zIndex: 3,
            backgroundColor: 'rgba(255, 255, 255, 0.75)',
            boxShadow: '0px 4px 74px rgba(0, 0, 0, 0.1)',
            backdropFilter: 'blur(20px) saturate(240%)',
            '&, *': {
                borderRadius: 10,
            },
            '&:hover': {
                backgroundColor: 'rgba(255, 255, 255, 0.9)',
            },
        },
        '.Gallery-arrowPrev': {
            left: 32,
        },
        '.Gallery-arrowNext': {
            right: 32,
        },
        '.Gallery-dots': {
            position: 'absolute',
            bottom: 20,
            left: '50%',
            transform: 'translateX(-50%)',
            display: 'flex',
            alignItems: 'center',
            gap: 10,
        },
        '.Gallery-dot': {
            display: 'inline-flex',
            fontSize: 0,
            width: 8,
            height: 8,
            padding: 0,
            borderRadius: 4,
            border: 'none',
            cursor: 'pointer',
            backgroundColor: 'rgba(255, 255, 255, 0.6)',
            '&:hover, &.active': {
                backgroundColor: '#fff',
            },
        },
    }),
);
