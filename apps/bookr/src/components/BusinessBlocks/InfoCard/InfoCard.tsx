import FacebookIcon from '@mui/icons-material/Facebook';
import InstagramIcon from '@mui/icons-material/Instagram';
import WebsiteIcon from '@mui/icons-material/Language';
import PhoneIcon from '@mui/icons-material/Phone';
import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import format from 'date-fns/format';
import { useMemo } from 'react';
import { hrefTarget, parseTime, utcToLocal } from '@bookr-technologies/core';
import { useLocale } from '@bookr-technologies/hooks';
import { useI18n } from '@bookr-technologies/i18n';
import { BusinessNow } from '~/components/BusinessNow';
import { IconLink } from '~/components/shared/IconLink';
import { createWorkingHoursList } from '~/lib/utilities/workingHours';
import { StyledInfoCard } from './styles';
import { InfoCardProps } from './types';

export function InfoCard({
    phone,
    website,
    instagram,
    facebook,
    workingHours,
    hideProgram,
    timezone,
}: InfoCardProps) {
    const { t } = useI18n();
    const [locale] = useLocale();

    const businessHours = useMemo(
        () => createWorkingHoursList(workingHours ?? [], locale),
        [workingHours, locale],
    );

    const timeToUTC = (value: string) => {
        const time = utcToLocal(parseTime(value, new Date()));
        return format(time, 'p', { locale });
    };

    return (
        <StyledInfoCard elevation={0}>
            <Stack gap={5}>
                {!hideProgram ? (
                    <Stack gap={2}>
                        <Grid
                            container
                            alignItems={'center'}
                            justifyContent={'space-between'}
                        >
                            <Typography variant={'title2'} fontWeight={800}>
                                {t('Program')}
                            </Typography>

                            <BusinessNow
                                workingHours={workingHours}
                                timezone={timezone}
                            />
                        </Grid>

                        <Stack gap={2}>
                            {businessHours.map((item) => (
                                <Grid
                                    key={item.day}
                                    container
                                    justifyContent={'space-between'}
                                    flexWrap={'nowrap'}
                                >
                                    <Typography
                                        fontWeight={600}
                                        variant={'body'}
                                    >
                                        {t(item.day)}
                                    </Typography>

                                    <Stack maxWidth={160} gap={2}>
                                        {item.hours?.length > 0 ? (
                                            item.hours.map((hour) => (
                                                <Typography
                                                    key={hour.id}
                                                    variant={'body'}
                                                    fontWeight={800}
                                                    align={'right'}
                                                >
                                                    {`${timeToUTC(
                                                        hour.start,
                                                    )} - ${timeToUTC(
                                                        hour.end,
                                                    )}`}
                                                </Typography>
                                            ))
                                        ) : (
                                            <Typography
                                                variant={'body'}
                                                fontWeight={800}
                                                align={'right'}
                                            >
                                                {t('closed')}
                                            </Typography>
                                        )}
                                    </Stack>
                                </Grid>
                            ))}
                        </Stack>
                    </Stack>
                ) : null}
                {phone || website || instagram || facebook ? (
                    <Stack gap={2}>
                        <Typography variant={'title2'} fontWeight={800}>
                            {t('Contact & Social')}
                        </Typography>

                        <Grid container gap={1}>
                            {phone ? (
                                <IconLink
                                    href={`tel:${phone}`}
                                    icon={<PhoneIcon />}
                                />
                            ) : null}
                            {website ? (
                                <IconLink
                                    icon={<WebsiteIcon />}
                                    {...hrefTarget(website)}
                                />
                            ) : null}
                            {instagram ? (
                                <IconLink
                                    icon={<InstagramIcon />}
                                    {...hrefTarget(
                                        urlFromUsername(instagram, 'instagram'),
                                    )}
                                />
                            ) : null}
                            {facebook ? (
                                <IconLink
                                    icon={<FacebookIcon />}
                                    {...hrefTarget(
                                        urlFromUsername(facebook, 'facebook'),
                                    )}
                                />
                            ) : null}
                        </Grid>
                    </Stack>
                ) : null}
            </Stack>
        </StyledInfoCard>
    );
}

function urlFromUsername(user: string, template: 'instagram' | 'facebook') {
    const templateURL = {
        instagram: 'https://instagram.com/{username}',
        facebook: 'https://facebook.com/{username}',
    }[template];

    if (user.startsWith('http')) {
        return user;
    }

    if (
        user.startsWith('www.') ||
        user.startsWith('instagram.com') ||
        user.startsWith('facebook.com')
    ) {
        return `http://${user}`;
    }

    return templateURL.replace('{username}', user.replace(/^@/, ''));
}
