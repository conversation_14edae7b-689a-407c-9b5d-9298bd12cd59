import AppBar from '@mui/material/AppBar';
import { GlobalStylesProps } from '@mui/material/GlobalStyles';
import { alpha, styled } from '@mui/material/styles';
import { palette } from '@bookr-technologies/ui/styles';

export const globalStyles: GlobalStylesProps['styles'] = {
    'header.RootLayoutHeader--root': {
        opacity: 0,
        pointerEvents: 'none',
        transition: 'opacity 0.3s ease-in-out',
    },
};

export const StyledHeader = styled(AppBar, { name: 'Header' })(({ theme }) => ({
    marginTop: -1,
    padding: theme.spacing(2, 0),
    backgroundColor: alpha(palette.extended.backgroundPrimary, 0.9),
    backdropFilter: 'blur(10px) saturate(240%)',
    '.MuiToolbar-root': {
        minHeight: 0,
    },
    [theme.breakpoints.down('sm')]: {
        padding: theme.spacing(1, 0),
        '.MuiToolbar-root': {
            padding: 0,
        },
    },
}));
