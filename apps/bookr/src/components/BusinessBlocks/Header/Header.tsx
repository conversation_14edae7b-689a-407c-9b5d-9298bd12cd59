import StarRateIcon from '@mui/icons-material/StarRateRounded';
import Container from '@mui/material/Container';
import Fade from '@mui/material/Fade';
import MuiGlobalStyles from '@mui/material/GlobalStyles';
import Grid from '@mui/material/Grid';
import Toolbar from '@mui/material/Toolbar';
import Typography from '@mui/material/Typography';
import { useState } from 'react';
import { num, str } from '@bookr-technologies/core';
import {
    useElement,
    useScroll,
    useWindowEvent,
} from '@bookr-technologies/hooks';
import { useI18n } from '@bookr-technologies/i18n';
import { BookNowButton } from '~/components/BookNowButton';
import { FavoriteButton } from '../../FavoriteButton';
import { globalStyles, StyledHeader } from './styles';
import { HeaderProps } from './types';

const globalStyles$ = <MuiGlobalStyles styles={globalStyles} />;

export function Header({
    businessId,
    businessName,
    rate,
    noOfReviews,
    isFavorite,
    onFavorite,
}: HeaderProps) {
    const scroll = useScroll();
    const { t, locale } = useI18n();
    const detailsCard = useElement('.DetailsCard-root');
    const [scrollThreshold, setScrollThreshold] = useState(0);

    useWindowEvent('resize', () => {
        const el = detailsCard.get();
        const rect = el?.getBoundingClientRect();
        const top = num(rect?.top) + scroll.y;

        setScrollThreshold(top + num(rect?.height) / 2);
    });

    const show = scroll.y > scrollThreshold;

    return (
        <>
            {show ? globalStyles$ : null}
            <Fade in={show} unmountOnExit>
                <StyledHeader
                    variant={'outlined'}
                    elevation={0}
                    position={'fixed'}
                    color={'primary'}
                >
                    <Toolbar>
                        <Container>
                            <Grid container alignItems={'center'}>
                                <Grid
                                    item
                                    xs
                                    flexGrow={1}
                                    flexDirection={'column'}
                                >
                                    <Typography
                                        component={'h1'}
                                        variant={'title2'}
                                        fontWeight={800}
                                    >
                                        {str(businessName)}
                                    </Typography>
                                    {noOfReviews && noOfReviews > 0 ? (
                                        <Grid container alignItems={'center'}>
                                            <StarRateIcon fontSize={'small'} />
                                            <Typography
                                                variant={'subhead'}
                                                fontWeight={600}
                                                lineHeight={'24px'}
                                                ml={0.5}
                                            >
                                                {t('businessReviewLine', {
                                                    rate,
                                                    noOfReviews,
                                                    locale,
                                                })}
                                            </Typography>
                                        </Grid>
                                    ) : null}
                                </Grid>

                                <FavoriteButton
                                    isFavorite={isFavorite}
                                    onToggle={onFavorite}
                                />
                                <BookNowButton
                                    businessId={businessId ?? ''}
                                    sx={{
                                        ml: 2,
                                        display: { xs: 'none', sm: 'flex' },
                                    }}
                                />
                            </Grid>
                        </Container>
                    </Toolbar>
                </StyledHeader>
            </Fade>
        </>
    );
}
