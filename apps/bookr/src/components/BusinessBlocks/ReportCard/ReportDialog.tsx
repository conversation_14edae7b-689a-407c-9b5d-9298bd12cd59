import CloseIcon from '@mui/icons-material/Close';
import LoadingButton from '@mui/lab/LoadingButton';
import Dialog from '@mui/material/Dialog';
import Grid from '@mui/material/Grid';
import IconButton from '@mui/material/IconButton';
import List from '@mui/material/List';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { Theme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import { useState } from 'react';
import { useEvent, useLoading } from '@bookr-technologies/hooks';
import { useI18n } from '@bookr-technologies/i18n';
import { ReportReasonEnum } from '@bookr-technologies/sdk';
import { BusinessReportReasons } from '~/api/types';
import { ReportReasonRow } from './ReportReasonRow';
import { StyledReasonsContent } from './styles';
import { ReportDialogProps } from './types';

export function ReportDialog({ onSubmit, ...rest }: ReportDialogProps) {
    const [selectedReason, setReason] = useState<ReportReasonEnum | null>(null);
    const { t } = useI18n();
    const isFullscreen = useMediaQuery((theme: Theme) =>
        theme.breakpoints.down('sm'),
    );
    const loading = useLoading();

    const handleClose = useEvent(() => rest.onClose?.({}, 'escapeKeyDown'));
    const handleReasonSelect = useEvent((reason) => setReason(reason));
    const handleSubmit = useEvent(async () => {
        if (selectedReason) {
            try {
                await loading.from(onSubmit(selectedReason));
                handleClose();
            } catch (e) {
                // TODO: handle error
            }
        }
    });

    const isReportDisabled = !selectedReason;
    const isReportLoading = loading.state;

    return (
        <Dialog fullScreen={isFullscreen} fullWidth maxWidth={'sm'} {...rest}>
            <Stack
                p={{ md: 5, xs: 3 }}
                flexGrow={1}
                gap={3}
                justifyContent={'space-between'}
            >
                <Stack>
                    <Grid container justifyContent={'flex-end'}>
                        <IconButton onClick={handleClose}>
                            <CloseIcon />
                        </IconButton>
                    </Grid>
                    <Typography variant={'title1'} fontWeight={800} mb={3}>
                        {t('reportThisBusiness')}
                    </Typography>
                    <StyledReasonsContent elevation={0}>
                        <List>
                            {BusinessReportReasons.map((reason) => (
                                <ReportReasonRow
                                    key={reason}
                                    selected={selectedReason === reason}
                                    reason={reason}
                                    onClick={handleReasonSelect}
                                />
                            ))}
                        </List>
                    </StyledReasonsContent>
                </Stack>
                <LoadingButton
                    variant={'contained'}
                    size={'large'}
                    onClick={handleSubmit}
                    disabled={isReportDisabled}
                    loading={isReportLoading}
                >
                    {t('report')}
                </LoadingButton>
            </Stack>
        </Dialog>
    );
}
