import FlagIcon from '@mui/icons-material/Flag';
import { useDialog, useEvent } from '@bookr-technologies/hooks';
import { useI18n } from '@bookr-technologies/i18n';
import { ReportReasonEnum } from '@bookr-technologies/sdk';
import { useReportBusinessMutation } from '~/api';
import { IconLink } from '~/components/shared/IconLink';
import { ReportDialog } from './ReportDialog';
import { StyledReportCard } from './styles';
import type { ReportCardProps } from './types';

export function ReportCard({ businessId }: ReportCardProps) {
    const { t } = useI18n();
    const dialog = useDialog();
    const reportBusinessMutation = useReportBusinessMutation();

    const handleSubmit = useEvent(async (reason: ReportReasonEnum) => {
        await reportBusinessMutation.mutateAsync({ businessId, reason });
    });

    return (
        <>
            <StyledReportCard elevation={0}>
                <IconLink icon={<FlagIcon />} onClick={dialog.open}>
                    {t('Report')}
                </IconLink>
            </StyledReportCard>
            <ReportDialog onSubmit={handleSubmit} {...dialog.props} />
        </>
    );
}
