import Paper from '@mui/material/Paper';
import { styled } from '@mui/material/styles';
import { palette } from '@bookr-technologies/ui';

export const StyledReportCard = styled(Paper, { name: 'ReportCard' })(
    ({ theme }) => ({
        position: 'relative',
        padding: theme.spacing(2, 4),
        [theme.breakpoints.down('sm')]: {
            borderRadius: 0,
            padding: theme.spacing(2),
        },
    }),
);

export const StyledReasonsContent = styled(Paper, { name: 'ReasonsContent' })(
    () => ({
        backgroundColor: palette.extended.backgroundSecondary,
        overflow: 'hidden',
    }),
);
