import { DialogProps } from '@mui/material/Dialog';
import { ReportReasonEnum } from '@bookr-technologies/sdk';

export interface ReportCardProps {
    businessId: string;
}

export interface ReportDialogProps extends Omit<DialogProps, 'onSubmit'> {
    onSubmit: (reason: ReportReasonEnum) => Promise<void>;
}

export interface ReportReasonRowProps {
    selected: boolean;
    reason: ReportReasonEnum;
    onClick(reason: ReportReasonEnum): void;
}
