import ListItemButton from '@mui/material/ListItemButton';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import Radio from '@mui/material/Radio';
import { useEvent } from '@bookr-technologies/hooks';
import { useI18n } from '@bookr-technologies/i18n';
import { ReportReasonRowProps } from './types';

export function ReportReasonRow({
    selected,
    onClick,
    reason,
}: ReportReasonRowProps) {
    const { t } = useI18n();

    const handleClick = useEvent(() => onClick(reason));

    return (
        <ListItemButton onClick={handleClick}>
            <ListItemIcon>
                <Radio checked={selected} />
            </ListItemIcon>
            <ListItemText
                primary={t(reason)}
                primaryTypographyProps={{
                    variant: 'subhead',
                    fontWeight: 600,
                }}
            />
        </ListItemButton>
    );
}
