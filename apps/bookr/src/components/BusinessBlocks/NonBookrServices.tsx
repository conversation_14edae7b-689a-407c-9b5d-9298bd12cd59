import PhoneInTalkIcon from '@mui/icons-material/PhoneInTalk';
import Paper from '@mui/material/Paper';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import { useI18n } from '@bookr-technologies/i18n';
import { CallNowButton } from '~/components/CallNowButton';

interface Props {
    businessId: string;
    businessName: string;
    phoneNumber: string;
}

export function NonBookrServices({
    businessId,
    businessName,
    phoneNumber,
}: Props) {
    const { t } = useI18n();
    return (
        <Root>
            <Stack gap={2}>
                <Typography variant={'title1'} fontWeight={800}>
                    {t('nonBookrServicesTitle')}
                </Typography>
                <Typography
                    variant={'callout'}
                    fontWeight={600}
                    color={'textSecondary'}
                    maxWidth={480}
                >
                    {t('nonBookrServicesDescription')}
                </Typography>
            </Stack>

            <CallNowButton
                phoneNumber={phoneNumber}
                businessId={businessId}
                businessName={businessName}
                color={'info'}
                size={'large'}
                startIcon={
                    <PhoneInTalkIcon
                        sx={{
                            position: 'absolute',
                            left: 18,
                            top: '50%',
                            transform: 'translateY(-50%)',
                        }}
                    />
                }
                fullWidth
            />
        </Root>
    );
}

export const Root = styled(Paper, { name: 'NonBookrServicesRoot' })(
    ({ theme }) => ({
        display: 'flex',
        flexDirection: 'column',
        position: 'relative',
        flexGrow: 1,
        padding: theme.spacing(4),
        gap: theme.spacing(4),
        [theme.breakpoints.down('sm')]: {
            borderRadius: 0,
            padding: theme.spacing(2),
        },
    }),
);
