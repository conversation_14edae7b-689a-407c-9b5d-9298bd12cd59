import Box from '@mui/material/Box';
import Paper from '@mui/material/Paper';
import { styled } from '@mui/material/styles';

export const StyledServices = styled(Paper, { name: 'Services' })(
    ({ theme }) => ({
        position: 'relative',
        flexGrow: 1,
        padding: theme.spacing(4),
        [theme.breakpoints.down('sm')]: {
            borderRadius: 0,
            padding: theme.spacing(2),
        },
    }),
);

export const StyledTitleContainer = styled(Box, {
    name: 'ServicesTitleContainer',
})(({ theme }) => ({
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    flexWrap: 'wrap',
    gap: theme.spacing(2),
    [theme.breakpoints.down('sm')]: {
        flexDirection: 'column',
        alignItems: 'flex-start',
        gap: theme.spacing(1),
    },
}));

export const StyledSearchContainer = styled(Box, {
    name: 'ServicesSearchContainer',
})(({ theme }) => ({
    display: 'flex',
    alignItems: 'center',
    position: 'relative',
    '& .MuiIconButton-root': {
        transition: theme.transitions.create(
            ['background-color', 'transform'],
            {
                duration: 300,
                easing: 'ease-in-out',
            },
        ),
        '&:hover': {
            // backgroundColor: theme.palette.action.hover,
            // transform: 'scale(1.1)',
        },
    },
    '& .Services-searchInput': {
        '& .MuiOutlinedInput-root': {
            // borderRadius: theme.spacing(3),
            // backgroundColor: theme.palette.background.paper,
            transition: theme.transitions.create(
                ['box-shadow', 'border-color'],
                {
                    duration: 300,
                    easing: 'ease-in-out',
                },
            ),
            '&:hover': {
                boxShadow: theme.shadows[2],
            },
            '&.Mui-focused': {
                boxShadow: theme.shadows[4],
            },
        },
    },
    [theme.breakpoints.down('sm')]: {
        width: '100%',
        '& .Services-searchInput': {
            width: '100%',
            '& .MuiTextField-root': {
                width: '100%',
            },
        },
    },
}));
