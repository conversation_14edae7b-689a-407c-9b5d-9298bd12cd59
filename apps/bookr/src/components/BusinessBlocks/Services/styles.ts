import Box from '@mui/material/Box';
import Paper from '@mui/material/Paper';
import { styled } from '@mui/material/styles';

export const StyledServices = styled(Paper, { name: 'Services' })(
    ({ theme }) => ({
        position: 'relative',
        flexGrow: 1,
        padding: theme.spacing(4),
        [theme.breakpoints.down('sm')]: {
            borderRadius: 0,
            padding: theme.spacing(2),
        },
    }),
);

export const StyledTitleContainer = styled(Box, {
    name: 'ServicesTitleContainer',
})(({ theme }) => ({
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    flexWrap: 'wrap',
    gap: theme.spacing(2),
    [theme.breakpoints.down('sm')]: {
        flexDirection: 'column',
        alignItems: 'flex-start',
        gap: theme.spacing(1),
    },
}));
