import Box from '@mui/material/Box';
import TextField from '@mui/material/TextField';
import Typography from '@mui/material/Typography';
import { useState } from 'react';
import { useEvent } from '@bookr-technologies/hooks';
import { useI18n } from '@bookr-technologies/i18n';
import { Services as ServicesList } from '~/components/Services';
import { StyledServices, StyledTitleContainer } from './styles';
import type { ServicesProps } from './types';

export function Services({ staffMembers, businessId }: ServicesProps) {
    const { t } = useI18n();
    const [searchTerm, setSearchTerm] = useState('');

    // Don't filter staff members, just pass the search term to filter services within each staff member
    const filteredStaffMembers = staffMembers;

    const handleSearchChange = useEvent(
        (event: React.ChangeEvent<HTMLInputElement>) => {
            setSearchTerm(event.target.value);
        },
    );

    return (
        <StyledServices elevation={0}>
            <StyledTitleContainer>
                <Typography variant={'title1'} fontWeight={800}>
                    {t('Services')}
                </Typography>

                <TextField
                    size="small"
                    label={t('searchByTyping')}
                    value={searchTerm}
                    onChange={handleSearchChange}
                    sx={{
                        minWidth: 250,
                        width: { xs: '100%', sm: 'auto' },
                    }}
                />
            </StyledTitleContainer>

            <Box
                mt={{
                    xs: 1,
                    sm: 3,
                }}
            >
                <ServicesList
                    staffMembers={filteredStaffMembers}
                    businessId={businessId}
                    searchTerm={searchTerm}
                />
            </Box>
        </StyledServices>
    );
}
