import CloseIcon from '@mui/icons-material/Close';
import SearchIcon from '@mui/icons-material/Search';
import Box from '@mui/material/Box';
import ClickAwayListener from '@mui/material/ClickAwayListener';
import Collapse from '@mui/material/Collapse';
import IconButton from '@mui/material/IconButton';
import InputAdornment from '@mui/material/InputAdornment';
import TextField from '@mui/material/TextField';
import Typography from '@mui/material/Typography';
import { useEffect, useMemo, useState } from 'react';
import { useEvent } from '@bookr-technologies/hooks';
import { useI18n } from '@bookr-technologies/i18n';
import { Services as ServicesList } from '~/components/Services';
import { StyledServices, StyledSearchContainer, StyledTitleContainer } from './styles';
import type { ServicesProps } from './types';

export function Services({ staffMembers, businessId }: ServicesProps) {
    const { t } = useI18n();
    const [searchTerm, setSearchTerm] = useState('');
    const [isSearchOpen, setIsSearchOpen] = useState(false);

    // Filter staff members based on search term
    const filteredStaffMembers = useMemo(() => {
        if (!searchTerm.trim()) {
            return staffMembers;
        }

        const searchLower = searchTerm.toLowerCase();
        return staffMembers?.filter((staffMember) =>
            staffMember.services?.some((service) =>
                service.name.toLowerCase().includes(searchLower) ||
                (service.description && service.description.toLowerCase().includes(searchLower))
            )
        );
    }, [staffMembers, searchTerm]);

    const handleSearchOpen = useEvent(() => {
        setIsSearchOpen(true);
    });

    const handleSearchClose = useEvent(() => {
        setIsSearchOpen(false);
        setSearchTerm('');
    });

    const handleSearchChange = useEvent((event: React.ChangeEvent<HTMLInputElement>) => {
        setSearchTerm(event.target.value);
    });

    const handleClickAway = useEvent(() => {
        if (isSearchOpen && !searchTerm.trim()) {
            handleSearchClose();
        }
    });

    const handleKeyDown = useEvent((event: React.KeyboardEvent) => {
        if (event.key === 'Escape') {
            handleSearchClose();
        }
    });

    // Auto-focus search input when opened
    useEffect(() => {
        if (isSearchOpen) {
            const searchInput = document.querySelector('.Services-searchInput input') as HTMLInputElement;
            if (searchInput) {
                searchInput.focus();
            }
        }
    }, [isSearchOpen]);

    return (
        <StyledServices elevation={0}>
            <StyledTitleContainer>
                <Typography variant={'title1'} fontWeight={800}>
                    {t('Services')}
                </Typography>

                <ClickAwayListener onClickAway={handleClickAway}>
                    <StyledSearchContainer>
                        {!isSearchOpen && (
                            <IconButton
                                onClick={handleSearchOpen}
                                size="small"
                                aria-label={t('search')}
                            >
                                <SearchIcon />
                            </IconButton>
                        )}

                        <Collapse
                            in={isSearchOpen}
                            orientation="horizontal"
                            timeout={300}
                        >
                            <TextField
                                className="Services-searchInput"
                                size="small"
                                placeholder={t('searchByTyping')}
                                value={searchTerm}
                                onChange={handleSearchChange}
                                onKeyDown={handleKeyDown}
                                InputProps={{
                                    startAdornment: (
                                        <InputAdornment position="start">
                                            <SearchIcon fontSize="small" />
                                        </InputAdornment>
                                    ),
                                    endAdornment: (
                                        <InputAdornment position="end">
                                            <IconButton
                                                size="small"
                                                onClick={handleSearchClose}
                                                aria-label="Close"
                                            >
                                                <CloseIcon fontSize="small" />
                                            </IconButton>
                                        </InputAdornment>
                                    ),
                                }}
                                sx={{
                                    minWidth: 250,
                                    '& .MuiOutlinedInput-root': {
                                        transition: 'all 300ms ease-in-out',
                                    },
                                }}
                            />
                        </Collapse>
                    </StyledSearchContainer>
                </ClickAwayListener>
            </StyledTitleContainer>

            <Box
                mt={{
                    xs: 1,
                    sm: 3,
                }}
            >
                <ServicesList
                    staffMembers={filteredStaffMembers}
                    businessId={businessId}
                />
            </Box>
        </StyledServices>
    );
}
