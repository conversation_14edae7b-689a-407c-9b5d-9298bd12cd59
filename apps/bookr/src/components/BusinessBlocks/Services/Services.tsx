import Box from '@mui/material/Box';
import TextField from '@mui/material/TextField';
import Typography from '@mui/material/Typography';
import { useMemo, useState } from 'react';
import { useEvent } from '@bookr-technologies/hooks';
import { useI18n } from '@bookr-technologies/i18n';
import { Services as ServicesList } from '~/components/Services';
import { StyledServices, StyledTitleContainer } from './styles';
import type { ServicesProps } from './types';

export function Services({ staffMembers, businessId }: ServicesProps) {
    const { t } = useI18n();
    const [searchTerm, setSearchTerm] = useState('');

    // Filter staff members based on search term
    const filteredStaffMembers = useMemo(() => {
        if (!searchTerm.trim()) {
            return staffMembers;
        }

        const searchLower = searchTerm.toLowerCase();
        return staffMembers?.filter((staffMember) =>
            staffMember.services?.some(
                (service) =>
                    service.name.toLowerCase().includes(searchLower) ||
                    (service.description &&
                        service.description
                            .toLowerCase()
                            .includes(searchLower)),
            ),
        );
    }, [staffMembers, searchTerm]);

    const handleSearchChange = useEvent(
        (event: React.ChangeEvent<HTMLInputElement>) => {
            setSearchTerm(event.target.value);
        },
    );

    return (
        <StyledServices elevation={0}>
            <StyledTitleContainer>
                <Typography variant={'title1'} fontWeight={800}>
                    {t('Services')}
                </Typography>

                <TextField
                    size="small"
                    label={t('searchByService')}
                    value={searchTerm}
                    onChange={handleSearchChange}
                    sx={{ minWidth: 250 }}
                />
            </StyledTitleContainer>

            <Box
                mt={{
                    xs: 1,
                    sm: 3,
                }}
            >
                <ServicesList
                    staffMembers={filteredStaffMembers}
                    businessId={businessId}
                />
            </Box>
        </StyledServices>
    );
}
