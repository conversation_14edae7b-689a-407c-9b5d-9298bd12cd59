import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import { useI18n } from '@bookr-technologies/i18n';
import { Services as ServicesList } from '~/components/Services';
import { StyledServices } from './styles';
import type { ServicesProps } from './types';

export function Services({ staffMembers, businessId }: ServicesProps) {
    const { t } = useI18n();

    return (
        <StyledServices elevation={0}>
            <Typography variant={'title1'} fontWeight={800}>
                {t('Services')}
            </Typography>
            <Box
                mt={{
                    xs: 1,
                    sm: 3,
                }}
            >
                <ServicesList
                    staffMembers={staffMembers}
                    businessId={businessId}
                />
            </Box>
        </StyledServices>
    );
}
