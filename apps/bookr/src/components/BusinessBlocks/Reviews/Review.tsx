import StarRateIcon from '@mui/icons-material/StarRateRounded';
import Avatar from '@mui/material/Avatar';
import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import formatDate from 'date-fns/format';
import parseISO from 'date-fns/parseISO';
import {
    displayFirstNameAndFirstLetterOfLastName,
    displayNameInitials,
    num,
    profilePicture,
    str,
} from '@bookr-technologies/core';
import { ReadMore } from '~/components/ReadMore';
import { ReviewProps } from './types';

export function Review({ review }: ReviewProps) {
    const formattedTime = review.dateTime
        ? formatDate(parseISO(review.dateTime), 'dd MMM yyyy')
        : '';

    const header = (
        <>
            <Avatar src={profilePicture(review?.user?.photoURL)}>
                {displayNameInitials(str(review.user?.displayName))}
            </Avatar>
            <Stack flexGrow={1}>
                <Typography variant={'callout'} fontWeight={600}>
                    {displayFirstNameAndFirstLetterOfLastName(
                        str(review.user?.displayName),
                    )}
                </Typography>
                <Typography
                    variant={'footnote'}
                    fontWeight={600}
                    color={'textSecondary'}
                >
                    {formattedTime}
                </Typography>
            </Stack>

            <Grid
                item
                xs
                container
                justifyContent={'flex-end'}
                alignItems={'center'}
                flexWrap={'nowrap'}
            >
                <StarRateIcon />
                <Typography variant={'callout'} fontWeight={800}>
                    {num(review.rating).toFixed(2)}
                </Typography>
            </Grid>
        </>
    );

    return (
        <Stack gap={1}>
            <Grid container alignItems={'flex-start'} gap={1}>
                {header}
            </Grid>
            {review.comment ? (
                <Typography
                    variant={'subhead'}
                    fontWeight={600}
                    color={'textSecondary'}
                >
                    <ReadMore
                        contents={str(review.comment)}
                        maxLength={120}
                        title={
                            <Grid
                                container
                                alignItems={'center'}
                                gap={1}
                                pr={1}
                            >
                                {header}
                            </Grid>
                        }
                    />
                </Typography>
            ) : null}
        </Stack>
    );
}
