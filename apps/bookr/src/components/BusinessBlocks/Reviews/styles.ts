import Card from '@mui/material/Card';
import Dialog from '@mui/material/Dialog';
import Stack from '@mui/material/Stack';
import { styled } from '@mui/material/styles';
import { palette } from '@bookr-technologies/ui';

export const StyledReviews = styled(Card, { name: 'Reviews' })(({ theme }) => ({
    padding: theme.spacing(4),
    [theme.breakpoints.down('sm')]: {
        borderRadius: 0,
        padding: theme.spacing(2),
    },
}));

export const StyledReviewsDialog = styled(Dialog, { name: 'ReviewsDialog' })(
    () => ({
        '.MuiDialog-paper': {
            height: '100%',
        },
    }),
);

export const StyledReviewsDialogPaper = styled(Stack, {
    name: 'ReviewsDialogPaper',
})(({ theme }) => ({
    padding: theme.spacing(2),
    backgroundColor: palette.extended.backgroundSecondary,
    borderRadius: theme.spacing(2),
    maxHeight: '100%',
    overflow: 'auto',
    [theme.breakpoints.down('sm')]: {
        borderRadius: 0,
        margin: theme.spacing(-1, -2, 0),
        backgroundColor: palette.extended.backgroundPrimary,
    },
}));
