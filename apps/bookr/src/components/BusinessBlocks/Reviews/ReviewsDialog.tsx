import CloseIcon from '@mui/icons-material/Close';
import StarRateIcon from '@mui/icons-material/StarRateRounded';
import LoadingButton from '@mui/lab/LoadingButton';
import Box from '@mui/material/Box';
import CircularProgress from '@mui/material/CircularProgress';
import Grid from '@mui/material/Grid';
import IconButton from '@mui/material/IconButton';
import LinearProgress from '@mui/material/LinearProgress';
import Radio from '@mui/material/Radio';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { Theme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import { useState } from 'react';
import { useEvent } from '@bookr-technologies/hooks';
import { useI18n } from '@bookr-technologies/i18n';
import {
    collectPages,
    useBusinessReviewsChartQuery,
    useBusinessReviewsQuery,
} from '~/api';
import { Review } from '~/components/BusinessBlocks/Reviews/Review';
import { ReviewInfo } from '~/components/ReviewInfo';
import { ReviewStars } from '~/components/ReviewStars';
import { StyledReviewsDialog, StyledReviewsDialogPaper } from './styles';
import { ReviewsDialogProps } from './types';

export function ReviewsDialog({
    businessId,
    averageRating,
    noOfReviews,
    ...rest
}: ReviewsDialogProps) {
    const { t } = useI18n();
    const [filterRating, setFilterRating] = useState<number | undefined>(
        undefined,
    );
    const reviewsChart = useBusinessReviewsChartQuery(businessId);
    const {
        data: reviewsData,
        isLoading,
        hasNextPage,
        fetchNextPage,
    } = useBusinessReviewsQuery(businessId, {
        filterRating,
        pageable: {
            page: 0,
            size: 10,
        },
    });
    const isDownSm = useMediaQuery<Theme>((theme) =>
        theme.breakpoints.down('sm'),
    );

    const reviews = collectPages(reviewsData);
    const ratings =
        reviewsChart.data?.ratings?.sort((a, b) => b.rating - a.rating) || [];
    const totals = ratings?.reduce((acc, curr) => acc + curr.noOfRatings, 0);
    const maxTotalWidth = String(
        ratings.reduce((acc, curr) => Math.max(acc, curr.noOfRatings), 0),
    ).length;
    const handleNextPage = useEvent(() => fetchNextPage());

    return (
        <StyledReviewsDialog
            open={true}
            fullScreen={isDownSm}
            fullWidth
            maxWidth={'md'}
            {...rest}
        >
            <Stack p={{ xs: 2, sm: 3 }} flexGrow={1}>
                <Grid container justifyContent={'flex-end'} mb={{ md: 1.25 }}>
                    <IconButton
                        size={'small'}
                        onClick={(e) => rest.onClose?.(e, 'backdropClick')}
                    >
                        <CloseIcon />
                    </IconButton>
                </Grid>

                <Grid
                    container
                    alignItems={'flex-start'}
                    justifyContent={'flex-start'}
                    flexGrow={1}
                >
                    <Grid item container xs={12} md={6} pr={{ md: 5 }}>
                        <Grid container alignItems={'center'} gap={1}>
                            <StarRateIcon />
                            <ReviewInfo
                                variant={'title3'}
                                fontWeight={800}
                                averageRating={averageRating}
                                noOfReviews={noOfReviews}
                                noContrast
                            />
                        </Grid>
                        <Typography
                            variant={'caption1'}
                            fontWeight={600}
                            color={'textSecondary'}
                            mt={1}
                        >
                            {t('reviewsDialogDescription')}
                        </Typography>
                        <Stack width={'100%'} mt={3} mb={2}>
                            {reviewsChart.isLoading ? (
                                <Grid
                                    container
                                    justifyContent={'center'}
                                    py={2}
                                >
                                    <CircularProgress size={24} />
                                </Grid>
                            ) : null}
                            {ratings?.map((chart) => (
                                <Grid
                                    container
                                    alignItems={'center'}
                                    key={chart.rating}
                                >
                                    <Radio
                                        size={'small'}
                                        checked={filterRating === chart.rating}
                                        onChange={() =>
                                            setFilterRating(chart.rating)
                                        }
                                    />
                                    <ReviewStars value={chart.rating} />

                                    <Box flexGrow={1} px={1}>
                                        <LinearProgress
                                            variant={'determinate'}
                                            value={
                                                (chart.noOfRatings * 100) /
                                                totals
                                            }
                                        />
                                    </Box>
                                    <Typography
                                        variant={'caption1'}
                                        fontWeight={600}
                                        color={'textSecondary'}
                                        width={`${maxTotalWidth}ch`}
                                        align={'right'}
                                    >
                                        {chart.noOfRatings}
                                    </Typography>
                                </Grid>
                            ))}
                        </Stack>
                    </Grid>
                    <Grid
                        item
                        xs={12}
                        md={6}
                        container
                        flexDirection={'column'}
                        flexGrow={1}
                        minHeight={{ md: '100%' }}
                        position={'relative'}
                    >
                        <Box
                            position={{ xs: 'relative', md: 'absolute' }}
                            top={0}
                            left={0}
                            bottom={0}
                            width={'100%'}
                        >
                            <StyledReviewsDialogPaper gap={3}>
                                {!reviews?.content?.length ? (
                                    <Grid container justifyContent={'center'}>
                                        <Typography
                                            variant={'body1'}
                                            fontWeight={600}
                                            color={'textSecondary'}
                                        >
                                            {t('No reviews found')}
                                        </Typography>
                                    </Grid>
                                ) : null}
                                {reviews?.content?.map((review) => (
                                    <Grid item xs={12} key={review.id}>
                                        <Review review={review} />
                                    </Grid>
                                ))}

                                {hasNextPage ? (
                                    <Grid container justifyContent={'center'}>
                                        <LoadingButton
                                            variant={'subtle'}
                                            onClick={handleNextPage}
                                            disabled={isLoading}
                                            loading={isLoading}
                                        >
                                            {t('Load more')}
                                        </LoadingButton>
                                    </Grid>
                                ) : null}
                            </StyledReviewsDialogPaper>
                        </Box>
                    </Grid>
                </Grid>
            </Stack>
        </StyledReviewsDialog>
    );
}
