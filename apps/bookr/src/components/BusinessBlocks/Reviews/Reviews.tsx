import Button from '@mui/material/Button';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import { useState } from 'react';
import { useEvent } from '@bookr-technologies/hooks';
import { useI18n } from '@bookr-technologies/i18n';
import { ReviewInfo } from '~/components/ReviewInfo';
import { Review } from './Review';
import { ReviewsDialog } from './ReviewsDialog';
import { StyledReviews } from './styles';
import { ReviewsProps } from './types';

export function Reviews({
    averageRating,
    noOfReviews,
    reviews,
    hasMore,
    businessId,
}: ReviewsProps) {
    const { t } = useI18n();
    const [reviewsDialogOpen, setReviewsDialogOpen] = useState(false);
    const handleOpenReviewsDialog = useEvent(() => setReviewsDialogOpen(true));
    const handleCloseReviewsDialog = useEvent(() =>
        setReviewsDialogOpen(false),
    );

    if (!reviews?.length) {
        return null;
    }

    return (
        <StyledReviews elevation={0}>
            <Grid
                container
                alignItems={'flex-start'}
                flexDirection={'column'}
                justifyContent={'flex-start'}
                mb={1}
            >
                <Typography variant={'title1'} fontWeight={800}>
                    {t('Reviews')}
                </Typography>
                <ReviewInfo
                    stars
                    variant={'body'}
                    averageRating={averageRating}
                    noOfReviews={noOfReviews}
                    stackProps={{
                        marginTop: {
                            md: 4,
                            xs: 2,
                        },
                        marginBottom: {
                            md: 4,
                            xs: 2,
                        },
                        alignItems: 'flex-start',
                    }}
                />
            </Grid>

            <Grid container rowSpacing={{ sm: 4 }} columnSpacing={{ lg: 5 }}>
                {reviews?.map((review, index) => (
                    <Grid
                        item
                        xs={12}
                        lg={6}
                        pt={{ xs: index ? 3 : 1.5, sm: 0 }}
                        key={review.id}
                    >
                        <Review review={review} />
                    </Grid>
                ))}
            </Grid>

            {hasMore ? (
                <Grid container mt={4}>
                    <Button
                        variant={'subtle'}
                        onClick={handleOpenReviewsDialog}
                    >
                        {t('View all reviews')}
                    </Button>
                    {reviewsDialogOpen ? (
                        <ReviewsDialog
                            businessId={businessId}
                            averageRating={averageRating}
                            noOfReviews={noOfReviews}
                            onClose={handleCloseReviewsDialog}
                        />
                    ) : null}
                </Grid>
            ) : null}
        </StyledReviews>
    );
}
