import { DialogProps } from '@mui/material/Dialog';
import { AppointmentReviewModel } from '@bookr-technologies/sdk';
import { Optional } from '@bookr-technologies/types';
import { ReviewInfoProps } from '~/components/ReviewInfo/types';

export interface ReviewsProps
    extends Pick<ReviewInfoProps, 'averageRating' | 'noOfReviews'> {
    reviews: Optional<AppointmentReviewModel[]>;
    hasMore: boolean;
    businessId: string;
}

export interface ReviewProps {
    review: AppointmentReviewModel;
}

export interface ReviewsDialogProps extends Omit<DialogProps, 'open'> {
    businessId: string;
    averageRating: ReviewInfoProps['averageRating'];
    noOfReviews: ReviewInfoProps['noOfReviews'];
}
