import Card from '@mui/material/Card';
import { styled } from '@mui/material/styles';
import { palette } from '@bookr-technologies/ui/styles/palette';

export const StyledBadge = styled('div')(({ theme }) => ({
    alignItems: 'center',
    backgroundColor: palette.extended.backgroundTertiary,
    borderRadius: theme.spacing(1),
    display: 'flex',
    flexWrap: 'nowrap',
    gap: theme.spacing(0.5),
    padding: theme.spacing(0.75, 1),
}));

export const StyledDetailsCard = styled(Card, { name: 'DetailsCard' })(
    ({ theme }) => ({
        padding: theme.spacing(4),
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'space-between',
        [theme.breakpoints.up('md')]: {
            '&.withImage': {
                flexDirection: 'column',
                minHeight: 520,
            },
        },
        [theme.breakpoints.down('sm')]: {
            borderRadius: 0,
            flexDirection: 'column',
            padding: theme.spacing(2),
        },
    }),
);
