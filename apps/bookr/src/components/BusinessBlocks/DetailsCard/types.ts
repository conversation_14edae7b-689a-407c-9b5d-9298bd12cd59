import { Optional } from '@bookr-technologies/types';

export interface DetailsCardProps {
    title: Optional<string>;
    businessId: Optional<string>;
    businessName: Optional<string>;
    phoneNumber: Optional<string>;
    formattedAddress: Optional<string>;
    description: Optional<string>;
    averageRating: Optional<number>;
    noOfReviews: Optional<number>;
    virtualTour?: Optional<string>;
    isInstantBooking?: Optional<boolean>;
    withImage?: Optional<boolean>;
}
