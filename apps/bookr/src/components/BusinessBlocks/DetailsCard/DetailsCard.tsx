import EventAvailableIcon from '@mui/icons-material/EventAvailable';
import PlaceIcon from '@mui/icons-material/PlaceOutlined';
import StarRateIcon from '@mui/icons-material/StarRateRounded';
import Grid from '@mui/material/Grid';
import Link from '@mui/material/Link';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { hrefTarget, str } from '@bookr-technologies/core';
import { useI18n } from '@bookr-technologies/i18n';
import { BookNowButton } from '~/components/BookNowButton';
import { CallNowButton } from '~/components/CallNowButton';
import { ReadMore } from '~/components/ReadMore/ReadMore';
import { ReviewInfo } from '~/components/ReviewInfo';
import { VirtualTourButton } from '~/components/VirtualTourButton';
import { RouteLinks } from '~/lib/routes';
import { cn } from '~/lib/utils';
import { StyledDetailsCard, StyledBadge } from './styles';
import { DetailsCardProps } from './types';

export function DetailsCard({
    title,
    description,
    formattedAddress,
    averageRating,
    noOfReviews,
    virtualTour,
    businessId,
    businessName,
    phoneNumber,
    isInstantBooking,
    withImage,
}: DetailsCardProps) {
    const { t } = useI18n();

    return (
        <StyledDetailsCard
            elevation={0}
            className={cn('DetailsCard-root', { withImage })}
        >
            <Stack gap={1.5} mb={3}>
                <Typography
                    component={'h3'}
                    variant={'title1'}
                    fontWeight={800}
                >
                    {str(title)}
                </Typography>

                <Grid container justifyContent={'flex-start'} gap={1}>
                    {isInstantBooking && (
                        <StyledBadge>
                            <EventAvailableIcon fontSize={'small'} />
                            <Typography
                                variant={'caption1'}
                                fontWeight={600}
                                color={'inherit'}
                            >
                                {t('instantBooking')}
                            </Typography>
                        </StyledBadge>
                    )}
                    {noOfReviews && noOfReviews > 0 ? (
                        <StyledBadge>
                            <StarRateIcon fontSize={'small'} />
                            <ReviewInfo
                                averageRating={averageRating}
                                noOfReviews={noOfReviews}
                            />
                        </StyledBadge>
                    ) : null}
                </Grid>

                <Grid
                    container
                    alignItems={'flex-start'}
                    flexWrap={'nowrap'}
                    gap={0.5}
                >
                    <PlaceIcon fontSize={'small'} />
                    <Link
                        variant={'footnote'}
                        fontWeight={600}
                        lineHeight={'20px'}
                        sx={{ textDecoration: 'underline' }}
                        {...hrefTarget(
                            RouteLinks.googleMapsLocation(formattedAddress),
                        )}
                    >
                        {str(formattedAddress)}
                    </Link>
                </Grid>
                <Typography
                    variant={'subhead'}
                    fontWeight={600}
                    color={'textSecondary'}
                >
                    <ReadMore
                        title={title}
                        contents={str(description)}
                        maxLength={128}
                    />
                </Typography>
            </Stack>

            <Stack
                mt={withImage ? { xs: 2, md: 10 } : 0}
                alignItems={!withImage ? 'flex-start' : undefined}
                justifyContent={!withImage ? 'flex-start' : undefined}
                gap={2}
            >
                {virtualTour ? (
                    <VirtualTourButton
                        virtualTourUrl={virtualTour}
                        size={'large'}
                    />
                ) : null}
                {isInstantBooking ? (
                    <BookNowButton
                        businessId={businessId ?? ''}
                        size={'large'}
                    />
                ) : (
                    <CallNowButton
                        businessId={businessId ?? ''}
                        size={'large'}
                        businessName={businessName ?? ''}
                        phoneNumber={phoneNumber ?? ''}
                    />
                )}
            </Stack>
        </StyledDetailsCard>
    );
}
