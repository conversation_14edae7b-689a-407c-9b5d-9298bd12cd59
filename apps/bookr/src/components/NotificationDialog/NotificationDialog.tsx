import CheckCircleIcon from '@mui/icons-material/CheckCircleOutline';
import ErrorIcon from '@mui/icons-material/ErrorOutline';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import WarningAmberIcon from '@mui/icons-material/WarningAmber';
import LoadingButton from '@mui/lab/LoadingButton';
import Avatar, { AvatarProps } from '@mui/material/Avatar';
import Dialog from '@mui/material/Dialog';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { isValidElement, useMemo } from 'react';
import { useEvent, useLoading } from '@bookr-technologies/hooks';
import { palette } from '@bookr-technologies/ui';
import { NotificationDialogProps, NotificationDialogVariant } from './types';

export function NotificationDialog({
    title,
    message,
    icon,
    variant,
    hideButton,
    buttonLabel,
    buttonAction,
    buttonProps = {},
    ...rest
}: NotificationDialogProps) {
    const actionLoading = useLoading();
    const handleAction = useEvent(() =>
        actionLoading.from(async () => {
            rest.onClose?.({}, 'escapeKeyDown');
            if (buttonAction) {
                await buttonAction();
            }
        }),
    );

    const icon$ = useMemo(() => {
        if (icon) {
            return icon;
        }

        switch (variant) {
            case NotificationDialogVariant.Error:
                return <ErrorIcon fontSize={'inherit'} color={'inherit'} />;
            case NotificationDialogVariant.Success:
                return (
                    <CheckCircleIcon fontSize={'inherit'} color={'inherit'} />
                );
            case NotificationDialogVariant.Warning:
                return (
                    <WarningAmberIcon fontSize={'inherit'} color={'inherit'} />
                );
            default:
                return (
                    <InfoOutlinedIcon fontSize={'inherit'} color={'inherit'} />
                );
        }
    }, [icon, variant]);

    const avatarStyle = useMemo(
        (): AvatarProps['sx'] => ({
            width: 88,
            height: 88,
            fontSize: 64,
            backgroundColor: () => {
                switch (variant) {
                    case NotificationDialogVariant.Error:
                        return palette.error.error100;
                    case NotificationDialogVariant.Success:
                        return palette.success.success100;
                    case NotificationDialogVariant.Warning:
                        return palette.warning.warning100;
                    default:
                        return palette.accent.accent100;
                }
            },
            color: () => {
                switch (variant) {
                    case NotificationDialogVariant.Error:
                        return palette.foundation.error;
                    case NotificationDialogVariant.Success:
                        return palette.foundation.success;
                    case NotificationDialogVariant.Warning:
                        return palette.foundation.warning;
                    default:
                        return palette.foundation.accent;
                }
            },
        }),
        [variant],
    );

    return (
        <Dialog maxWidth={'xs'} fullWidth {...rest}>
            <Stack gap={5} p={5}>
                <Stack gap={2} alignItems={'center'}>
                    <Avatar sx={avatarStyle}>{icon$}</Avatar>
                    <Stack gap={1} alignItems={'center'}>
                        {!isValidElement(title) ? (
                            <Typography
                                variant={'title1'}
                                fontWeight={800}
                                textAlign={'center'}
                            >
                                {title}
                            </Typography>
                        ) : (
                            title
                        )}
                        {!isValidElement(message) ? (
                            <Typography
                                variant={'body'}
                                fontWeight={600}
                                color={'textSecondary'}
                                textAlign={'center'}
                            >
                                {message}
                            </Typography>
                        ) : (
                            message
                        )}
                    </Stack>
                </Stack>
                {!hideButton ? (
                    <LoadingButton
                        variant={'subtle'}
                        size={'large'}
                        onClick={handleAction}
                        loading={actionLoading.state}
                        {...buttonProps}
                    >
                        {buttonLabel}
                    </LoadingButton>
                ) : null}
            </Stack>
        </Dialog>
    );
}
