import { LoadingButtonProps } from '@mui/lab';
import { DialogProps } from '@mui/material/Dialog';
import { ReactNode } from 'react';

export enum NotificationDialogVariant {
    Success = 'success',
    Error = 'error',
    Warning = 'warning',
    Info = 'info',
}

export interface NotificationDialogProps extends Omit<DialogProps, 'title'> {
    title: string | ReactNode;
    message: string | ReactNode;
    variant?: NotificationDialogVariant;
    icon?: ReactNode;
    hideButton?: string;
    buttonLabel?: string;
    buttonAction?: () => void | Promise<void>;
    buttonProps?: LoadingButtonProps;
}
