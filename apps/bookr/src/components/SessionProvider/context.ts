import { createContext } from 'react';
import { Nullable } from '@bookr-technologies/types';
import { AuthSession } from '~/lib/session';

export interface SessionContextType {
    session?: Nullable<AuthSession>;

    setSession(session: AuthSession): void;
}

const constVoid = () => void 0;

export const SessionContext = createContext<SessionContextType>({
    session: null,
    setSession: constVoid,
});
