import { User } from 'firebase/auth';
import { ReactNode, useEffect, useMemo, useState } from 'react';
import { setUser } from '@bookr-technologies/analytics';
import { str } from '@bookr-technologies/core';
import { getAuth } from '@bookr-technologies/firebase';
import { useDebouncedEvent } from '@bookr-technologies/hooks';
import { Optional } from '@bookr-technologies/types';
import { setAccessToken, usePostSessionMutation } from '~/api';
import { signOut } from '~/lib/session';
import { SessionContext, SessionContextType } from './context';

interface Props {
    children: ReactNode;
    session: SessionContextType['session'];
}

export function SessionProvider({ children, session }: Props) {
    const [sessionValue, setSession] = useState(session);
    const postSessionMutation = usePostSessionMutation();

    if (typeof window !== 'undefined' && sessionValue?.token) {
        setAccessToken(sessionValue?.token);
    }

    const context = useMemo<SessionContextType>(
        () => ({
            session: sessionValue,
            setSession,
        }),
        [sessionValue],
    );

    const handleTokenChange = useDebouncedEvent(
        async (user: Optional<User>) => {
            if (user) {
                let tokenResult = await user.getIdTokenResult();
                const now = new Date().getTime() / 1000;
                if (now > Number(tokenResult.claims.exp) - 120) {
                    tokenResult = await user.getIdTokenResult(true);
                }

                const { token } = tokenResult;
                setAccessToken(token);
                setSession({
                    uid: user.uid,
                    token,
                });

                if (!sessionValue || session?.token !== token) {
                    await postSessionMutation.mutateAsync();
                }

                await setUser({
                    uid: user.uid,
                    email: str(user.email),
                    phone: str(user.phoneNumber),
                    displayName: str(user.displayName),
                    createdAt: user.metadata.creationTime,
                });
            } else {
                if (sessionValue) {
                    await signOut();
                }

                setSession(undefined);
            }
        },
        100,
    );

    useEffect(() => setSession(session), [session]);
    useEffect(
        () => getAuth().onIdTokenChanged(handleTokenChange),
        [handleTokenChange],
    );

    return (
        <SessionContext.Provider value={context}>
            {children}
        </SessionContext.Provider>
    );
}
