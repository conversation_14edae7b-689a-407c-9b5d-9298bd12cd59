import { useAtomValue } from 'jotai';
import { useRouter } from 'next/router';
import { useEvent } from '@bookr-technologies/hooks';
import { RouteLinks } from '~/lib/routes';
import {
    searchCategoryAtom,
    searchLocationTextAtom,
    searchTextAtom,
} from './store';

export function useSearchBar() {
    const router = useRouter();

    const location = useAtomValue(searchLocationTextAtom);
    const category = useAtomValue(searchCategoryAtom);
    const text = useAtomValue(searchTextAtom);

    const submit = useEvent(() =>
        router.push(
            RouteLinks.search({
                category,
                text,
                location:
                    location.text && location.lat > 0 && location.lng > 0
                        ? location
                        : null,
            }),
        ),
    );

    return {
        submit,
    };
}
