import { GridProps } from '@mui/material/Grid';
import { TextFieldProps } from '@mui/material/TextField';
import { HTMLAttributes } from 'react';

export interface SearchBarProps extends HTMLAttributes<HTMLDivElement> {
    variant?: 'default' | 'minimal' | 'mobile';
    maxWidth?: string | number;
}

interface SectionProps {
    variant?: SearchBarProps['variant'];
    onFocus?: (field: string) => void;
    textFieldProps?: TextFieldProps;
}

export type SearchSectionProps = SectionProps & Omit<GridProps, 'onFocus'>;
export type LocationSectionProps = SectionProps & Omit<GridProps, 'onFocus'>;
