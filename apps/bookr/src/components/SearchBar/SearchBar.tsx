import CloseIcon from '@mui/icons-material/Close';
import SearchIcon from '@mui/icons-material/Search';
import ClickAwayListener from '@mui/material/ClickAwayListener';
import Divider from '@mui/material/Divider';
import Fade from '@mui/material/Fade';
import Grid from '@mui/material/Grid';
import IconButton from '@mui/material/IconButton';
import Paper from '@mui/material/Paper';
import { useAtom } from 'jotai';
import React, {
    ForwardedRef,
    forwardRef,
    memo,
    useId,
    useRef,
    useState,
} from 'react';
import { useEvent } from '@bookr-technologies/hooks';
import { clsx, composeRefs } from '@bookr-technologies/ui';
import { useSearchBar } from '~/components/SearchBar/useSearchBar';
import { LocationResults } from './LocationResults';
import { LocationSection } from './LocationSection';
import { SearchResults } from './SearchResults';
import { SearchSection } from './SearchSection';
import { searchPopperAtom } from './store';
import { StyledPopper, StyledSearchBar, StyledSearchButton } from './styles';
import { SearchBarProps } from './types';

function SearchBarComponent(
    { variant = 'default', className, maxWidth, ...rest }: SearchBarProps,
    ref: ForwardedRef<HTMLDivElement>,
) {
    const searchBar = useSearchBar();
    const id = useId();
    const rootRef = useRef<HTMLDivElement>(null);
    const [searchPopper, setSearchPopper] = useAtom(searchPopperAtom);
    const [focusedField, setFocusedField] = useState<string | null>(null);
    const isOpen = searchPopper === id;

    const handleClose = useEvent(() => {
        if (isOpen) {
            setSearchPopper('');
            setFocusedField(null);
        }
    });

    const handleFocusedField = useEvent((field: string) => {
        setSearchPopper(field ? id : '');
        setFocusedField(field);
    });

    return (
        <ClickAwayListener onClickAway={handleClose}>
            <StyledSearchBar
                aria-describedby={id}
                ref={composeRefs(rootRef, ref)}
                className={clsx(className, {
                    ['SearchBar-' + variant]: variant,
                })}
                sx={{ maxWidth }}
                {...rest}
            >
                <Grid container flexWrap={'nowrap'} ml={-1}>
                    <SearchSection
                        variant={variant}
                        onFocus={handleFocusedField}
                    />
                    <Divider orientation={'vertical'} />
                    <LocationSection
                        variant={variant}
                        onFocus={handleFocusedField}
                    />
                </Grid>
                <StyledSearchButton
                    className={'SearchBar-searchButton'}
                    onClick={searchBar.submit}
                >
                    <SearchIcon color={'inherit'} />
                </StyledSearchButton>

                <StyledPopper
                    transition
                    id={id}
                    anchorEl={rootRef.current}
                    open={isOpen}
                    placement={'bottom'}
                >
                    {({ TransitionProps }) => (
                        <Fade {...TransitionProps}>
                            <Paper
                                className={clsx(
                                    'SearchBarPopper-paper',
                                    `SearchBarPopper-${variant}`,
                                )}
                            >
                                <Grid
                                    container
                                    flexDirection={'row'}
                                    alignItems={'center'}
                                    justifyContent={'flex-end'}
                                    position={'sticky'}
                                    top={0}
                                    height={'8px'}
                                >
                                    <IconButton
                                        size={'small'}
                                        className={
                                            'SearchBarPopper-closeButton'
                                        }
                                        onClick={handleClose}
                                    >
                                        <CloseIcon />
                                    </IconButton>
                                </Grid>
                                {focusedField === 'search' ? (
                                    <SearchResults />
                                ) : null}
                                {focusedField === 'location' ? (
                                    <LocationResults />
                                ) : null}
                            </Paper>
                        </Fade>
                    )}
                </StyledPopper>
            </StyledSearchBar>
        </ClickAwayListener>
    );
}

export const SearchBar = memo(forwardRef(SearchBarComponent));
