import BookmarkIcon from '@mui/icons-material/Bookmark';
import StarOutlineIcon from '@mui/icons-material/StarOutline';
import Chip, { ChipProps } from '@mui/material/Chip';
import Divider from '@mui/material/Divider';
import Grid from '@mui/material/Grid';
import List from '@mui/material/List';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import Typography from '@mui/material/Typography';
import { Theme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import { useAtomValue } from 'jotai';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { useMemo, useState } from 'react';
import { str } from '@bookr-technologies/core';
import { useEvent, useLocation } from '@bookr-technologies/hooks';
import { useI18n } from '@bookr-technologies/i18n';
import { CategoryModel } from '@bookr-technologies/sdk';
import { clsx } from '@bookr-technologies/ui';
import { useGetCategoriesQuery } from '~/api';
import { DisplayContent } from '~/components/DisplayContent';
import { StyledHistoryAvatar } from '~/components/SearchBar/styles';
import { RouteLinks } from '~/lib/routes';
import { searchHistoryAtom } from './store';

export function SearchResults() {
    const history = useAtomValue(searchHistoryAtom);
    const router = useRouter();
    const location = useLocation();
    const { t } = useI18n();
    const categories = useGetCategoriesQuery();
    const [allCategories, setAllCategories] = useState(false);
    const isXs = useMediaQuery<Theme>((theme) => theme.breakpoints.down('sm'));

    const searchHistory = useMemo(
        () => (history.data ?? []).slice(0, 5),
        [history],
    );
    const list = useMemo(() => {
        const list = categories.data ?? [];

        if (allCategories) {
            return list;
        }

        return list.slice(0, 5);
    }, [categories.data, allCategories]);

    const hasCategories = !!list?.length;
    const hasHistory = !!searchHistory?.length;

    const handleSeeAll = useEvent(() => setAllCategories((prev) => !prev));
    const handleCategory = useEvent((category: CategoryModel | null) =>
        router.push(RouteLinks.search({ location, category })),
    );

    return (
        <Grid
            container
            flexWrap={'nowrap'}
            gap={2}
            flexDirection={{
                xs: 'column-reverse',
                sm: 'row',
            }}
        >
            {hasHistory ? (
                <Grid
                    container
                    item
                    xs={12}
                    sm={hasCategories ? 6 : 12}
                    flexDirection={'column'}
                >
                    <Typography variant={'body'} fontWeight={800} mb={1}>
                        {t('lastSearches')}
                    </Typography>
                    <List dense disablePadding sx={{ marginLeft: -1 }}>
                        {searchHistory.map((item) => (
                            <ListItemButton
                                LinkComponent={Link}
                                key={item.id}
                                href={item.href}
                            >
                                <ListItemIcon>
                                    <StyledHistoryAvatar
                                        src={item.imageUrl ?? undefined}
                                        variant={'rounded'}
                                    >
                                        <BookmarkIcon />
                                    </StyledHistoryAvatar>
                                </ListItemIcon>
                                <ListItemText
                                    primary={item.name}
                                    secondary={item.formattedAddress}
                                    primaryTypographyProps={{
                                        variant: 'caption2',
                                        fontWeight: 800,
                                    }}
                                    secondaryTypographyProps={{
                                        variant: 'caption2',
                                        fontWeight: 600,
                                        color: 'textSecondary',
                                    }}
                                />
                            </ListItemButton>
                        ))}
                    </List>
                </Grid>
            ) : null}

            {hasHistory && hasCategories && !isXs ? (
                <Divider orientation={'vertical'} flexItem />
            ) : null}

            <DisplayContent
                loading={categories.isLoading}
                noResults={!hasCategories}
            >
                <Grid
                    container
                    item
                    xs={12}
                    sm={hasHistory ? 6 : 12}
                    flexDirection={'column'}
                >
                    <Typography variant={'body'} fontWeight={800} mb={2}>
                        {t('searchByCategory')}
                    </Typography>
                    <Grid container gap={1}>
                        <CategoryChip
                            value={null}
                            onClick={handleCategory}
                            variant={'subtle'}
                            color={'info'}
                            label={t('allCategories')}
                            icon={
                                <StarOutlineIcon
                                    fontSize={'small'}
                                    color={'inherit'}
                                />
                            }
                            className={clsx('SearchBarPopper-chip')}
                        />
                        {list
                            .filter((category) => !!category.name)
                            .map((category) => (
                                <CategoryChip
                                    key={category.name}
                                    value={category}
                                    onClick={handleCategory}
                                    variant={'subtle'}
                                    color={'info'}
                                    className={clsx('SearchBarPopper-chip')}
                                    label={t(
                                        `categories.${str(
                                            category.name,
                                        ).toLowerCase()}.name` as any,
                                    )}
                                />
                            ))}

                        <Chip
                            variant={'subtle'}
                            color={'info'}
                            label={
                                allCategories
                                    ? t('seeLessCategories')
                                    : t('seeAllCategories')
                            }
                            className={'SearchBarPopper-chip'}
                            onClick={handleSeeAll}
                        />
                    </Grid>
                </Grid>
            </DisplayContent>
        </Grid>
    );
}

function CategoryChip({
    value,
    onClick,
    ...rest
}: Omit<ChipProps, 'onClick'> & {
    value: CategoryModel | null;
    onClick: (value: CategoryModel | null) => void;
}) {
    const handleClick = useEvent(() => onClick(value));

    return <Chip onClick={handleClick} {...rest} />;
}
