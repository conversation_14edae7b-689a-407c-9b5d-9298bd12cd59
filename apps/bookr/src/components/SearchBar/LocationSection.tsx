import PlaceIcon from '@mui/icons-material/Place';
import Grid from '@mui/material/Grid';
import TextField from '@mui/material/TextField';
import Typography from '@mui/material/Typography';
import { useAtom } from 'jotai';
import { ChangeEvent, useRef } from 'react';
import {
    LocationError,
    useEvent,
    useLocation,
    useNotification,
    useValueRef,
} from '@bookr-technologies/hooks';
import { useI18n } from '@bookr-technologies/i18n';
import { clsx } from '@bookr-technologies/ui';
import { googleMapsClient } from '~/api';
import { searchLocationTextAtom } from './store';
import { LocationSectionProps } from './types';

export function LocationSection({
    variant,
    onFocus,
    textFieldProps,
    ...rest
}: LocationSectionProps) {
    const { t } = useI18n();
    const notification = useNotification();

    const locationErrorRef = useRef<LocationError | null>(null);
    const [location, setLocation] = useAtom(searchLocationTextAtom);
    const locationRef = useValueRef(location);

    const handleSearchLocation = useEvent(
        (e: ChangeEvent<HTMLInputElement>) => {
            setLocation((prev) => ({
                ...prev,
                text: e.target.value,
            }));
        },
    );

    useLocation({
        onError: (message) => {
            if (locationErrorRef.current !== message) {
                notification.warning(t(message.type));
                locationErrorRef.current = message;
            }
        },
        onSuccess: async (coords) => {
            const location = locationRef.current ?? {
                text: '',
                lat: 0,
                lng: 0,
            };

            if (location.text !== '' || location.lat || location.lng) {
                return;
            }

            const [firstResult] =
                await googleMapsClient.getPlacesByCoordinates(coords);

            if (firstResult) {
                setLocation({
                    text: firstResult.formatted_address ?? '',
                    ...googleMapsClient.geometryToLatLng(firstResult.geometry),
                });
            }
        },
    });

    return (
        <Grid item xs={6} className={'SearchBar-section'} {...rest}>
            {variant === 'default' ? (
                <Grid container gap={1} mb={1}>
                    <PlaceIcon fontSize={'small'} />
                    <Typography variant={'footnote'} fontWeight={800}>
                        {t('location')}
                    </Typography>
                </Grid>
            ) : null}
            <TextField
                fullWidth
                value={location.text}
                size={'small'}
                onChange={handleSearchLocation}
                onFocus={() => onFocus?.('location')}
                className={clsx('SearchBar-searchField', {
                    'SearchBar-searchFieldFilled': !!location,
                })}
                {...textFieldProps}
                {...{
                    [variant === 'minimal' ? 'label' : 'placeholder']:
                        t('wereDoYouNeedIt'),
                }}
            />
        </Grid>
    );
}
