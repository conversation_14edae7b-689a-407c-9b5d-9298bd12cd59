import ContentCutIcon from '@mui/icons-material/ContentCut';
import Grid from '@mui/material/Grid';
import TextField from '@mui/material/TextField';
import Typography from '@mui/material/Typography';
import { useAtom } from 'jotai';
import { useI18n } from '@bookr-technologies/i18n';
import { clsx } from '@bookr-technologies/ui';
import { searchTextAtom } from './store';
import { SearchSectionProps } from './types';

export function SearchSection({
    variant,
    onFocus,
    textFieldProps,
    ...rest
}: SearchSectionProps) {
    const { t } = useI18n();

    const [search, setSearch] = useAtom(searchTextAtom);

    return (
        <Grid item xs={6} className={'SearchBar-section'} {...rest}>
            {variant === 'default' ? (
                <Grid container gap={1} mb={1}>
                    <ContentCutIcon fontSize={'small'} />
                    <Typography variant={'footnote'} fontWeight={800}>
                        {t('services')}
                    </Typography>
                </Grid>
            ) : null}
            <TextField
                fullWidth
                value={search}
                size={'small'}
                onChange={(e) => setSearch(e.target.value)}
                onClick={() => onFocus?.('search')}
                className={clsx('SearchBar-searchField', {
                    'SearchBar-searchFieldFilled': !!search,
                })}
                {...textFieldProps}
                {...{
                    [variant === 'minimal' ? 'label' : 'placeholder']: t(
                        'whatServiceDoYouNeed',
                    ),
                }}
            />
        </Grid>
    );
}
