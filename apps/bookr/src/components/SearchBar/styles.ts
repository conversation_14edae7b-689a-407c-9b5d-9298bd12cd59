import Avatar from '@mui/material/Avatar';
import IconButton from '@mui/material/IconButton';
import Popper from '@mui/material/Popper';
import { alpha, styled } from '@mui/material/styles';
import { palette, shadows } from '@bookr-technologies/ui/styles';

export const StyledSearchBar = styled('div', { name: 'SearchBar' })(
    ({ theme }) => ({
        width: '100%',
        backgroundColor: theme.palette.background.paper,
        borderRadius: 8,
        padding: theme.spacing(1),
        display: 'flex',
        flexWrap: 'nowrap',
        '.SearchBar-searchButton': {
            minWidth: 70,
            width: 70,
            height: 70,
        },
        '.SearchBar-section': {
            padding: theme.spacing(0.5, 1.5),
            '.MuiSvgIcon-root.MuiSvgIcon-fontSizeSmall': {
                fontSize: 18,
            },
        },
        '.SearchBar-searchField': {
            '.MuiOutlinedInput-root': {
                borderRadius: 8,
                transition: theme.transitions.create(['box-shadow']),
            },
            '.MuiOutlinedInput-input': {
                backgroundColor: theme.palette.background.paper,
                borderRadius: 8,
                height: 20,
            },
            '.MuiOutlinedInput-notchedOutline': {
                border: 'none',
                outline: '1px solid transparent',
                borderRadius: 8,
                transition: theme.transitions.create(['outline']),
            },
            '.MuiInputLabel-root': {
                fontWeight: 700,
            },
            '.MuiInputLabel-root.Mui-focused': {
                color: theme.palette.info.main,
            },
            '.MuiOutlinedInput-root.Mui-focused': {
                boxShadow: `0px 11px 20px 0px ${alpha(
                    theme.palette.info.main,
                    0.2,
                )}`,
                '.MuiOutlinedInput-notchedOutline': {
                    outline: `1px solid ${theme.palette.info.main}`,
                },
            },
        },

        '&.SearchBar-minimal': {
            padding: theme.spacing(0.875),
            border: `1px solid ${palette.extended.borderSecondary}`,
            '.SearchBar-section': {
                padding: theme.spacing(0, 1),
            },
            '.SearchBar-searchButton': {
                minWidth: 40,
                width: 40,
                height: 40,
            },
            '& .MuiOutlinedInput-input': {
                paddingTop: 16,
                paddingBottom: 4,
            },
        },

        '&.SearchBar-default': {
            '& .MuiOutlinedInput-input': {
                padding: theme.spacing(1, 2),
            },
        },
    }),
);

export const StyledPopper = styled(Popper, { name: 'SearchBarPopper' })(
    ({ theme }) => ({
        zIndex: theme.zIndex.appBar + 1,
        maxWidth: 720,
        width: 'calc(100% - 32px)',
        display: 'flex',
        justifyContent: 'center',
        '.SearchBarPopper-paper': {
            overflow: 'auto',
            marginTop: theme.spacing(3),
            padding: theme.spacing(3),
            width: '100%',
            maxHeight: '35vh',
            boxShadow: shadows.medium,
            position: 'relative',
            '&.SearchBarPopper-minimal': {
                marginTop: theme.spacing(4),
            },

            '.MuiListItemButton-root, .MuiAvatar-root': {
                borderRadius: 8,
            },

            '.MuiList-root': {
                '.MuiListItemIcon-root': {
                    minWidth: 48,
                },
                '.MuiListItemButton-root': {
                    padding: theme.spacing(1),
                    marginBottom: theme.spacing(0.5),
                    '&:last-child': {
                        marginBottom: 0,
                    },
                },
                '.MuiListItemText-root': {
                    margin: 0,
                },
            },
            '.SearchBarPopper-closeButton': {
                transform: 'translateY(-50%)',
                top: '50%',
                right: 0,
            },
        },
        '.SearchBarPopper-chip': {
            borderRadius: 14,
            '&.active': {
                outline: `1px solid ${theme.palette.info.main}`,
            },
            '.MuiChip-icon': {
                fontSize: 15,
                marginLeft: 0,
            },
        },
    }),
);

export const StyledSearchButton = styled(IconButton, {
    name: 'MobileSearchBar',
})(({ theme }) => ({
    backgroundColor: theme.palette.info.main,
    color: theme.palette.info.contrastText,
    borderRadius: 12,
    '&:hover': {
        backgroundColor: theme.palette.info.light,
    },
}));

export const StyledHistoryAvatar = styled(Avatar)({
    minWidth: 40,
    minHeight: 40,
    backgroundColor: palette.foundation.accent,
});
