import PlaceIcon from '@mui/icons-material/Place';
import Avatar from '@mui/material/Avatar';
import Grid from '@mui/material/Grid';
import List from '@mui/material/List';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import Typography from '@mui/material/Typography';
import { useAtom, useSetAtom } from 'jotai';
import { useEffect } from 'react';
import { useDebouncedEvent, useEvent } from '@bookr-technologies/hooks';
import { useI18n } from '@bookr-technologies/i18n';
import { googleMapsClient, useGetPlacesByAddressQuery } from '~/api';
import { DisplayContent } from '~/components/DisplayContent';
import { searchLocationTextAtom, searchPopperAtom } from './store';

function LocationRow({
    place,
    onClick,
}: {
    onClick: (location: { text: string; lat: number; lng: number }) => void;
    place: google.maps.places.PlaceResult;
}) {
    const setSearchPopper = useSetAtom(searchPopperAtom);

    const handleClick = useEvent(() => {
        setSearchPopper('');

        onClick({
            text: place.formatted_address ?? '',
            ...googleMapsClient.geometryToLatLng(place.geometry),
        });
    });

    return (
        <ListItemButton dense onClick={handleClick}>
            <ListItemIcon>
                <Avatar variant={'rounded'} sx={{ width: 32, height: 32 }}>
                    <PlaceIcon />
                </Avatar>
            </ListItemIcon>

            <ListItemText
                primary={place.formatted_address}
                primaryTypographyProps={{
                    variant: 'subhead',
                    fontWeight: 600,
                }}
            />
        </ListItemButton>
    );
}

export function LocationResults({ onSelect }: { onSelect?: () => void }) {
    const [location, setLocation] = useAtom(searchLocationTextAtom);
    const { t } = useI18n();
    const places = useGetPlacesByAddressQuery(location.text);
    const fetch = useDebouncedEvent(async () => {
        await places.refetch();
    }, 500);

    useEffect(() => fetch(), [fetch, location.text]);

    const handleClick = useEvent((value) => {
        onSelect?.();
        setLocation(value);
    });

    return (
        <Grid container flexWrap={'nowrap'}>
            <DisplayContent
                loading={places.isLoading}
                noResults={!places.data?.length}
                error={!!places.error}
                slots={
                    location.text === ''
                        ? {
                              NoResults: (
                                  <Typography
                                      variant={'subhead'}
                                      fontWeight={600}
                                      color={'textSecondary'}
                                  >
                                      {t('typeYourPreferredLocation')}
                                  </Typography>
                              ),
                          }
                        : {}
                }
            >
                <List dense disablePadding sx={{ width: '100%' }}>
                    {places.data?.map((place) => (
                        <LocationRow
                            key={place.place_id}
                            place={place}
                            onClick={handleClick}
                        />
                    ))}
                </List>
            </DisplayContent>
        </Grid>
    );
}
