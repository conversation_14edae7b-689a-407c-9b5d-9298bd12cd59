import { atom } from 'jotai';
import { persistentAtom } from '@bookr-technologies/core/store';
import { CategoryModel } from '@bookr-technologies/sdk';
import { Optional } from '@bookr-technologies/types';

export interface SearchHistory {
    id: string;
    href: string;
    name: string;
    formattedAddress: string;
    imageUrl: Optional<string>;
}

export const searchPopperAtom = atom<string>('');
export const searchTextAtom = atom<string>('');
export const searchCategoryAtom = atom<CategoryModel | null>(null);
export const searchLocationTextAtom = atom<{
    text: string;
    lat: number;
    lng: number;
}>({
    text: '',
    lat: 0,
    lng: 0,
});

export const searchHistoryAtom = persistentAtom<SearchHistory[]>(
    'search.history',
    [],
);

export function searchHistoryItem(item: SearchHistory) {
    return (prev: SearchHistory[]) => [item, ...prev].slice(0, 10);
}
