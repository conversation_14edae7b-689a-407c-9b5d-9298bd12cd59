import CloseIcon from '@mui/icons-material/Close';
import SearchIcon from '@mui/icons-material/Search';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import Grid from '@mui/material/Grid';
import IconButton from '@mui/material/IconButton';
import Stack from '@mui/material/Stack';
import { useTheme } from '@mui/material/styles';
import React, { useState } from 'react';
import { useDialog, useEvent } from '@bookr-technologies/hooks';
import { useI18n } from '@bookr-technologies/i18n';
import { Logo } from '@bookr-technologies/ui/components';
import { LocationResults } from './LocationResults';
import { LocationSection } from './LocationSection';
import { SearchResults } from './SearchResults';
import { SearchSection } from './SearchSection';
import { useSearchBar } from './useSearchBar';

export function MobileSearchBar() {
    const { t } = useI18n();
    const dialog = useDialog();
    const [locationFocused, setLocationFocused] = useState(false);
    const theme = useTheme();
    const searchBar = useSearchBar();

    const handleSearch = useEvent(async () => {
        dialog.close();
        await searchBar.submit();
    });

    return (
        <>
            <IconButton onClick={dialog.open}>
                <SearchIcon />
            </IconButton>

            <Dialog fullScreen {...dialog.props}>
                <Grid
                    p={1.625}
                    px={2.125}
                    position={'sticky'}
                    top={0}
                    container
                    alignItems={'center'}
                    justifyContent={'space-between'}
                    borderBottom={`1px solid ${theme.palette.divider}`}
                    zIndex={2}
                    bgcolor={theme.palette.background.paper}
                >
                    <Logo />

                    <IconButton size={'small'} onClick={dialog.close}>
                        <CloseIcon />
                    </IconButton>
                </Grid>
                <Stack
                    p={2}
                    flexDirection={'column'}
                    flexGrow={1}
                    justifyContent={'flex-start'}
                    alignItems={'flex-start'}
                    gap={2}
                >
                    <Grid
                        container
                        flexDirection={'column'}
                        flexWrap={'nowrap'}
                        gap={2}
                    >
                        <SearchSection
                            variant={'minimal'}
                            xs={12}
                            textFieldProps={{ size: 'medium' }}
                            onFocus={() => setLocationFocused(false)}
                        />
                        <LocationSection
                            variant={'minimal'}
                            xs={12}
                            textFieldProps={{ size: 'medium' }}
                            onFocus={() => setLocationFocused(true)}
                        />
                    </Grid>
                    {locationFocused ? (
                        <LocationResults
                            onSelect={() => setLocationFocused(false)}
                        />
                    ) : (
                        <SearchResults />
                    )}
                </Stack>
                <Box
                    p={2}
                    bgcolor={theme.palette.background.paper}
                    position={'sticky'}
                    bottom={0}
                    borderTop={`1px solid ${theme.palette.divider}`}
                >
                    <Button
                        sx={{ height: 40 }}
                        variant={'contained'}
                        color={'info'}
                        onClick={handleSearch}
                        fullWidth
                    >
                        {t('search')}
                    </Button>
                </Box>
            </Dialog>
        </>
    );
}
