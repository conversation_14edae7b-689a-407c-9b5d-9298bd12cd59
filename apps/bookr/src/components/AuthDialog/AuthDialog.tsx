import { Theme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import { useEvent } from '@bookr-technologies/hooks';
import { AuthForm } from '../AuthForm';
import { StyledAuthDialog } from './styles';
import { AuthDialogProps } from './types';

export function AuthDialog({ required, onClose, ...rest }: AuthDialogProps) {
    const isFullscreen = useMediaQuery((theme: Theme) =>
        theme.breakpoints.down('sm'),
    );

    const handleClose = useEvent(() => onClose?.({}, 'escapeKeyDown'));
    const handleSuccess = useEvent(() => handleClose());
    const handleDialogClose = useEvent(
        ({}, reason: 'escapeKeyDown' | 'backdropClick') => {
            if (
                (reason === 'escapeKeyDown' || reason === 'backdropClick') &&
                required
            ) {
                return;
            }

            handleClose();
        },
    );

    return (
        <StyledAuthDialog
            fullWidth
            fullScreen={isFullscreen}
            maxWidth={'sm'}
            onClose={handleDialogClose}
            disableEscapeKeyDown={required}
            {...rest}
        >
            <AuthForm
                onSuccess={handleSuccess}
                onClose={handleClose}
                required={required}
            />
        </StyledAuthDialog>
    );
}
