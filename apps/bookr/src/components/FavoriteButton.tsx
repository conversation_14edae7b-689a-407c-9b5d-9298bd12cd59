import FavoriteIcon from '@mui/icons-material/Favorite';
import FavoriteBorderIcon from '@mui/icons-material/FavoriteBorder';
import CircularProgress from '@mui/material/CircularProgress';
import Grid from '@mui/material/Grid';
import IconButton, { IconButtonProps } from '@mui/material/IconButton';
import { SvgIconProps } from '@mui/material/SvgIcon';
import { useEvent, useLoading } from '@bookr-technologies/hooks';
import { useSession } from '~/components/SessionProvider';

export interface FavoriteButtonProps extends Omit<IconButtonProps, 'onToggle'> {
    isFavorite: boolean;
    iconProps?: SvgIconProps;

    onToggle(state: boolean): Promise<void>;
}

export function FavoriteButton({
    isFavorite,
    onToggle,
    onClick,
    iconProps,
    ...rest
}: FavoriteButtonProps) {
    const loading = useLoading();
    const { session } = useSession();

    const handleFavorite = useEvent(async (e) => {
        onClick?.(e);

        if (onToggle) {
            await loading
                .from(() => onToggle(!isFavorite))
                .catch(() => {
                    // TODO: treat error
                });
        }
    });

    if (!session) {
        return null;
    }

    return (
        <IconButton
            size={'small'}
            color={'error'}
            onClick={handleFavorite}
            {...rest}
        >
            {loading.state ? (
                <Grid
                    container
                    width={24}
                    height={24}
                    alignItems={'center'}
                    justifyContent={'center'}
                >
                    <CircularProgress size={18} />
                </Grid>
            ) : isFavorite ? (
                <FavoriteIcon color={'inherit'} {...iconProps} />
            ) : (
                <FavoriteBorderIcon color={'disabled'} {...iconProps} />
            )}
        </IconButton>
    );
}
