import Chip from '@mui/material/Chip';
import { useMemo } from 'react';
import { Duration } from '@bookr-technologies/core';
import { useNow } from '@bookr-technologies/hooks';
import { useI18n } from '@bookr-technologies/i18n';
import { getBusinessStatus } from '~/lib/utilities/workingHours';
import { BusinessNowProps } from './types';

export function BusinessNow({ workingHours, timezone }: BusinessNowProps) {
    const { t } = useI18n();
    const now = useNow(Duration.minutes(1));
    const { isClosed, isOpen, isBreak } = useMemo(
        () => getBusinessStatus(now, workingHours ?? [], timezone),
        [now, workingHours, timezone],
    );

    return (
        <>
            {isOpen ? (
                <Chip variant={'subtle'} label={t('opened')} color="success" />
            ) : isClosed ? (
                <Chip variant={'subtle'} label={t('closed')} color="error" />
            ) : isBreak ? (
                <Chip variant={'subtle'} label={t('break')} color="warning" />
            ) : null}
        </>
    );
}
