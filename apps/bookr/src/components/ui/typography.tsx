import { Slot } from '@radix-ui/react-slot';
import { cva, type VariantProps } from 'class-variance-authority';
import * as React from 'react';
import { cn } from '~/lib/utils';

const typographyVariants = cva('', {
    variants: {
        variant: {
            body: '',
            callout: 'text-base leading-6',
            subhead: 'text-[15px] leading-[1.5]',
            caption1: 'text-xs leading-4',
        },
        weight: {
            normal: 'font-normal',
            medium: 'font-medium',
            semi: 'font-semibold',
            bold: 'font-bold',
        },
        color: {
            primary: 'text-content-primary',
            secondary: 'text-content-secondary',
            tertiary: 'text-content-tertiary',
            inverse: 'text-content-inverse',
        },
    },
    defaultVariants: {
        variant: 'body',
        weight: 'normal',
    },
});

export interface TypographyProps
    extends Omit<React.HTMLAttributes<HTMLParagraphElement>, 'color'>,
        VariantProps<typeof typographyVariants> {
    asChild?: boolean;
}

const Typography = React.forwardRef<HTMLParagraphElement, TypographyProps>(
    ({ className, color, variant, weight, asChild = false, ...props }, ref) => {
        const Comp = asChild ? Slot : 'p';

        return (
            <Comp
                className={cn(
                    typographyVariants({
                        className,
                        color,
                        variant,
                        weight,
                    }),
                )}
                ref={ref}
                {...props}
            />
        );
    },
);
Typography.displayName = 'Typography';

export { Typography, typographyVariants };
