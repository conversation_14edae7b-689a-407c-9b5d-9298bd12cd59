import CloseIcon from '@mui/icons-material/Close';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';
import IconButton from '@mui/material/IconButton';
import Link from '@mui/material/Link';
import { Fragment, isValidElement, useMemo, useState } from 'react';
import { readMore, str } from '@bookr-technologies/core';
import { useEvent } from '@bookr-technologies/hooks';
import { useI18n } from '@bookr-technologies/i18n';
import { ReadMoreProps } from './types';

export function ReadMore({ title, contents, maxLength }: ReadMoreProps) {
    const { t } = useI18n();

    const [open, setOpen] = useState(false);
    const cutContents = useMemo(
        () => readMore(contents, maxLength),
        [contents, maxLength],
    );
    const hasBeenCut =
        cutContents.length < contents.length || contents.endsWith('...');

    const handleClose = useEvent(() => setOpen(false));
    const handleMore = useEvent(() => setOpen(true));

    function renderContents(text: string) {
        return text
            .replace(/\r/g, '')
            .split('\n')
            .map((line, index) => (
                <Fragment key={index}>
                    {index > 0 ? <br /> : null}
                    {line}
                </Fragment>
            ));
    }

    const renderedContents = useMemo(
        () => <Fragment>{renderContents(contents)}</Fragment>,
        [contents],
    );

    const renderedCutContents = useMemo(
        () => <Fragment>{renderContents(cutContents)}</Fragment>,
        [cutContents],
    );

    return (
        <>
            {renderedCutContents}
            <br />
            {hasBeenCut ? (
                <Link
                    variant={'caption1'}
                    fontWeight={800}
                    underline={'always'}
                    mt={1}
                    onClick={handleMore}
                    sx={{ cursor: 'pointer' }}
                >
                    {t('Read more')}
                </Link>
            ) : null}

            <Dialog open={open} onClose={handleClose} fullWidth maxWidth={'sm'}>
                <DialogTitle
                    variant={'title1'}
                    fontWeight={800}
                    display={'flex'}
                    justifyContent={'space-between'}
                    alignItems={'center'}
                >
                    {isValidElement(title) ? title : str(title, t('Read more'))}

                    <IconButton size={'small'} onClick={handleClose}>
                        <CloseIcon />
                    </IconButton>
                </DialogTitle>
                <DialogContent>
                    <DialogContentText
                        variant={'subhead'}
                        fontWeight={600}
                        lineHeight={1.5}
                    >
                        {renderedContents}
                    </DialogContentText>
                </DialogContent>
            </Dialog>
        </>
    );
}
