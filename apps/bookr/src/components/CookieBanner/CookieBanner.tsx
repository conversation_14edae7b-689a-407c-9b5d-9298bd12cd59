import CookieIcon from '@mui/icons-material/Cookie';
import Button from '@mui/material/Button';
import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { useAtom } from 'jotai';
import { ReactNode, useEffect, useState } from 'react';
import {
    ConsentStatus,
    setConsent,
    setCookieBannerConsent,
} from '@bookr-technologies/analytics';
import { useEvent } from '@bookr-technologies/hooks';
import { useI18n } from '@bookr-technologies/i18n';
import { cookieBannerConsentAtom } from './store';
import { StyledPaper } from './styles';

export function CookieBanner() {
    const [content, setContent] = useState<ReactNode>(null);
    const [consent, setConsentStatus] = useAtom(cookieBannerConsentAtom);
    const { t } = useI18n();

    const handleConsent = useEvent(async (status: ConsentStatus) => {
        await setConsent({ consent: status });
        setConsentStatus(status);
        setCookieBannerConsent(status);
        window.location.reload();
    });

    const handleAccept = useEvent(() => handleConsent(ConsentStatus.Granted));
    const handleDecline = useEvent(() => handleConsent(ConsentStatus.Denied));

    useEffect(() => {
        setContent(
            <StyledPaper variant={'outlined'}>
                <Stack gap={3}>
                    <Grid container>
                        <CookieIcon />

                        <Typography
                            variant={'callout'}
                            fontWeight={600}
                            maxWidth={'calc(100% - 24px)'}
                            pl={2}
                        >
                            {t('cookieBannerDescription')}
                        </Typography>
                    </Grid>

                    <Grid container justifyContent={'flex-end'} gap={3}>
                        <Button color={'error'} onClick={handleDecline}>
                            {t('Decline')}
                        </Button>
                        <Button
                            variant={'contained'}
                            color={'info'}
                            onClick={handleAccept}
                        >
                            {t('Accept')}
                        </Button>
                    </Grid>
                </Stack>
            </StyledPaper>,
        );
    }, [handleAccept, handleDecline, t]);

    if (consent && consent !== ConsentStatus.Unknown) {
        return null;
    }

    return <>{content}</>;
}
