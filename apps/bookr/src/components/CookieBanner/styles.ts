import Paper from '@mui/material/Paper';
import { alpha, styled } from '@mui/material/styles';
import { palette } from '@bookr-technologies/ui';

const offset = 2;
export const StyledPaper = styled(Paper)(({ theme }) => ({
    position: 'fixed',
    bottom: theme.spacing(offset),
    right: theme.spacing(offset),
    width: `calc(100% - ${theme.spacing(offset * 2)})`,
    maxWidth: 600,
    backgroundColor: alpha(palette.extended.backgroundPrimary, 0.8),
    backdropFilter: 'blur(10px) saturate(240%)',
    padding: theme.spacing(3),
    zIndex: theme.zIndex.modal,
}));
