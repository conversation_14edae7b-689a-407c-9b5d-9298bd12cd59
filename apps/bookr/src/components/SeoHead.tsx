import Head from 'next/head';
import { useI18n } from '@bookr-technologies/i18n';
import { ReviewAverageModel } from '@bookr-technologies/sdk';
import { WorkingHourModel } from '@bookr-technologies/sdk/bookr/models/WorkingHourModel';
import { getCdnUrl } from '~/lib/utilities/cdn';

export interface SeoMeta {
    description?: string;
    title?: string;
    keywords?: string;
    image?: string;
    name?: string;
    url?: string;
    telephone?: string;
    formattedAddress?: string;
    latitude?: number;
    longitude?: number;
    openingHours?: WorkingHourModel[];
    sameAs?: string[];
    reviewInfo?: ReviewAverageModel;
}

interface Props extends SeoMeta {
    root?: boolean;
}

function getOpeningHours(openingHours: WorkingHourModel[] = []) {
    const firstLetterUppercase = (str: string) =>
        str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();

    return JSON.stringify(
        openingHours.map((hour) => ({
            '@type': 'OpeningHoursSpecification',
            dayOfWeek: firstLetterUppercase(hour.day),
            opens: hour.start,
            closes: hour.end,
        })),
    );
}

function addBusinessJsonLd({
    name = '',
    image = '',
    id = '',
    url = '',
    telephone = '',
    formattedAddress = '',
    latitude = 0,
    longitude = 0,
    openingHours = [] as WorkingHourModel[],
    sameAs = [] as string[],
    reviewInfo = {} as ReviewAverageModel,
}) {
    return {
        __html: `
{
  "@context": "https://schema.org",
  "@type": "HealthAndBeautyBusiness",
  "name": "${name}",
  "image": "${image}",
  "@id": "${id}",
  "url": "${url}",
  "telephone": "${telephone}",
  "address": {
    "@type": "PostalAddress",
    "streetAddress": "${formattedAddress}"
  },
  "geo": {
    "@type": "GeoCoordinates",
    "latitude": ${latitude},
    "longitude": ${longitude}
  },
  "openingHoursSpecification": ${getOpeningHours(openingHours)},
  "sameAs": ${JSON.stringify(sameAs)},
  "aggregateRating": {
    "@type": "AggregateRating",
    "ratingValue": "${
        reviewInfo.averageRating > 0 ? reviewInfo.averageRating : 5
    }",
    "reviewCount": "${reviewInfo.noOfReviews > 0 ? reviewInfo.noOfReviews : 1}"
  }
}
  `,
    };
}

export function SeoHead({
    root,
    description,
    title,
    keywords,
    image,
    name,
    telephone,
    formattedAddress,
    sameAs,
    url,
    openingHours,
    longitude,
    latitude,
    reviewInfo,
}: Props) {
    const { t, locale } = useI18n();

    title ||= t('app.title');
    description ||= t('app.description');
    keywords ||= t('app.keywords');
    image ||= getCdnUrl(`/seo-banner-${locale}.png`);

    const businessHtml = addBusinessJsonLd({
        id: url,
        image,
        name,
        telephone,
        formattedAddress,
        sameAs,
        url,
        openingHours,
        longitude,
        latitude,
        reviewInfo,
    });

    return (
        <Head>
            <title>{title}</title>
            <meta
                content="width=device-width, initial-scale=1"
                name="viewport"
            />
            <meta charSet="UTF-8" />
            <meta
                name="viewport"
                content="minimum-scale=1, initial-scale=1, width=device-width, shrink-to-fit=no, user-scalable=no, viewport-fit=cover"
            />
            <link
                rel="apple-touch-icon"
                sizes="192x192"
                href="/favicon/favicon-192.png"
            />
            <link
                rel="icon"
                type="image/png"
                sizes="32x32"
                href="/favicon/favicon-32.png"
            />
            <link
                rel="icon"
                type="image/png"
                sizes="16x16"
                href="/favicon/favicon-16.png"
            />
            <link rel="manifest" href="/manifest.json" />
            <meta content={description} name="description" />
            <meta content={description} property="og:description" />
            <meta content={keywords} name="keywords" />
            <meta content={image} property="og:image" />
            <link href={image} rel="image_src" />
            <meta content="2048" property="og:image:width" />
            <meta content="1000" property="og:image:height" />
            <meta content="website" property="og:type" />
            <meta content="summary_large_image" name="twitter:card" />
            <meta name="apple-itunes-app" content="app-id=1547131136" />

            {root ? (
                <meta
                    name="facebook-domain-verification"
                    content="mqiljxqkcl6xyc1te6devd2u5nyt01"
                />
            ) : null}

            {!!formattedAddress && (
                <script
                    type="application/ld+json"
                    dangerouslySetInnerHTML={businessHtml}
                />
            )}
        </Head>
    );
}
