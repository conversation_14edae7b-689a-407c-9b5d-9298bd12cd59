import LoadingButton from '@mui/lab/LoadingButton';
import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { useRouter } from 'next/router';
import { useMemo } from 'react';
import { castArray } from '@bookr-technologies/core';
import { useEvent } from '@bookr-technologies/hooks';
import { useI18n } from '@bookr-technologies/i18n';
import { collectPages, useBusinessSearchQuery } from '~/api';
import { BusinessCard } from '~/components/BusinessCard';
import { getSearchArguments } from '~/lib/utilities/search';

export function BusinessSearch() {
    const { t } = useI18n();
    const router = useRouter();
    const searchArgs = useMemo(
        () =>
            getSearchArguments(
                castArray(router.query?.args ?? []),
                router.query,
            ),
        [router.query],
    );
    const searchQuery = useBusinessSearchQuery(searchArgs);
    const results = collectPages(searchQuery.data, 'hits');
    const totals = results.nbHits;

    const { hits } = results || {};
    const handleMore = useEvent(() => searchQuery.fetchNextPage());

    return (
        <Stack py={3} gap={3}>
            <Grid>
                <Typography variant={'title2'} fontWeight={700}>
                    {t('searchResults', { totals })}
                </Typography>
            </Grid>
            <Grid container spacing={3}>
                {hits?.map((hit) => (
                    <Grid key={hit.id} item xs={12} sm={4} md={3}>
                        <BusinessCard business={hit} />
                    </Grid>
                ))}
            </Grid>
            {searchQuery.hasNextPage ? (
                <Grid container justifyContent={'center'}>
                    <LoadingButton
                        variant={'contained'}
                        color={'primary'}
                        size={'medium'}
                        onClick={handleMore}
                        loading={searchQuery.isLoading}
                    >
                        {t('viewMore')}
                    </LoadingButton>
                </Grid>
            ) : null}
        </Stack>
    );
}
