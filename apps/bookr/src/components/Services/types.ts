import { ServiceModel, UserModel } from '@bookr-technologies/sdk';
import { Optional } from '@bookr-technologies/types';

export interface ServicesProps {
    businessId: string;
    staffMembers: Optional<UserModel[]>;
    hideExpand?: boolean;
}

export interface StaffMemberProps {
    businessId: string;
    staffMember: UserModel;
    hideExpand?: boolean;
}

export interface StaffMemberServiceProps {
    businessId: string;
    staffId: string;
    service: ServiceModel;
}
