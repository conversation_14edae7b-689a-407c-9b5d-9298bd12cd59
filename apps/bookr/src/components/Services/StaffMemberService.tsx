import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { useI18n } from '@bookr-technologies/i18n';
import { BookNowButton } from '~/components/BookNowButton';
import { ReadMore } from '~/components/ReadMore';
import { StaffMemberServiceProps } from './types';

export function StaffMemberService({
    businessId,
    service,
    staffId,
}: StaffMemberServiceProps) {
    const { t } = useI18n();

    if (service.inactive || service.hiddenFromClients) {
        return null;
    }

    return (
        <Stack pl={1} gap={1}>
            <Grid container alignItems={'center'} flexWrap={'nowrap'}>
                <Stack flexGrow={1}>
                    <Typography variant={'callout'} fontWeight={800}>
                        {service.name}
                    </Typography>
                    <Typography
                        variant={'body2'}
                        fontWeight={600}
                        color={'textSecondary'}
                        sx={{
                            display: 'flex',
                            flexWrap: 'wrap',
                            alignItems: 'center',
                            span: {
                                whiteSpace: 'nowrap',
                                '&:after': {
                                    content: '"\\00B7"',
                                    margin: '0 3px',
                                },
                                '&:last-child:after': {
                                    content: '""',
                                },
                            },
                        }}
                    >
                        <span>{`${service.price}${service.currency}`}</span>
                        <span>
                            {service.duration}{' '}
                            {t('minutes', { count: service.duration })}
                        </span>
                        {service.numberOfSessions > 0 ? (
                            <span>
                                {t('numberOfSessions', {
                                    count: service.numberOfSessions,
                                })}
                            </span>
                        ) : null}
                    </Typography>
                </Stack>
                <BookNowButton
                    businessId={businessId}
                    staffId={staffId}
                    serviceId={service.id}
                    isSubscription={service.numberOfSessions > 0}
                    color={'info'}
                    sx={{ whiteSpace: 'nowrap', minWidth: 120 }}
                />
            </Grid>
            {service.description ? (
                <Typography
                    variant={'footnote'}
                    fontWeight={600}
                    color={'textSecondary'}
                >
                    <ReadMore
                        title={service.name}
                        maxLength={128}
                        contents={service.description}
                    />
                </Typography>
            ) : null}
        </Stack>
    );
}
