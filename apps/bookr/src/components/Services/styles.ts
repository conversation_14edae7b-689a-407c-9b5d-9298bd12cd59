import ButtonBase from '@mui/material/ButtonBase';
import Grid from '@mui/material/Grid';
import { styled } from '@mui/material/styles';
import { palette } from '@bookr-technologies/ui';

export const StyledAvatarGrid = styled(Grid, { name: 'AvatarGrid' })(
    ({ theme }) => ({
        position: 'relative',
        padding: theme.spacing(1),
        borderRadius: theme.spacing(2),
        backgroundColor: palette.extended.backgroundSecondary,
        '& .MuiAvatar-root': {
            width: theme.spacing(6),
            height: theme.spacing(6),
        },
    }),
);

export const StyledMoreServices = styled(ButtonBase, { name: 'MoreServices' })(
    ({ theme }) => ({
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        flexDirection: 'column',
        padding: theme.spacing(1),
        borderRadius: theme.spacing(1),
    }),
);
