import Stack from '@mui/material/Stack';
import { useMemo } from 'react';
import { num } from '@bookr-technologies/core';
import { StaffMember } from './StaffMember';
import type { ServicesProps } from './types';

export function Services({
    staffMembers,
    businessId,
    hideExpand,
    searchTerm,
}: ServicesProps) {
    const members = useMemo(
        () =>
            (staffMembers ?? []).sort(
                (a, b) => num(a.staffRank) - num(b.staffRank),
            ),
        [staffMembers],
    );

    return (
        <Stack gap={4}>
            {members.map((staffMember) => (
                <StaffMember
                    key={staffMember.uid}
                    staffMember={staffMember}
                    businessId={businessId}
                    hideExpand={hideExpand}
                    searchTerm={searchTerm}
                />
            ))}
        </Stack>
    );
}
