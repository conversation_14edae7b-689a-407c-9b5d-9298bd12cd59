import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import Avatar from '@mui/material/Avatar';
import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { useMemo, useState } from 'react';
import { displayNameInitials, num } from '@bookr-technologies/core';
import { useEvent } from '@bookr-technologies/hooks';
import { useI18n } from '@bookr-technologies/i18n';
import { AccountTypeEnum } from '@bookr-technologies/sdk';
import { StaffMemberService } from './StaffMemberService';
import { StyledAvatarGrid, StyledMoreServices } from './styles';
import { StaffMemberProps } from './types';

const maxServices = 2;

export function StaffMember({
    staffMember,
    businessId,
    hideExpand,
}: StaffMemberProps) {
    const { t } = useI18n();
    const [expanded, setExpanded] = useState(false);
    const staffMemberServices = useMemo(() => {
        const list = [...(staffMember.services ?? [])].sort(
            (a, b) => num(a.serviceRank) - num(b.serviceRank),
        );

        return list.filter(
            (service) => !service.hiddenFromClients && !service.inactive,
        );
    }, [staffMember.services]);

    const services = useMemo(() => {
        if (expanded || hideExpand) {
            return staffMemberServices;
        }

        return staffMemberServices.slice(0, maxServices);
    }, [expanded, hideExpand, staffMemberServices]);

    const handleExpand = useEvent(() => setExpanded((prev) => !prev));

    return (
        <Stack gap={2}>
            <StyledAvatarGrid container alignItems={'center'} gap={1}>
                <Avatar src={staffMember.photoURL}>
                    {displayNameInitials(staffMember.displayName ?? '')}
                </Avatar>

                <Stack gap={0.5}>
                    <Typography variant={'callout'} fontWeight={800}>
                        {staffMember.displayName}
                    </Typography>
                    <Typography
                        variant={'footnote'}
                        fontWeight={600}
                        color={'textSecondary'}
                    >
                        {staffMember.accountType ===
                        AccountTypeEnum.BUSINESS_OWNER
                            ? t('Business Owner')
                            : t('Staff Member')}
                    </Typography>
                </Stack>
            </StyledAvatarGrid>
            <Stack gap={3}>
                {services.map((service) => (
                    <StaffMemberService
                        key={service.id}
                        service={service}
                        businessId={businessId}
                        staffId={staffMember.uid}
                    />
                ))}

                {!hideExpand &&
                staffMember.services &&
                staffMember.services.length > 2 ? (
                    <Grid
                        container
                        alignItems={'center'}
                        justifyContent={'center'}
                    >
                        <StyledMoreServices onClick={handleExpand}>
                            <Typography
                                variant={'body2'}
                                fontWeight={600}
                                color={'textSecondary'}
                            >
                                {expanded
                                    ? t('View less services')
                                    : t('View all services')}
                            </Typography>
                            {expanded ? (
                                <ExpandLessIcon color={'info'} />
                            ) : (
                                <ExpandMoreIcon color={'info'} />
                            )}
                        </StyledMoreServices>
                    </Grid>
                ) : null}
            </Stack>
        </Stack>
    );
}
