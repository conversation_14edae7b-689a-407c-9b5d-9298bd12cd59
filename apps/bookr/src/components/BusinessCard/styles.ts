import ButtonBase from '@mui/material/ButtonBase';
import Chip from '@mui/material/Chip';
import { styled } from '@mui/material/styles';
import { palette } from '@bookr-technologies/ui';
import { FavoriteButton } from '~/components/FavoriteButton';

export const BusinessCardClasses = {
    imageHolder: 'BusinessCard-imageHolder',
    image: 'BusinessCard-image',
    noImage: 'BusinessCard-noImage',
};

export const StyledCard = styled(ButtonBase)(({ theme }) => ({
    display: 'flex',
    flexDirection: 'column',
    width: '100%',
    gap: theme.spacing(2),
    borderRadius: 12,
    overflow: 'hidden',
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
    paddingBottom: theme.spacing(1),
    [`.${BusinessCardClasses.imageHolder}`]: {
        width: '100%',
        paddingBottom: '100%',
        position: 'relative',
        borderRadius: 12,
        overflow: 'hidden',
        backgroundColor: palette.extended.backgroundTertiary,
        transition: theme.transitions.create('background-color'),
        '&.noImage': {
            backgroundColor: palette.foundation.primary,
        },
    },
    [`.${BusinessCardClasses.image}`]: {
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        objectFit: 'cover',
        transform: 'scale(1.01)',
        transition: theme.transitions.create('transform'),
    },
    [`.${BusinessCardClasses.noImage}`]: {
        position: 'absolute',
        top: '50%',
        left: '50%',
        transition: theme.transitions.create('transform'),
        transform: 'translate(-50%, -50%)',
        color: '#fff',
    },

    '&:hover': {
        [`.${BusinessCardClasses.imageHolder}`]: {
            backgroundColor: palette.extended.backgroundPrimary,
            '&.noImage': {
                backgroundColor: palette.gray.gray800,
            },
        },
        [`.${BusinessCardClasses.image}`]: {
            transform: 'scale(1.03)',
        },
        [`.${BusinessCardClasses.noImage}`]: {
            transform: 'translate(-50%, -50%) scale(1.1) rotate(-6deg)',
        },
    },
}));

export const StyledBadge = styled('div')(({ theme }) => ({
    position: 'absolute',
    padding: theme.spacing(1),
    gap: theme.spacing(1),
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    color: '#fff',
    borderRadius: 8,
    backgroundColor: 'rgba(37, 37, 37, 0.55)',
    backgroundBlendMode: 'overlay, normal',
    backdropFilter: 'blur(30px) saturate(180%)',
    zIndex: 3,
}));

export const Styled3DTourBadge = styled(StyledBadge)(({ theme }) => ({
    top: theme.spacing(2),
    left: theme.spacing(2),
}));

export const StyledInstantBookingBadge = styled(StyledBadge)(({ theme }) => ({
    bottom: theme.spacing(2),
    left: theme.spacing(2),
}));

export const StyledFavoriteButton = styled(FavoriteButton)(({ theme }) => ({
    position: 'absolute',
    top: theme.spacing(2),
    right: theme.spacing(2),
    padding: theme.spacing(1),
    gap: theme.spacing(1),
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    color: '#fff',
    borderRadius: 8,
    backgroundColor: 'rgba(37, 37, 37, 0.55)',
    backgroundBlendMode: 'overlay, normal',
    backdropFilter: 'blur(30px) saturate(180%)',
    zIndex: 3,
}));

export const StyledCategoryChip = styled(Chip)(({ theme }) => ({
    padding: theme.spacing(0.25, 0.75),
    borderRadius: 14,
    backgroundColor: palette.gray.gray300,
    fontSize: theme.typography.caption2.fontSize,
    fontWeight: 600,
    color: palette.extended.contentTertiary,
    pointerEvents: 'none',
}));
