import EventAvailableIcon from '@mui/icons-material/EventAvailable';
import StarRateIcon from '@mui/icons-material/StarRateRounded';
import ViewInArIcon from '@mui/icons-material/ViewInAr';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import { useSetAtom } from 'jotai';
import Image from 'next/image';
import Link from 'next/link';
import { Fragment, useRef, useState } from 'react';
import { formatCurrency, num, str, titleCase } from '@bookr-technologies/core';
import { useEvent } from '@bookr-technologies/hooks';
import { useI18n } from '@bookr-technologies/i18n';
import {
    BusinessModel,
    CategoriesNameEnum,
    SearchBusinessModel,
} from '@bookr-technologies/sdk';
import { SingleProp } from '@bookr-technologies/types';
import { clsx } from '@bookr-technologies/ui';
import { categoryComponents } from '~/components/CategoryIcon';
import {
    searchHistoryAtom,
    searchHistoryItem,
} from '~/components/SearchBar/store';
import {
    BusinessCardClasses,
    Styled3DTourBadge,
    StyledCard,
    StyledInstantBookingBadge,
} from './styles';

export function BusinessCard({
    business,
    displayServices,
}: SingleProp<'business', BusinessModel | SearchBusinessModel> & {
    displayServices?: boolean;
}) {
    const setSearchHistory = useSetAtom(searchHistoryAtom);
    const { t } = useI18n();
    const imgRef = useRef<HTMLImageElement>(null);
    const [noImage, setNoImage] = useState(false);
    const image = business.profilePicture ?? business.photos[0];
    const extra = {
        href: `/b/${business.slug || business.id}`,
    };

    const averageRating =
        'averageRating' in business && business.averageRating
            ? business.averageRating
            : 'reviewInfo' in business && business?.reviewInfo?.averageRating
              ? business.reviewInfo.averageRating
              : null;

    const mappedCategories = (business.categories ?? []).map((category) =>
        typeof category === 'object' ? category.name : category,
    );

    const categories = mappedCategories.slice(0, 2).map((name) =>
        t(`categories.${name.toLowerCase()}.name` as any, {
            defaultValue: titleCase(name),
        }),
    );
    // .concat(
    //     mappedCategories.length > 2
    //         ? [
    //               t('n_others', {
    //                   count: mappedCategories.length,
    //                   suffix: t('categories'),
    //               }),
    //           ]
    //         : [],
    // );

    const handleError = useEvent(() => {
        if (imgRef.current) {
            imgRef.current.style.display = 'none';
        }
        setNoImage(true);
    });

    const handleClickBusiness = useEvent(() =>
        setSearchHistory(
            searchHistoryItem({
                id: business.id,
                href: extra.href,
                name: business.name,
                formattedAddress: business.formattedAddress,
                imageUrl: business.profilePicture,
            }),
        ),
    );

    // const handleFavorite = useEvent(async (state: boolean) => {
    //     console.log(state);
    // });

    const firstCategory = str(
        mappedCategories?.[0],
    ).toUpperCase() as CategoriesNameEnum;

    const CategoryComponent =
        categoryComponents[firstCategory] ||
        categoryComponents[CategoriesNameEnum.OTHER];

    return (
        <StyledCard
            LinkComponent={Link}
            disableRipple
            disableTouchRipple
            onClick={handleClickBusiness}
            {...extra}
        >
            <Grid
                container
                className={clsx(BusinessCardClasses.imageHolder, 'border', {
                    noImage: !image || noImage,
                })}
            >
                {image && !noImage ? (
                    /* eslint-disable-next-line @next/next/no-img-element */
                    <Image
                        ref={imgRef}
                        src={image}
                        alt={business.name}
                        loading={'lazy'}
                        className={BusinessCardClasses.image}
                        onError={handleError}
                        width={380}
                        height={380}
                    />
                ) : (
                    <CategoryComponent
                        width={64}
                        height={64}
                        className={BusinessCardClasses.noImage}
                    />
                )}

                {business.virtualTourURL ? (
                    <Styled3DTourBadge>
                        <ViewInArIcon fontSize={'small'} />
                        <Typography
                            variant={'caption1'}
                            fontWeight={700}
                            color={'inherit'}
                        >
                            {t('3dVirtualTour')}
                        </Typography>
                    </Styled3DTourBadge>
                ) : null}

                {/*<StyledFavoriteButton*/}
                {/*    isFavorite*/}
                {/*    onToggle={handleFavorite}*/}
                {/*    iconProps={{ fontSize: 'small' }}*/}
                {/*/>*/}

                {'services' in business && !!business.services?.length ? (
                    <StyledInstantBookingBadge>
                        <EventAvailableIcon fontSize={'small'} />
                        <Typography
                            variant={'caption1'}
                            fontWeight={700}
                            color={'inherit'}
                        >
                            {t('instantBooking')}
                        </Typography>
                    </StyledInstantBookingBadge>
                ) : null}
            </Grid>
            <Grid
                container
                flexDirection={'column'}
                alignItems={'flex-start'}
                justifyContent={'flex-start'}
                flexWrap={'nowrap'}
            >
                <Grid
                    container
                    justifyContent={'space-between'}
                    flexWrap={'nowrap'}
                >
                    <Typography
                        variant={'body'}
                        fontWeight={800}
                        align={'left'}
                        noWrap
                        sx={{ width: '100%' }}
                        title={str(business.name)}
                    >
                        {str(business.name)}
                    </Typography>

                    {num(averageRating) > 0 ? (
                        <Typography
                            variant={'subhead'}
                            fontWeight={800}
                            noWrap
                            minWidth={60}
                            align={'right'}
                            className={'flex items-center'}
                        >
                            <StarRateIcon
                                color={'inherit'}
                                fontSize={'inherit'}
                            />
                            {num(averageRating).toFixed(2)}
                        </Typography>
                    ) : null}
                </Grid>

                <Typography variant={'footnote'} color={'textSecondary'} mt={1}>
                    {categories.map((category, index) => (
                        <Fragment key={`${category}_${index}`}>
                            {index > 0 ? <>&nbsp; &bull; &nbsp;</> : null}
                            <span>{category}</span>
                        </Fragment>
                    ))}
                    <br />
                    <span className={'flex mt-1'}>
                        {business.formattedAddress}
                    </span>
                </Typography>
            </Grid>
            {displayServices && 'businessServices' in business ? (
                <Services services={business.businessServices} />
            ) : null}
        </StyledCard>
    );
}

interface ServicesProps {
    services?: {
        currency: string;
        duration: number;
        id: number;
        name: string;
        ownerName: string;
        price: number;
        serviceRank: number;
        staffRank: number;
    }[];
}

function Services({ services }: ServicesProps) {
    const { t, locale } = useI18n();

    if (!services?.length) {
        return null;
    }

    return (
        <div
            className={
                'flex flex-col py-4 border-t bg-bg-tertiary w-full gap-3'
            }
        >
            {(services || []).slice(0, 2).map((service) => (
                <div key={service.id} className={'flex flex-col p-1.5 gap-1'}>
                    <div className={'flex justify-between items-center gap-2'}>
                        <Typography
                            variant={'subhead'}
                            fontWeight={700}
                            className={
                                'overflow-ellipsis overflow-hidden whitespace-nowrap'
                            }
                            title={service.name}
                        >
                            {service.name}
                        </Typography>
                        <Typography variant={'subhead'} fontWeight={700}>
                            {formatCurrency(
                                service.price,
                                service.currency,
                                locale,
                            )}
                        </Typography>
                    </div>
                    <div className={'flex gap-1 items-center'}>
                        <Typography variant={'footnote'}>
                            <span
                                className={
                                    'font-semibold text-content-tertiary'
                                }
                            >
                                {service.duration} {t('minutes')}
                                &nbsp; &bull; &nbsp;
                            </span>
                            <span className={'font-bold'}>
                                {service.ownerName}
                            </span>
                        </Typography>
                    </div>
                </div>
            ))}

            <Typography variant={'footnote'} className={'text-brand underline'}>
                {t('seeMoreOnBusinessPage')}
            </Typography>
        </div>
    );
}
