import Button, { ButtonProps } from '@mui/material/Button';
import parsePhoneNumber from 'libphonenumber-js';
import { useCallback, useState } from 'react';
import { track, TrackEvents } from '@bookr-technologies/analytics';
import { useI18n } from '@bookr-technologies/i18n';

export type CallNowButtonProps = {
    phoneNumber: string;
    businessId: string;
    businessName: string;
} & ButtonProps;

export function CallNowButton({
    phoneNumber,
    businessId,
    businessName,
    children,
    ...rest
}: CallNowButtonProps) {
    const { t } = useI18n();
    const [clickedPhoneNumber, setClickedPhoneNumber] = useState('');
    const handleClick = useCallback(() => {
        const cleanedPhoneNumber = phoneNumber.replace(/[^()\s\d+\-]/g, '');
        const parsedPhoneNumber = parsePhoneNumber(phoneNumber) ?? {
            getURI: (): string => `tel:${cleanedPhoneNumber}`,
            formatInternational: (): string => cleanedPhoneNumber,
        };

        track(
            TrackEvents.PhoneCallAppointment,
            {
                phoneNumber,
                businessId,
                businessName,
                formattedPhoneNumber: parsedPhoneNumber?.formatInternational(),
            },
            {
                firebaseAnalytics: false,
                facebookPixel: false,
            },
        );

        setClickedPhoneNumber(parsedPhoneNumber.formatInternational());
        window.open(parsedPhoneNumber.getURI(), '_self');
    }, [businessId, businessName, phoneNumber]);

    if (!phoneNumber) {
        return null;
    }

    return (
        <Button
            variant={'contained'}
            color={'primary'}
            onClick={handleClick}
            {...rest}
        >
            {clickedPhoneNumber
                ? clickedPhoneNumber
                : (children ?? t('bookByPhone'))}
        </Button>
    );
}
