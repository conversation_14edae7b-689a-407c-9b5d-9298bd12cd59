import type { GetServerSidePropsContext, PreviewData } from 'next/types';
import { ParsedUrlQuery } from 'querystring';
import { BookrClient, NextClient, UserModel } from '@bookr-technologies/sdk';

export interface AppContext {
    requestId: string;
    accessToken?: string;
    user?: Pick<UserModel, 'uid'> | UserModel | null;
    apiClients: {
        bookr: BookrClient;
        next: NextClient;
    };

    contextValues: Record<string, any>;
}

export type NextAppContext<
    Q extends ParsedUrlQuery = ParsedUrlQuery,
    D extends PreviewData = PreviewData,
> = GetServerSidePropsContext<Q, D> & AppContext;
