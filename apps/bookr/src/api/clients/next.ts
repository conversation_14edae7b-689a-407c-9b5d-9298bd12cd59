import { getClientConfig, getServerConfig } from '@bookr-technologies/config';
import { NextClient } from '@bookr-technologies/sdk';
import { AppContext, getAccessToken } from '~/api';

const { endpoints } =
    typeof window !== 'undefined' ? getClientConfig() : getServerConfig();

export const nextClient = new NextClient({
    baseUrl: `${endpoints.nextApiUrl}/api`,
    auth: 'bearer',
    getBearer: async () => getAccessToken(),
});

export const serverSideNextClient = (context: AppContext) => {
    return new NextClient({
        baseUrl: `${endpoints.nextApiUrl}/api`,
        auth: 'bearer',
        getBearer: () => context.accessToken ?? '',
    });
};
