import { getClientConfig, getServerConfig } from '@bookr-technologies/config';
import { BookrClient } from '@bookr-technologies/sdk';
import { AppContext, getAccessToken } from '~/api';

const { endpoints } =
    typeof window !== 'undefined' ? getClientConfig() : getServerConfig();

/**
 * Bookr client, to be used in the frontend exclusively.
 */
export const bookrClient = new BookrClient({
    baseUrl: endpoints.bookrApiUrl,
    auth: 'bearer',
    getBearer: async () => getAccessToken(),
});

export const serverSideBookrClient = (context: AppContext) =>
    new BookrClient({
        baseUrl: endpoints.bookrApiUrl,
        auth: 'bearer',
        getBearer: () => context.accessToken ?? '',
    });
