import { Loader } from '@googlemaps/js-api-loader';
import { getClientConfig } from '@bookr-technologies/config';
import { Optional } from '@bookr-technologies/types';

class PlacesSearchError extends Error {
    constructor(public status: string) {
        super('Unable to search for places');
    }
}

type NormalizedCoords =
    | {
          lat: number;
          lng: number;
      }
    | {
          latitude: number;
          longitude: number;
      };

class GoogleMapsClient {
    config = getClientConfig();

    loader = new Loader({
        apiKey: this.config.googleMaps.apiKey,
        libraries: ['places'],
    });

    get places() {
        return this.loader.importLibrary('places');
    }

    async getPlacesByAddress(query: string) {
        const places = await this.places;
        const service = new places.PlacesService(document.createElement('div'));

        return new Promise<google.maps.places.PlaceResult[]>(
            (resolve, reject) => {
                service.textSearch({ query }, (results, status) => {
                    switch (status) {
                        case places.PlacesServiceStatus.NOT_FOUND:
                        case places.PlacesServiceStatus.ZERO_RESULTS:
                            resolve([]);
                            break;

                        case places.PlacesServiceStatus.OK:
                            resolve(results ?? []);
                            break;

                        default:
                            reject(new PlacesSearchError(status));
                            break;
                    }
                });
            },
        );
    }

    async getPlacesByCoordinates(coords: NormalizedCoords) {
        const places = await this.places;
        const service = new places.PlacesService(document.createElement('div'));

        return new Promise<google.maps.places.PlaceResult[]>(
            (resolve, reject) => {
                service.findPlaceFromQuery(
                    {
                        query: 'street',
                        fields: ['formatted_address', 'geometry'],
                        locationBias: this.latLng(coords),
                    },
                    (results, status) => {
                        switch (status) {
                            case places.PlacesServiceStatus.NOT_FOUND:
                            case places.PlacesServiceStatus.ZERO_RESULTS:
                                resolve([]);
                                break;

                            case places.PlacesServiceStatus.OK:
                                resolve(results ?? []);
                                break;

                            default:
                                reject(new PlacesSearchError(status));
                                break;
                        }
                    },
                );
            },
        );
    }

    private latLng(coords: NormalizedCoords) {
        return {
            lat: 'lat' in coords ? coords.lat : coords.latitude,
            lng: 'lng' in coords ? coords.lng : coords.longitude,
        };
    }

    geometryToLatLng(geometry: Optional<google.maps.places.PlaceGeometry>) {
        const coords = JSON.parse(
            JSON.stringify(geometry?.location ?? { lat: 0, lng: 0 }),
        );

        return this.latLng(coords);
    }
}

export const googleMapsClient = new GoogleMapsClient();
