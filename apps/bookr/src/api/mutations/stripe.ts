import { useMutation } from '@tanstack/react-query';
import { getRuntimeConfig } from '@bookr-technologies/config';
import { BookrClient, StripeForms } from '@bookr-technologies/sdk';
import { bookrClient, stripeKeys } from '~/api';
import { getLocation } from '~/lib/utilities/window';

export const stripeQueries = {
    async createCheckoutSession(
        client: BookrClient,
        input: StripeForms.CreateCheckoutSessionData,
    ) {
        const runtimeConfig = getRuntimeConfig();
        const pathname = input.pathname || getLocation('pathname', '/');
        const origin =
            input.origin || getLocation('origin', runtimeConfig.urls.web);

        if (input.successUrl === 'auto') {
            input.successUrl = new URL(
                '/settings/application/subscription?action=success',
                origin,
            ).toString();
        }

        if (input.cancelUrl === 'auto') {
            input.cancelUrl = new URL(
                `${pathname}?action=cancel`,
                origin,
            ).toString();
        }

        const { data } = await client.stripe.createCheckoutSession(input);

        return data;
    },
};

export function useCreateCheckoutSessionMutation() {
    return useMutation({
        mutationKey: stripeKeys.createCheckoutSession(),
        mutationFn: (input: StripeForms.CreateCheckoutSessionData) =>
            stripeQueries.createCheckoutSession(bookrClient, input),
    });
}
