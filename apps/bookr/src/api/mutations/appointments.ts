import { useMutation } from '@tanstack/react-query';
import {
    AppointmentsForms,
    AppointmentStatusEnum,
} from '@bookr-technologies/sdk';
import { bookrClient } from '~/api';
import {
    createAppointmentSideEffect,
    updateAppointmentSideEffect,
} from '../sideEffects';

const appointmentMutations = {
    async createAppointment(input: AppointmentsForms.CreateAppointmentData) {
        const { data } =
            await bookrClient.appointments.createAppointment(input);

        return data;
    },
    async cancelAppointment(appointmentId: number) {
        const { data } = await bookrClient.appointments.cancelAppointment({
            appointmentId,
        });

        return data;
    },
};

export function useCreateAppointmentMutation() {
    return useMutation({
        mutationKey: ['appointments', 'createAppointment'],
        mutationFn: (input: AppointmentsForms.CreateAppointmentData) =>
            appointmentMutations.createAppointment(input),
        onSuccess: (data) => createAppointmentSideEffect(data),
    });
}

export function useCancelAppointmentMutation() {
    return useMutation({
        mutationKey: ['appointments', 'cancelAppointment'],
        mutationFn: async (appointmentId: number) => {
            const status =
                await appointmentMutations.cancelAppointment(appointmentId);
            if (status) {
                return appointmentId;
            }

            return null;
        },
        onSuccess: (appointmentId) => {
            if (!appointmentId) {
                return;
            }

            updateAppointmentSideEffect(appointmentId, (appointment) => ({
                ...appointment,
                status: AppointmentStatusEnum.CANCELLED,
            }));
        },
    });
}
