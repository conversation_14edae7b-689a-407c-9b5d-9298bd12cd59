import { useMutation } from '@tanstack/react-query';
import { BusinessesForms, MinimalBusinessModel } from '@bookr-technologies/sdk';
import {
    bookrClient,
    businessKeys,
    queryClient,
    transformResponse,
} from '~/api';

export const businessMutations = {
    addFavorite: async (businessId: string) => {
        const results = await bookrClient.users.addFavourite({ businessId });

        return transformResponse(results);
    },
    removeFavorite: async (businessId: string) => {
        const results = await bookrClient.users.deleteFavorite({ businessId });

        return transformResponse(results);
    },
    reportBusiness: async (body: BusinessesForms.BusinessReportData) => {
        const { data } = await bookrClient.businesses.reportContent(body);

        return data;
    },
};

export function useBusinessFavoriteMutation() {
    return useMutation({
        mutationFn: async ({
            businessId,
            action,
        }: {
            businessId: string;
            action: 'add' | 'remove';
        }) => {
            let favorites: MinimalBusinessModel[] = [];

            switch (action) {
                case 'add':
                    favorites = await businessMutations.addFavorite(businessId);
                    break;
                case 'remove':
                    favorites =
                        await businessMutations.removeFavorite(businessId);
            }

            return favorites;
        },
        onSuccess: (data) => {
            data.forEach((business) =>
                queryClient.setQueryData(
                    businessKeys.getBusinessFavorite(business.id),
                    business,
                ),
            );
        },
    });
}

export function useReportBusinessMutation() {
    return useMutation({
        mutationFn: (input: BusinessesForms.BusinessReportData) =>
            businessMutations.reportBusiness(input),
    });
}
