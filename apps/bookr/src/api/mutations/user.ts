import { useMutation } from '@tanstack/react-query';
import { ErrorCode } from '@bookr-technologies/core';
import { UserForms } from '@bookr-technologies/sdk';
import { bookrClient } from '~/api';
import { useSession } from '~/components/SessionProvider';
import { updateCachedUser } from '../sideEffects';

const userMutations = {
    async createUser(body: UserForms.CreateUserData) {
        const { data } = await bookrClient.users.createUser(body);

        return data;
    },
    async updateUser(body: UserForms.UpdateUserData) {
        if (!body.uid) {
            throw new ErrorCode('user/not-found');
        }

        const { data } = await bookrClient.users.updateUser(body);

        return data;
    },
    async uploadUserPhoto(body: UserForms.UploadUserPhotoData) {
        const { data } = await bookrClient.users.uploadUserPhoto(body);

        return data;
    },
    async deleteUser() {
        const { data } = await bookrClient.users.deleteUser();

        return data;
    },
};

export function useCreateUserMutation() {
    return useMutation({
        mutationKey: ['users', 'createUser'],
        mutationFn: (input: UserForms.CreateUserData) =>
            userMutations.createUser(input),
        onSuccess: (data) => updateCachedUser(data.uid, () => data),
    });
}

export function useUpdateUserMutation() {
    const { session } = useSession();

    return useMutation({
        mutationKey: ['users', 'updateUser', session?.uid],
        mutationFn: (input: Omit<UserForms.UpdateUserData, 'uid'>) =>
            userMutations.updateUser({
                uid: session?.uid ?? '',
                ...input,
            }),
        onSuccess: (data) => updateCachedUser(data.uid, () => data),
    });
}

export function useUploadUserPhotoMutation() {
    return useMutation({
        mutationKey: ['users', 'uploadUserPhoto'],
        mutationFn: (input: UserForms.UploadUserPhotoData) =>
            userMutations.uploadUserPhoto(input),
        onSuccess: (data) => updateCachedUser(data.uid, () => data),
    });
}

export function useDeleteUserMutation() {
    return useMutation({
        mutationKey: ['users', 'deleteUser'],
        mutationFn: () => userMutations.deleteUser(),
    });
}
