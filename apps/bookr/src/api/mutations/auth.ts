import { useMutation } from '@tanstack/react-query';
import { MutexLock } from '@bookr-technologies/core';
import { getAccessToken, nextClient } from '~/api';

const postSessionLock = new MutexLock();

export const authMutations = {
    postSession: async (token: string) => {
        if (postSessionLock.isLocked()) {
            return;
        }

        const unlock = postSessionLock.lock();
        await nextClient.auth.postSession(token);

        unlock();
    },
};

export function usePostSessionMutation() {
    const token = getAccessToken();

    return useMutation({
        mutationKey: ['session', token],
        mutationFn: () => authMutations.postSession(token),
    });
}
