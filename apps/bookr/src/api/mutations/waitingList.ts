import { useMutation } from '@tanstack/react-query';
import { WaitingListForms } from '@bookr-technologies/sdk';
import { bookrClient } from '~/api';

const waitingListMutations = {
    async subscribe(input: WaitingListForms.SubscribeParams) {
        const { data } = await bookrClient.waitingList.subscribe(input);

        return data;
    },
};

export function useSubscribeToWaitingList() {
    return useMutation({
        mutationKey: ['appointments', 'waitingList', 'subscribe'],
        mutationFn: (input: WaitingListForms.SubscribeParams) =>
            waitingListMutations.subscribe(input),
    });
}
