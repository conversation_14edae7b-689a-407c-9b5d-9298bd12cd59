import { transformResponse } from './utils';

describe('api#utils', () => {
    it('should transform the request correctly', () => {
        expect(transformResponse({ data: { id: '123' } })).toEqual({
            id: '123',
        });
        expect(
            transformResponse({
                data: [{ id: '123' }],
                pagination: { totals: 1 },
            }),
        ).toEqual({
            data: [{ id: '123' }],
            pagination: { totals: 1 },
        });
    });
});
