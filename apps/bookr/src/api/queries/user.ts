/* eslint-disable @typescript-eslint/no-non-null-assertion */
import { useQuery, UseQueryOptions } from '@tanstack/react-query';
import { isAxiosError } from 'axios';
import { getAuth } from '@bookr-technologies/firebase';
import { BookrClient, UserModel } from '@bookr-technologies/sdk';
import { Optional, SingleOptionalProp } from '@bookr-technologies/types';
import { bookrClient, queryClient } from '~/api';
import { userKeys } from '~/api';
import { AppContext } from '~/api';
import { transformResponse } from '~/api';
import { useSession } from '~/components/SessionProvider';
import { navigateToUserSetup } from '../sideEffects';

export const userQueries = {
    getUser: async (client: BookrClient, uid: string) => {
        const result = await client.users.getById({ uid });

        return transformResponse(result);
    },
    getUserMetadata: async (client: BookrClient, email: string) => {
        const result = await client.users.getMetadata({ email });

        return transformResponse(result);
    },
};

// All user preloaders
export function preloadGetUser(
    context: AppContext,
    { uid }: SingleOptionalProp<'uid', string>,
) {
    return queryClient.prefetchQuery(userKeys.getUser(uid), async () => {
        if (!uid) {
            return null;
        }

        const results = await context.apiClients.bookr.users.getById({ uid });

        return transformResponse(results);
    });
}

export async function preloadGetUserMetadata(
    context: AppContext,
    email: string,
) {
    return queryClient.prefetchQuery(
        userKeys.getUserMetadata(email),
        async () => {
            const results = await context.apiClients.bookr.users.getMetadata({
                email,
            });

            return transformResponse(results);
        },
    );
}

// Queries
export function useUserQuery({
    skipConfiguredCheck,
    ...options
}: Omit<
    UseQueryOptions<UserModel, unknown, UserModel, Optional<string>[]>,
    'queryKey' | 'queryFn'
> & { skipConfiguredCheck?: boolean } = {}) {
    const { session } = useSession();
    const uid = () => session?.uid || getAuth().currentUser?.uid;

    return useQuery({
        ...options,
        networkMode: 'offlineFirst',
        enabled: options.enabled ?? !!uid(),
        queryKey: userKeys.getUser(uid()),
        queryFn: () => userQueries.getUser(bookrClient, uid()!),
        initialData: () => queryClient.getQueryData(userKeys.getUser(uid())),
        onError: (error) => {
            if (
                !skipConfiguredCheck &&
                isAxiosError(error) &&
                error.status === 404
            ) {
                navigateToUserSetup(3);
            }

            options?.onError?.(error);
        },
    });
}

export function useUserMetadataQuery(email: Optional<string>) {
    return useQuery({
        networkMode: 'offlineFirst',
        enabled: !!email,
        queryKey: userKeys.getUserMetadata(email),
        queryFn: () => userQueries.getUserMetadata(bookrClient, email!),
        initialData: () =>
            queryClient.getQueryData(userKeys.getUserMetadata(email)),
    });
}
