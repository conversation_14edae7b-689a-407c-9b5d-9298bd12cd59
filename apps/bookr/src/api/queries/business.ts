import {
    InfiniteData,
    useInfiniteQuery,
    UseInfiniteQueryOptions,
    useQuery,
} from '@tanstack/react-query';
import { isAxiosError } from 'axios';
import {
    AlgoliaBusinessModel,
    AppointmentReviewModel,
    BookrClient,
    PaginatedParams,
    PaginatedResponse,
} from '@bookr-technologies/sdk';
import {
    Optional,
    SingleOptionalProp,
    SingleProp,
} from '@bookr-technologies/types';
import {
    AppContext,
    bookrClient,
    businessKeys,
    queryClient,
    transformResponse,
} from '~/api';
import { useSession } from '~/components/SessionProvider';
import { GetBusinessSearchInput } from '../types';

const businessQueries = {
    getBusiness: async (id: string) => {
        const results = await bookrClient.businesses.getById({ id });

        return transformResponse(results);
    },
    getBusinessFavorite: async (businessId: string) => {
        try {
            const results = await bookrClient.users.getFavourite({
                businessId,
            });

            return transformResponse(results);
        } catch (error) {
            if (!isAxiosError(error) || error.status !== 404) {
                throw error;
            }

            return null;
        }
    },
    getBusinessReviews: async (
        businessId: string,
        filterRating?: number,
        pageable: PaginatedParams = {},
    ) => {
        const results = await bookrClient.reviews.getReviews({
            businessId,
            filterRating,
            ...pageable,
        });

        return transformResponse(results);
    },
    getBusinessReviewsChart: async (businessId: string) => {
        const results = await bookrClient.reviews.getReviewsChart({
            businessId,
        });

        return transformResponse(results);
    },
    getBusinessSearch: async (input: GetBusinessSearchInput) => {
        const results = await bookrClient.businesses.search(input);

        return transformResponse(results);
    },
    async getBestBusinesses(
        client: BookrClient,
        params: SingleOptionalProp<'radius' | 'latLng', string> = {},
    ) {
        const results = await client.businesses.getBestBusinesses(params);
        return transformResponse(results);
    },
    async getNearByBusinesses(
        client: BookrClient,
        params: SingleOptionalProp<'radius' | 'latLng', string> = {},
    ) {
        const results = await client.businesses.getNearByBusinesses(params);
        return transformResponse(results);
    },
    async getRecentBusinesses(
        client: BookrClient,
        pageable: PaginatedParams = {},
    ) {
        const results = await client.businesses.getRecentBusinesses(pageable);
        return transformResponse(results);
    },
    async getBookedBusinesses(client: BookrClient) {
        const results = await client.businesses.getBookedBusinesses();
        return transformResponse(results);
    },
};

// All user preloaders
export async function preloadGetBusiness(
    context: AppContext,
    { businessId }: SingleProp<'businessId'>,
) {
    const result = await context.apiClients.bookr.businesses.getById({
        id: businessId,
    });
    const data = transformResponse(result);

    await Promise.all(
        [
            queryClient.prefetchQuery(
                businessKeys.getBusiness(businessId),
                async () => data,
            ),
            data.id !== businessId &&
                queryClient.prefetchQuery(
                    businessKeys.getBusiness(data.id),
                    async () => data,
                ),
        ].filter(Boolean),
    );
}

export async function preloadGetBusinessReviews(
    context: AppContext,
    {
        businessId,
        filterRating,
        pageable = {},
    }: SingleProp<'businessId'> &
        SingleOptionalProp<'pageable'> &
        SingleOptionalProp<'filterRating'>,
) {
    await queryClient.prefetchQuery(
        businessKeys.getBusinessReviews(businessId, filterRating, pageable),
        async () => {
            const results = await context.apiClients.bookr.reviews.getReviews({
                businessId,
                filterRating,
                ...pageable,
            });

            return {
                pages: [transformResponse(results)],
                pageParams: [],
            } as InfiniteData<PaginatedResponse<AppointmentReviewModel>>;
        },
    );
}

export async function preloadGetBusinessFavorite(
    context: AppContext,
    { businessId }: SingleProp<'businessId'>,
) {
    await queryClient.prefetchQuery(
        businessKeys.getBusinessFavorite(businessId),
        async () => {
            try {
                const results =
                    await context.apiClients.bookr.users.getFavourite({
                        businessId,
                    });

                return transformResponse(results);
            } catch (error) {
                if (!isAxiosError(error) || error.status !== 404) {
                    throw error;
                }

                return null;
            }
        },
    );
}

export async function preloadGetBusinessReviewChart(
    context: AppContext,
    { businessId }: SingleProp<'businessId'>,
) {
    await queryClient.prefetchQuery(
        businessKeys.getBusinessReviewsChart(businessId),
        async () => {
            const results =
                await context.apiClients.bookr.reviews.getReviewsChart({
                    businessId,
                });

            return transformResponse(results);
        },
    );
}

export async function preloadGetBusinessSearch(
    context: AppContext,
    input: GetBusinessSearchInput,
) {
    await queryClient.prefetchQuery(
        businessKeys.getBusinessSearch(input),
        async () => {
            const { data } =
                await context.apiClients.bookr.businesses.search(input);

            return {
                pages: [data],
                pageParams: [],
            } as InfiniteData<AlgoliaBusinessModel>;
        },
    );
}

export async function preloadGetBestBusinesses(
    context: AppContext,
    params: SingleOptionalProp<'radius' | 'latLng', string> = {},
) {
    await queryClient.prefetchQuery(
        businessKeys.getBestBusinesses(params),
        () =>
            businessQueries.getBestBusinesses(context.apiClients.bookr, params),
    );
}

export async function preloadGetNearbyBusinesses(
    context: AppContext,
    params: SingleOptionalProp<'radius' | 'latLng', string> = {},
) {
    await queryClient.prefetchQuery(
        businessKeys.getNearByBusinesses(params),
        () =>
            businessQueries.getNearByBusinesses(
                context.apiClients.bookr,
                params,
            ),
    );
}

export async function preloadGetRecentBusinesses(
    context: AppContext,
    pageable: PaginatedParams = {},
) {
    await queryClient.prefetchQuery(
        businessKeys.getRecentBusinesses(pageable),
        () =>
            businessQueries.getRecentBusinesses(
                context.apiClients.bookr,
                pageable,
            ),
    );
}

export async function preloadGetBookedBusinesses(context: AppContext) {
    await queryClient.prefetchQuery(businessKeys.getBookedBusinesses(), () =>
        businessQueries.getBookedBusinesses(context.apiClients.bookr),
    );
}

// Queries
export function useBusinessQuery(id: Optional<string>) {
    return useQuery({
        enabled: !!id,
        queryKey: businessKeys.getBusiness(id),
        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
        queryFn: () => businessQueries.getBusiness(id!),
        onSuccess: (data) =>
            queryClient.setQueryData(businessKeys.getBusiness(data.id), data),
        // initialData: () =>
        //     queryClient.getQueryData(businessKeys.getBusiness(id)),
    });
}

export function useBusinessReviewsQuery(
    id: Optional<string>,
    {
        filterRating,
        pageable,
    }: {
        filterRating?: number;
        pageable?: PaginatedParams;
    },
) {
    return useInfiniteQuery({
        enabled: !!id,
        keepPreviousData: true,
        queryKey: businessKeys.getBusinessReviews(id, filterRating, pageable),
        queryFn: ({ pageParam }) =>
            // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
            businessQueries.getBusinessReviews(id!, filterRating, {
                ...pageable,
                page: pageParam || pageable?.page || 0,
            }),
        // initialData: () =>
        //     queryClient.getQueryData<
        //         InfiniteData<PaginatedResponse<AppointmentReviewModel>>
        //     >(businessKeys.getBusinessReviews(id)),
        getNextPageParam: (page: PaginatedResponse) => {
            const newPage = page.number + 1;
            if (!page.last) {
                return newPage;
            }
        },
        getPreviousPageParam: (page: PaginatedResponse) => {
            const newPage = page.number - 1;
            if (!page.first) {
                return newPage;
            }
        },
    });
}

export function useBusinessFavoriteQuery(id: Optional<string>) {
    const { session } = useSession();

    return useQuery({
        enabled: !!(id && session),
        queryKey: businessKeys.getBusinessFavorite(id),
        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
        queryFn: () => businessQueries.getBusinessFavorite(id!),
        // initialData: () =>
        //     queryClient.getQueryData(businessKeys.getBusinessFavorite(id)),
    });
}

export function useBusinessReviewsChartQuery(id: Optional<string>) {
    return useQuery({
        enabled: !!id,
        queryKey: businessKeys.getBusinessReviewsChart(id),
        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
        queryFn: () => businessQueries.getBusinessReviewsChart(id!),
        // initialData: () =>
        //     queryClient.getQueryData(businessKeys.getBusinessReviewsChart(id)),
    });
}

export function useBusinessSearchQuery(
    input: GetBusinessSearchInput = {},
    options: Omit<
        UseInfiniteQueryOptions<
            AlgoliaBusinessModel,
            unknown,
            AlgoliaBusinessModel,
            AlgoliaBusinessModel,
            string[]
        >,
        | 'queryKey'
        | 'queryFn'
        | 'getNextPageParam'
        | 'getPreviousPageParam'
        | 'keepPreviousData'
    > = {},
) {
    return useInfiniteQuery({
        ...options,
        keepPreviousData: true,
        queryKey: businessKeys.getBusinessSearch(input),
        queryFn: ({ pageParam }) =>
            businessQueries.getBusinessSearch({
                ...input,
                page: String(pageParam ?? input?.page ?? 0),
            }),
        // initialData: () =>
        //     queryClient.getQueryData<InfiniteData<AlgoliaBusinessModel>>(
        //         businessKeys.getBusinessSearch(input),
        //     ),
        getNextPageParam: (page: AlgoliaBusinessModel) => {
            const newPage = page.page + 1;
            if (newPage < page.nbPages) {
                return newPage;
            }
        },
        getPreviousPageParam: (page: AlgoliaBusinessModel) => {
            const newPage = page.page - 1;
            if (newPage >= 0) {
                return newPage;
            }
        },
    });
}

export function useGetBestBusinessesQuery(
    params: SingleOptionalProp<'radius' | 'latLng', string> = {},
) {
    return useQuery({
        queryKey: businessKeys.getBestBusinesses(params),
        queryFn: () => businessQueries.getBestBusinesses(bookrClient, params),
        staleTime: 1000 * 60 * 60,
        // initialData: () =>
        //     queryClient.getQueryData(businessKeys.getBestBusinesses(params)),
    });
}

export function useGetNearByBusinessesQuery(
    params: SingleOptionalProp<'radius' | 'latLng', string> = {},
) {
    return useQuery({
        queryKey: businessKeys.getNearByBusinesses(params),
        queryFn: () => businessQueries.getNearByBusinesses(bookrClient, params),
        // initialData: () =>
        //     queryClient.getQueryData(businessKeys.getNearByBusinesses(params)),
    });
}

export function useGetRecentBusinessesQuery(pageable: PaginatedParams = {}) {
    return useQuery({
        queryKey: businessKeys.getRecentBusinesses(pageable),
        queryFn: () =>
            businessQueries.getRecentBusinesses(bookrClient, pageable),
        // initialData: () =>
        //     queryClient.getQueryData(
        //         businessKeys.getRecentBusinesses(pageable),
        //     ),
    });
}

export function useGetBookedBusinessesQuery() {
    const { session } = useSession();

    return useQuery({
        enabled: !!session,
        queryKey: businessKeys.getBookedBusinesses(),
        queryFn: () => businessQueries.getBookedBusinesses(bookrClient),
        // initialData: () =>
        //     queryClient.getQueryData(businessKeys.getBookedBusinesses()),
    });
}
