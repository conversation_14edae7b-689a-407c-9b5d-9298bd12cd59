/* eslint-disable @typescript-eslint/no-non-null-assertion */
import { useQuery, UseQueryOptions } from '@tanstack/react-query';
import { BookrClient, CategoryModel } from '@bookr-technologies/sdk';
import { AppContext, bookrClient, categoriesKeys, queryClient } from '~/api';

export const categoriesQueries = {
    async getCategories(client: BookrClient) {
        const { data } = await client.categories.getCategories();

        return data;
    },
};

// All category preloaders
export function preloadGetCategories(context: AppContext) {
    return queryClient.prefetchQuery(categoriesKeys.getCategories(), async () =>
        categoriesQueries.getCategories(context.apiClients.bookr),
    );
}

// Queries
export function useGetCategoriesQuery({
    ...options
}: Omit<
    UseQueryOptions<CategoryModel[], unknown, CategoryModel[], string[]>,
    'queryKey' | 'queryFn'
> = {}) {
    return useQuery({
        ...options,
        networkMode: 'offlineFirst',
        queryKey: categoriesKeys.getCategories(),
        queryFn: () => categoriesQueries.getCategories(bookrClient),
        initialData: () =>
            queryClient.getQueryData(categoriesKeys.getCategories()),
    });
}

export function useGetCategoriesSuspenseQuery({
    ...options
}: Omit<
    UseQueryOptions<CategoryModel[], unknown, CategoryModel[], string[]>,
    'queryKey' | 'queryFn'
> = {}) {
    return useQuery({
        ...options,
        suspense: true,
        queryKey: categoriesKeys.getCategories(),
        queryFn: () => categoriesQueries.getCategories(bookrClient),
    });
}
