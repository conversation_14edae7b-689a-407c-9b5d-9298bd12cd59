import { useQuery } from '@tanstack/react-query';
import { BookrClient, ServiceForms } from '@bookr-technologies/sdk';
import { bookrClient, serviceKeys } from '~/api';

export const serviceQueries = {
    async getService(client: BookrClient, form: ServiceForms.GetServiceParams) {
        const { data } = await client.services.getService(form);

        return data;
    },
};

export function useGetServiceQuery(form: ServiceForms.GetServiceParams) {
    return useQuery({
        enabled: !!form.serviceId,
        queryKey: serviceKeys.getService(form),
        queryFn: () => serviceQueries.getService(bookrClient, form),
    });
}
