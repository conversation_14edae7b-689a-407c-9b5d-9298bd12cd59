import { useQuery } from '@tanstack/react-query';
import { googleMapsClient } from '~/api';

export const googleMapsQueries = {
    getPlacesByAddress(query: string) {
        if (!query) {
            return Promise.resolve([]);
        }

        return googleMapsClient.getPlacesByAddress(query);
    },
};

export function useGetPlacesByAddressQuery(query: string) {
    return useQuery({
        enabled: false,
        queryKey: ['googleMaps', 'getPlacesByAddress', query],
        queryFn: () => googleMapsQueries.getPlacesByAddress(query),
    });
}
