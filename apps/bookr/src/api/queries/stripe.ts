import { useQuery } from '@tanstack/react-query';
import { BookrClient } from '@bookr-technologies/sdk';
import { bookrClient, stripeKeys } from '~/api';

export const stripeQueries = {
    async getSubscriptionPlans(client: BookrClient, withCustom: boolean) {
        const { data } = await client.stripe.getSubscriptionPlans(withCustom);

        return data;
    },
};

export function useGetSubscriptionPlansQuery(withCustom: boolean) {
    return useQuery({
        queryKey: stripeKeys.getSubscriptionPlans(withCustom),
        queryFn: () =>
            stripeQueries.getSubscriptionPlans(bookrClient, withCustom),
    });
}
