import {
    InfiniteData,
    useInfiniteQuery,
    UseInfiniteQueryOptions,
    useQuery,
    UseQueryOptions,
} from '@tanstack/react-query';
import format from 'date-fns/format';
import { Duration } from '@bookr-technologies/core';
import {
    AppointmentModel,
    AppointmentsForms,
    BookrClient,
    PaginatedParams,
    PaginatedResponse,
} from '@bookr-technologies/sdk';
import {
    appointmentKeys,
    bookrClient,
    collectPages,
    queryClient,
    transformResponse,
} from '~/api';
import {
    AppointmentGetDaysThatCanBeBookedInput,
    AppointmentGetTimeslotsInput,
} from '../types';

const appointmentQueries = {
    async getAppointmentGetTimeslots(
        client: BookrClient,
        input: AppointmentGetTimeslotsInput,
    ) {
        const { staffId, serviceId, date: inputDate } = input;
        const date = format(inputDate, 'yyyy-MM-dd');
        const results = await client.appointments.getTimeslots({
            staffId,
            serviceId,
            date,
        });

        return transformResponse(results);
    },
    async getDaysThatCanBeBooked(
        client: BookrClient,
        input: AppointmentGetDaysThatCanBeBookedInput,
    ) {
        const { staffId, serviceId } = input;
        const { data } = await client.appointments.getDaysThatCanBeBooked({
            staffId,
            serviceId,
        });

        return data;
    },
    async getAppointmentsByUser(
        client: BookrClient,
        params: AppointmentsForms.GetAppointmentsByUserIdParams = {},
    ) {
        const results =
            await client.appointments.getAppointmentsByUserId(params);

        return transformResponse(results);
    },
    async getAppointment(client: BookrClient, appointmentId: number) {
        const results = await client.appointments.getAppointment({
            appointmentId,
        });

        return transformResponse(results);
    },
};

// Queries
export function useAppointmentGetTimeslotsQuery(
    args: AppointmentGetTimeslotsInput,
) {
    return useQuery({
        queryKey: appointmentKeys.getAppointmentGetTimeslots(args),
        queryFn: () =>
            appointmentQueries.getAppointmentGetTimeslots(bookrClient, args),
    });
}

export function useAppointmentDaysThatCanBeBookedQuery(
    args: AppointmentGetDaysThatCanBeBookedInput,
) {
    return useQuery({
        queryKey: appointmentKeys.getAppointmentGetDaysThatCanBeBooked(args),
        queryFn: () =>
            appointmentQueries.getDaysThatCanBeBooked(bookrClient, args),
    });
}

export function useAppointmentsByUserQuery(
    pageable?: PaginatedParams,
    options: Omit<
        UseInfiniteQueryOptions<
            PaginatedResponse<AppointmentModel>,
            unknown,
            PaginatedResponse<AppointmentModel>,
            PaginatedResponse<AppointmentModel>,
            string[]
        >,
        'queryKey' | 'queryFn' | 'getNextPageParam' | 'getPreviousPageParam'
    > = {},
) {
    return useInfiniteQuery({
        ...options,
        networkMode: 'offlineFirst',
        keepPreviousData: true,
        queryKey: appointmentKeys.getAppointmentsByUser(pageable),
        queryFn: ({ pageParam }) =>
            // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
            appointmentQueries.getAppointmentsByUser(bookrClient, {
                ...pageable,
                page: pageParam || pageable?.page || 0,
            }),
        initialData: () =>
            queryClient.getQueryData<
                InfiniteData<PaginatedResponse<AppointmentModel>>
            >(appointmentKeys.getAppointmentsByUser(pageable)),
        getNextPageParam: (page: PaginatedResponse) => {
            const newPage = page.number + 1;
            if (!page.last) {
                return newPage;
            }
        },
        getPreviousPageParam: (page: PaginatedResponse) => {
            const newPage = page.number - 1;
            if (!page.first) {
                return newPage;
            }
        },
        onSuccess: (data) => {
            const { content } = collectPages(data);

            content.forEach((appointment) =>
                queryClient.setQueryData(
                    appointmentKeys.getAppointment(appointment.id),
                    appointment,
                ),
            );

            options.onSuccess?.(data);
        },
    });
}

export function useAppointmentQuery(
    appointmentId: number,
    options: Omit<
        UseQueryOptions<AppointmentModel, unknown, AppointmentModel, string[]>,
        'queryKey' | 'queryFn'
    > = {},
) {
    return useQuery({
        ...options,
        networkMode: 'offlineFirst',
        enabled: options.enabled ?? !!appointmentId,
        queryKey: appointmentKeys.getAppointment(appointmentId),
        staleTime: Duration.seconds(30),
        queryFn: () =>
            appointmentQueries.getAppointment(bookrClient, appointmentId),
        initialData: () =>
            queryClient.getQueryData(
                appointmentKeys.getAppointment(appointmentId),
            ),
    });
}
