import { str } from '@bookr-technologies/core';
import { PaginatedParams } from '@bookr-technologies/sdk';
import { pageableToQuery } from '~/api';
import {
    AppointmentGetDaysThatCanBeBookedInput,
    AppointmentGetTimeslotsInput,
} from '../types';

export const appointmentKeys = {
    getAppointmentGetTimeslots({
        staffId,
        serviceId,
        date,
    }: AppointmentGetTimeslotsInput) {
        return [
            'appointments',
            'getTimeslots',
            `staffId=${staffId}`,
            `serviceId=${serviceId}`,
            `date=${date}`,
        ];
    },
    getAppointmentGetDaysThatCanBeBooked({
        staffId,
        serviceId,
    }: AppointmentGetDaysThatCanBeBookedInput) {
        return [
            'appointments',
            'getDaysThatCanBeBooked',
            `staffId=${staffId}`,
            `serviceId=${serviceId}`,
        ];
    },
    getAppointmentsByUser(pageable?: PaginatedParams) {
        return ['appointments', ...pageableToQuery(pageable)];
    },
    getAppointment(id: number) {
        return ['appointments', str(id)];
    },
};
