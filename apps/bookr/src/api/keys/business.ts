import { PaginatedParams } from '@bookr-technologies/sdk';
import { Optional, SingleOptionalProp } from '@bookr-technologies/types';
import { pageableToQuery } from '~/api';
import { GetBusinessSearchInput } from '../types';

export const businessKeys = {
    getBusiness: (id: Optional<string>) => ['businesses', id],
    getBusinessFavorite: (id: Optional<string>) => [
        'businesses',
        id,
        'favorite',
    ],
    getBusinessReviews: (
        id: Optional<string>,
        filterRating?: Optional<number>,
        pageable?: PaginatedParams,
    ) => [
        'businesses',
        id,
        'reviews',
        `filterRating=${filterRating}`,
        ...pageableToQuery(pageable),
    ],
    getBusinessReviewsChart: (id: Optional<string>) => [
        'businesses',
        id,
        'reviews',
        'chart',
    ],
    getBusinessSearch: ({
        text,
        category,
        latLng,
        radius,
        page,
        size,
        maxPrice,
        sort,
        minPrice,
        instantBooking,
    }: GetBusinessSearchInput = {}) => [
        'businesses',
        'search',
        `text=${text}`,
        `category=${category}`,
        `latLng=${latLng}`,
        `radius=${radius}`,
        `page=${page}`,
        `size=${size}`,
        `maxPrice=${maxPrice}`,
        `sort=${sort}`,
        `minPrice=${minPrice}`,
        `instantBooking=${instantBooking}`,
    ],
    getBestBusinesses(params: SingleOptionalProp<'radius' | 'latLng', string>) {
        return [
            'businesses',
            'best',
            `radius=${params.radius ?? ''}`,
            `latLng=${params.latLng ?? ''}`,
        ];
    },
    getRecentBusinesses(pageable: PaginatedParams) {
        return ['businesses', 'recent', ...pageableToQuery(pageable)];
    },
    getNearByBusinesses(
        params: SingleOptionalProp<'radius' | 'latLng', string>,
    ) {
        return [
            'businesses',
            'nearby',
            `radius=${params.radius ?? ''}`,
            `latLng=${params.latLng ?? ''}`,
        ];
    },
    getBookedBusinesses() {
        return ['businesses', 'booked'];
    },
};
