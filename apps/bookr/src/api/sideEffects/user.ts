import { value } from '@bookr-technologies/core';
import { UserModel } from '@bookr-technologies/sdk';
import { Updater } from '@bookr-technologies/types';
import { queryClient, userKeys } from '~/api';

export function getCachedUser(id: string) {
    return queryClient.getQueryData<UserModel>(userKeys.getUser(id));
}

export function updateCachedUser(id: string, updater: Updater<UserModel>) {
    const user = getCachedUser(id);

    if (!user) {
        return;
    }

    return queryClient.setQueryData(userKeys.getUser(id), value(updater, user));
}

export function navigateToUserSetup(step: number) {
    if (window.location.pathname !== '/authenticate') {
        window.location.href =
            `/authenticate?step=${step}&redirect=` +
            encodeURIComponent(window.location.pathname);
    }
}
