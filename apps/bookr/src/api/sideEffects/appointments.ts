import { str, value } from '@bookr-technologies/core';
import {
    AppointmentModel,
    CreatedAppointmentModel,
    PaginatedResponse,
} from '@bookr-technologies/sdk';
import { Updater } from '@bookr-technologies/types';
import { appointmentKeys, queryClient } from '~/api';

export function getCachedAppointment(id: number) {
    return queryClient.getQueryData<AppointmentModel>(
        appointmentKeys.getAppointment(id),
    );
}

export function createAppointmentSideEffect({
    success,
    appointment,
}: CreatedAppointmentModel) {
    if (!success || !appointment) {
        return;
    }
    queryClient.setQueryData(
        appointmentKeys.getAppointment(appointment.id),
        appointment,
    );
}

export function updateAppointmentSideEffect(
    id: number,
    updater: Updater<AppointmentModel>,
) {
    const appointment = getCachedAppointment(id);

    if (!appointment) {
        return;
    }

    const appointmentQueries = queryClient.getQueryCache().findAll({
        predicate: (query) =>
            query.queryKey[0] === 'appointments' &&
            str(query.queryKey[1])?.startsWith('page='),
    });

    appointmentQueries.forEach((query) => {
        const data = query.state.data as {
            pages: PaginatedResponse<AppointmentModel>[];
        };

        data.pages = data.pages.map((page) => {
            page.content = page.content.map((appointment) =>
                appointment.id === id
                    ? value(updater, appointment)
                    : appointment,
            );

            return page;
        });

        return query.setData(value(updater, data));
    });

    return queryClient.setQueryData(
        appointmentKeys.getAppointment(appointment.id),
        value(updater, appointment),
    );
}
