import { ReportReasonEnum } from '@bookr-technologies/sdk';

export const BusinessReportReasons = [
    ReportReasonEnum.SEXUAL_CONTENT,
    ReportReasonEnum.VIOLENT_OR_REPULSIVE_CONTENT,
    ReportReasonEnum.HATEFUL_OR_ABUSIVE_CONTENT,
    ReportReasonEnum.HARMFUL_DANGEROUS_ACTS,
    ReportReasonEnum.CHILD_ABUSE,
    ReportReasonEnum.INFRINGES_MY_RIGHTS,
    ReportReasonEnum.PROMOTES_TERRORISM,
    ReportReasonEnum.SPAM_OR_MISLEADING,
];

export interface GetBusinessSearchInput {
    text?: string;
    category?: string;
    latLng?: string;
    radius?: string;
    page?: string;
    size?: string;
    sort?: string;
    instantBooking?: boolean;
    minPrice?: number;
    maxPrice?: number;
}
