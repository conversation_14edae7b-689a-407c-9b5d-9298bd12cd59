import { InfiniteData } from '@tanstack/react-query';
import { PaginatedParams } from '@bookr-technologies/sdk';
import { Optional } from '@bookr-technologies/types';

export function transformResponse<T extends object>(
    data: T,
): T extends { data: infer R } ? R : T extends { data?: infer O } ? O : T {
    if ('data' in data) {
        return data.data as any;
    }

    return data as any;
}

export function collectPages<T extends object>(
    data: Optional<InfiniteData<T>>,
    key: keyof T = 'content' as any,
): T {
    const arr = (val: any) => val ?? [];

    return (data?.pages ?? []).reduce(
        (acc, page) => ({
            ...acc,
            ...page,
            [key]: [...arr(acc[key]), ...arr(page[key])],
        }),
        {} as T,
    );
}

export function pageableToQuery(pageable?: PaginatedParams) {
    return [
        `page=${pageable?.page ?? 0}x${pageable?.size}`,
        `sort=${pageable?.sort?.sort((a, b) => a.localeCompare(b))?.join(',')}`,
    ];
}
