import { SimpleLocales } from '@bookr-technologies/i18n';

const defaultLinks = {
    business: '/en/business',
    pricing: '/en/pricing',
    beauty: '/en/beauty',
    blog: '/en/blog',
    faqs: '/en/faqs',
    about: '/en/about',
    newsletter: '/en/newsletter',
};

export const ExternalLinks = {
    en: defaultLinks,
    ro: {
        business: '/ro/business',
        pricing: '/ro/preturi',
        beauty: '/ro/beauty',
        blog: '/ro/blog',
        faqs: '/ro/faqs',
        about: '/ro/despre',
        newsletter: '/ro/newsletter',
    },
} satisfies Record<SimpleLocales, Record<keyof typeof defaultLinks, string>>;

export function getExternalLink(
    lang: SimpleLocales | string,
    key: keyof typeof defaultLinks,
): string {
    const fallback = defaultLinks[key] ?? '';
    if (lang in ExternalLinks) {
        return String(ExternalLinks[lang as SimpleLocales]?.[key] ?? fallback);
    }

    return fallback;
}
