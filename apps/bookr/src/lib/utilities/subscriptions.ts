import { num, str } from '@bookr-technologies/core';
import {
    PeriodTypeEnum,
    SubscriptionPlanModel,
    SubscriptionPlanTypeEnum,
} from '@bookr-technologies/sdk';
import { Optional } from '@bookr-technologies/types';

export interface ExtendedSubscriptionPlanModel extends SubscriptionPlanModel {
    previousPlan?: Optional<ExtendedSubscriptionPlanModel>;
    planType: SubscriptionPlanTypeEnum;
    planRank: number;
}

const rankings = {
    [PeriodTypeEnum.Monthly]: 100,
    [PeriodTypeEnum.Yearly]: 200,
    [SubscriptionPlanTypeEnum.Free]: 10,
    [SubscriptionPlanTypeEnum.Standard]: 20,
    [SubscriptionPlanTypeEnum.Professional]: 30,
    [SubscriptionPlanTypeEnum.Custom]: 40,
};

function getPlanRank(
    period: PeriodTypeEnum,
    planType: SubscriptionPlanTypeEnum,
) {
    return num(rankings[period]) + num(rankings[planType]);
}

function buildExtendedPlans(
    plans: Record<SubscriptionPlanTypeEnum, SubscriptionPlanModel[]>,
): ExtendedSubscriptionPlanModel[] {
    return Object.entries(plans)
        .reduce((acc, [planType, items]) => {
            acc.push(
                items.map((plan) => ({
                    ...plan,
                    planType: planType as SubscriptionPlanTypeEnum,
                    planRank: getPlanRank(
                        plan.period,
                        planType as SubscriptionPlanTypeEnum,
                    ),
                })),
            );

            return acc;
        }, [] as ExtendedSubscriptionPlanModel[][])
        .flat(1);
}

export function filerSubscriptionPlansByPeriod(period: PeriodTypeEnum) {
    return (plan: ExtendedSubscriptionPlanModel) =>
        str(plan?.period).toUpperCase() === str(period).toUpperCase();
}

export function getSortedPlans(
    plans?: Record<SubscriptionPlanTypeEnum, SubscriptionPlanModel[]>,
) {
    const sortedPlansByPeriod = buildExtendedPlans(plans ?? ({} as any))
        .sort((a, b) => a.planRank - b.planRank)
        .reduce(
            (acc, plan) => {
                acc[plan.period] = acc[plan.period] || [];

                const len = acc[plan.period].length;
                const last = acc[plan.period][len - 1];
                const previousPlan = last ? { ...last } : undefined;

                const linkedPlan = {
                    ...plan,
                    previousPlan,
                };

                acc[plan.period].push(linkedPlan);

                return acc;
            },
            {} as Record<PeriodTypeEnum, ExtendedSubscriptionPlanModel[]>,
        );

    return Object.values(sortedPlansByPeriod).flat(1);
}

export function isSubscriptionPlanRecommended(
    plan: ExtendedSubscriptionPlanModel,
) {
    return plan.planType === SubscriptionPlanTypeEnum.Professional;
}

export function isSubscriptionPlanCustom(plan: ExtendedSubscriptionPlanModel) {
    return plan.planType === SubscriptionPlanTypeEnum.Custom;
}
