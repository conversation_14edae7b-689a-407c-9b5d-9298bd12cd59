import { addMinutes, format, parse, parseISO } from 'date-fns';
import { utcToZonedTime } from 'date-fns-tz';
import {
    getWeekday,
    isWeekdayEqual,
    timeToIndex,
    Weekday,
    Weekdays,
} from '@bookr-technologies/core';
import { WorkingHourModel } from '@bookr-technologies/sdk';
import { Optional } from '@bookr-technologies/types';

export function createWorkingHoursMap(list: WorkingHourModel[]) {
    const collection = list.reduce(
        (acc, item: WorkingHourModel) => {
            const day = item.day?.toLowerCase() as Weekday;

            return {
                ...acc,
                [day]: [...(acc[day] || []), item as WorkingHourModel],
            };
        },
        {} as Record<Weekday, WorkingHourModel[]>,
    );

    (Object.keys(collection) as Weekday[]).forEach((key) => {
        collection[key] = collection[key].sort((a, b) =>
            String(a.start).localeCompare(String(b.start)),
        );
    });

    return collection;
}

export const convertTimeToBusinessTimezone = (
    time: string,
    offset: number,
    timezone: string,
) => {
    const parsedTime = parse(time, 'HH:mm', new Date());
    const utcTime = addMinutes(parsedTime, offset);

    return utcToZonedTime(utcTime, timezone);
};

export const convertHoursToBusinessTimezone = (
    hours: WorkingHourModel[],
    timezone: string,
) =>
    hours.map((hour) => {
        const offset = hour.lastUpdatedAt
            ? parseISO(hour.lastUpdatedAt).getTimezoneOffset()
            : 0;

        const start = convertTimeToBusinessTimezone(
            hour.start,
            offset,
            timezone,
        );

        const end = convertTimeToBusinessTimezone(hour.end, offset, timezone);

        return {
            ...hour,
            start: format(start, 'HH:mm'),
            end: format(end, 'HH:mm'),
        };
    });

export function createWorkingHoursList(
    list: WorkingHourModel[],
    locale?: Locale,
) {
    const map = createWorkingHoursMap(list);
    const days = new Array(7).fill(0).map((_, index) => {
        let day = index + (locale?.options?.weekStartsOn || 0);
        if (day > 6) {
            day = day - 7;
        }

        return Weekdays[day];
    });

    return days.map((day) => ({
        day,
        hours: map[day] || [],
    }));
}

export function getBusinessStatus(
    date: Date,
    workingHours: WorkingHourModel[],
    timezone?: Optional<string>,
) {
    const normalizedDate = convertTimeToBusinessTimezone(
        format(date, 'HH:mm'),
        new Date().getTimezoneOffset(),
        timezone || 'UTC',
    );
    const normalizedHours = convertHoursToBusinessTimezone(
        workingHours,
        timezone || 'UTC',
    );

    const weekday = getWeekday(normalizedDate);
    const intervals = normalizedHours.filter((item) =>
        isWeekdayEqual(item.day, weekday),
    );

    const nowTime = timeToIndex(format(normalizedDate, 'HH:mm'));
    const start = intervals.reduce((acc, item) => {
        const start = timeToIndex(String(item.start));
        return acc === -1 || start < acc ? start : acc;
    }, -1);

    const end = intervals.reduce((acc, item) => {
        const end = timeToIndex(String(item.end));
        return acc === -1 || end > acc ? end : acc;
    }, -1);

    const matchInterval = intervals.find((item) => {
        const start = timeToIndex(String(item.start));
        const end = timeToIndex(String(item.end));
        return nowTime >= start && nowTime <= end;
    });

    const isOutsideHours = nowTime < start || nowTime > end;
    const isClosed =
        intervals.length === 0 || (!matchInterval && isOutsideHours);
    const isOpen = !isClosed && !!matchInterval;
    const isBreak = !matchInterval && !isOutsideHours;

    return { isClosed, isOutsideHours, isOpen, isBreak };
}
