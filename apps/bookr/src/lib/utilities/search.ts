import { castArray, num } from '@bookr-technologies/core';
import { Optional } from '@bookr-technologies/types';
import { GetBusinessSearchInput } from '~/api/types';

export function getSearchArguments(
    args: Optional<string[]>,
    query: Record<string, any> = {},
): GetBusinessSearchInput {
    const map = query;

    for (const arg of castArray(args)) {
        const [key, value] = (arg || '').split(':');
        map[key.trim()] = value.trim();
    }

    function getArgument(...args: string[]) {
        const key = args.find((arg) => arg in map);
        if (!key) {
            return null;
        }

        return map[key];
    }

    return Object.fromEntries(
        Object.entries({
            page: getArgument('p', 'page'),
            size: getArgument('s', 'size'),
            latLng: getArgument('l', 'latLng'),
            radius: getArgument('r', 'radius'),
            text: getArgument('t', 'text'),
            category: getArgument('c', 'category')?.toUpperCase(),
            sort: getArgument('o', 'sort'),
            instantBooking: Boolean(getArgument('i', 'instantBooking')),
            minPrice: num(getArgument('n', 'minPrice')),
            maxPrice: num(getArgument('x', 'maxPrice')),
        }).filter(([, value]) => !!value),
    );
}
