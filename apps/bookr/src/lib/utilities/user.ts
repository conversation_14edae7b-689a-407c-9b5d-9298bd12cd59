import { AccountTypeEnum, UserModel } from '@bookr-technologies/sdk';
import { Optional } from '@bookr-technologies/types';
import { AuthFormSteps } from '~/components/AuthForm';

export function getUserConfigStep(user: Optional<UserModel>) {
    if (!user) {
        return AuthFormSteps.SignUp;
    }

    if (
        user.accountType === AccountTypeEnum.INVALID ||
        (user.accountType === AccountTypeEnum.BUSINESS_OWNER &&
            !user.business?.id)
    ) {
        return AuthFormSteps.Setup;
    }

    return -1;
}
