import { Weekday } from '@bookr-technologies/core';
import {
    convertTimeToBusinessTimezone,
    getBusinessStatus,
} from './workingHours';

describe('bookr#lib/utilities/workingHours', () => {
    describe('convertTimeToBusinessTimezone', () => {
        it('should correctly convert the time for a user in New york and the business in France', () => {
            jest.useFakeTimers({
                now: new Date('2024-01-02T00:00:00-04:00'),
            });

            expect(
                convertTimeToBusinessTimezone('08:00', -240, 'Europe/Paris'),
            ).toEqual(new Date('2024-01-02T14:00:00Z'));

            jest.useRealTimers();
        });
    });

    describe('getBusinessStatus', () => {
        it('should correctly return closed status of the business', () => {
            expect(
                getBusinessStatus(
                    new Date('2024-01-01T08:00:00Z'),
                    [
                        {
                            id: 1,
                            day: Weekday.Monday,
                            start: '13:00',
                            end: '15:00',
                            lastUpdatedAt: '2024-01-01T00:00:00Z',
                        },
                    ],
                    'Europe/London',
                ),
            ).toEqual({
                isBreak: false,
                isClosed: true,
                isOpen: false,
                isOutsideHours: true,
            });
        });

        it('should correctly return break status of the business', () => {
            expect(
                getBusinessStatus(
                    new Date('2024-01-01T12:30:00Z'),
                    [
                        {
                            id: 1,
                            day: Weekday.Monday,
                            start: '08:00',
                            end: '12:00',
                            lastUpdatedAt: '2024-01-01T12:30:00Z',
                        },
                        {
                            id: 2,
                            day: Weekday.Monday,
                            start: '13:00',
                            end: '18:00',
                            lastUpdatedAt: '2024-01-01T12:30:00Z',
                        },
                    ],
                    'Europe/London',
                ),
            ).toEqual({
                isBreak: true,
                isClosed: false,
                isOpen: false,
                isOutsideHours: false,
            });
        });

        it('should correctly return open status of the business', () => {
            expect(
                getBusinessStatus(
                    new Date('2024-01-01T14:00:00Z'),
                    [
                        {
                            id: 1,
                            day: Weekday.Monday,
                            start: '10:00',
                            end: '18:00',
                            lastUpdatedAt: '2024-01-01T14:00:00Z',
                        },
                    ],
                    'Europe/London',
                ),
            ).toEqual({
                isBreak: false,
                isClosed: false,
                isOpen: true,
                isOutsideHours: false,
            });
        });

        it('should correctly return the status of the business when timezones differ', () => {
            expect(
                getBusinessStatus(
                    new Date('2024-01-01T14:00:00Z'),
                    [
                        {
                            id: 1,
                            day: Weekday.Monday,
                            start: '10:00',
                            end: '18:00',
                            lastUpdatedAt: '2024-01-01T14:00:00Z',
                        },
                    ],
                    'America/New_York',
                ),
            ).toEqual({
                isBreak: false,
                isClosed: true,
                isOpen: false,
                isOutsideHours: true,
            });
        });
    });
});
