import { Optional } from '@bookr-technologies/types';

export function getWindow() {
    if (typeof window !== 'undefined') {
        return window;
    }

    return {} as any;
}

export function getLocation<K extends keyof Location, V = Location[K]>(
    key: K,
): Optional<V>;

export function getLocation<K extends keyof Location, V = Location[K]>(
    key: K,
    defaultValue?: V,
): V;

export function getLocation<K extends keyof Location, V = Location[K]>(
    key: K,
    defaultValue?: V,
) {
    const window = getWindow();

    if (window?.location?.[key]) {
        return window.location[key];
    }

    return defaultValue;
}
