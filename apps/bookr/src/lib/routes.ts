import { num, str } from '@bookr-technologies/core';
import { CategoryModel } from '@bookr-technologies/sdk';
import { Optional } from '@bookr-technologies/types';

type ID = string | number;

interface SearchParams {
    category?: Optional<CategoryModel | string>;
    latLng?: Optional<string>;
    location?: Optional<
        | GeolocationCoordinates
        | { lat: string | number; lng: string | number }
        | { latitude: string | number; longitude: string | number }
        | string
    >;
    page?: Optional<number | string>;
    radius?: Optional<number | string>;
    size?: Optional<number | string>;
    text?: Optional<string>;
    sort?: Optional<string>;
    instantBooking?: Optional<boolean>;
    minPrice?: Optional<number | string>;
    maxPrice?: Optional<number | string>;
}

export const RouteLinks = {
    appointments() {
        return '/appointments';
    },
    appointmentsView: (id: ID) => `/appointments/${id}`,
    googleMapsLocation(formattedAddress: Optional<string>) {
        return `https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(
            formattedAddress ?? '',
        )}`;
    },
    newSubscription(serviceId: number, businessId: string) {
        return `/subscriptions/new/${serviceId}?businessId=${businessId}`;
    },
    search(params: SearchParams) {
        params.latLng =
            params.latLng ||
            (params.location && typeof params.location !== 'string'
                ? `${
                      'latitude' in params.location
                          ? params.location.latitude
                          : params.location.lat
                  },${
                      'longitude' in params.location
                          ? params.location.longitude
                          : params.location.lng
                  }`
                : params.location);

        if (typeof params.category !== 'string' && params.category?.name) {
            params.category = params.category?.name;
        }

        params.category = str(params.category)?.toLowerCase();

        const segments = [
            params.page ? `p:${params.page}` : '',
            params.size ? `s:${params.size}` : '',
            params.latLng ? `l:${params.latLng}` : '',
            params.radius ? `r:${params.radius}` : '',
            params.text ? `t:${params.text}` : '',
            params.category ? `c:${params.category}` : '',
            params.sort ? `o:${params.sort}` : '',
            params.instantBooking ? `i:1` : '',
            num(params.minPrice) > 0 ? `n:${params.minPrice}` : '',
            num(params.maxPrice) > 0 ? `x:${params.maxPrice}` : '',
        ]
            .filter(Boolean)
            .join('/');

        return `/search-v2/${segments}`;
    },
    subscriptionPage(id: string, serviceId: number, businessId: string) {
        return `/subscriptions/${id}/${serviceId}?businessId=${businessId}`;
    },
};
