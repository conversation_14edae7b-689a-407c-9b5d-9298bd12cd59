/* Make clicks pass-through */
#nprogress {
    pointer-events: none;
    position: fixed;
    z-index: 2000;
    top: 0;
    left: 0;
    width: 100%;
}

#nprogress,
#nprogress .bar {
    height: 3px;
}

#nprogress .bar {
    background: #2f80fb;
}

/* Fancy blur effect */
#nprogress .peg {
    display: block;
    position: absolute;
    right: 0;
    width: 100px;
    height: 100%;
    box-shadow:
        0 0 10px #2f80fb,
        0 0 5px #2f80fb;
    opacity: 1;
    transform: rotate(3deg) translate(0px, -4px);
}

/* Remove these to get rid of the spinner */
#nprogress .spinner {
    display: block;
    position: fixed;
    z-index: 1031;
    top: 10px;
    right: 10px;
}

#nprogress .spinner-icon {
    width: 10px;
    height: 10px;
    box-sizing: border-box;

    border: solid 1px transparent;
    border-top-color: #2f80fb;
    border-left-color: #2f80fb;
    border-radius: 50%;

    animation: nprogress-spinner 400ms linear infinite;
}

.nprogress-custom-parent {
    overflow: hidden;
    position: relative;
}

.nprogress-custom-parent #nprogress .spinner,
.nprogress-custom-parent #nprogress .bar {
    position: absolute;
}

@keyframes nprogress-spinner {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}
