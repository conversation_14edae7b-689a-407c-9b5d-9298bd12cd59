import Redis from 'ioredis';
import { getServerConfig } from '@bookr-technologies/config';
import { value, Value } from '@bookr-technologies/core';

let globalRedisClient: Redis;

export function getRedisClient() {
    if (!globalRedisClient) {
        const { redis } = getServerConfig();

        globalRedisClient = new Redis({
            host: redis.host || '127.0.0.1',
            port: redis.port || 6379,
            ...(redis.user ? { username: redis.user } : {}),
            ...(redis.password ? { password: redis.password } : {}),
        });
    }

    return globalRedisClient;
}

export async function getRedisValue<T>(key: string) {
    const redis = getRedisClient();
    const value = await redis.get(key);

    try {
        if (value) {
            return JSON.parse(`[${value}]`)[0] as T;
        }
    } catch (e) {
        return null;
    }

    return undefined;
}

export async function getOrSetRedisValue<T>(
    key: string,
    fn: Value<T>,
    ttl = 60,
) {
    const redis = getRedisClient();
    const data = await getRedisValue<T>(key);

    if (data !== undefined && data !== null) {
        return data;
    }

    const result = await value(fn);
    if (ttl > 0) {
        await redis.setex(key, ttl, JSON.stringify(result));
    } else {
        await redis.set(key, JSON.stringify(result));
    }

    return result as T;
}

export function deleteRedisValue(key: string) {
    const redis = getRedisClient();

    return redis.del(key);
}
