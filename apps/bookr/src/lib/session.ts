import { User } from 'firebase/auth';
import { ErrorCode, PersistentStore, retry } from '@bookr-technologies/core';
import { signOut as firebaseSignOut } from '@bookr-technologies/firebase';
import { AccountTypeEnum, UserModel } from '@bookr-technologies/sdk';
import {
    authMutations,
    bookrClient,
    nextClient,
    queryClient,
    setAccessToken,
    transformResponse,
    userQueries,
} from '~/api';

export class AuthSession {
    public uid!: string;
    public token!: string;
}

interface SetAuthSessionOptions {
    user: User;
    redirect?: string;
}

const waitForSession = () =>
    retry(
        async () => {
            const session = await nextClient.auth.getSession();
            const { uid } = transformResponse(session) ?? {};

            if (!uid) {
                throw new ErrorCode('unauthorized');
            }
        },
        {
            delay: 300,
        },
    );

export async function setAuthSession({
    user,
    redirect,
}: SetAuthSessionOptions) {
    const token = await user.getIdToken();

    setAccessToken(token);
    await authMutations.postSession(token);

    try {
        await waitForSession();
        const data = await userQueries.getUser(bookrClient, user.uid);

        if (redirect && getUserConfigStep(data) === -1) {
            window.location.href = redirect;
        }
    } catch (e) {
        // console.log({ e });
    }
}

function getUserConfigStep(user: UserModel) {
    if (!user) {
        return 3;
    }

    if (
        user.accountType === AccountTypeEnum.INVALID ||
        (user.accountType === AccountTypeEnum.BUSINESS_OWNER &&
            !user.business?.id)
    ) {
        return 4;
    }

    return -1;
}

export async function signOut() {
    await nextClient.auth.deleteSession();
    setAccessToken(null);
    queryClient.clear();
    await PersistentStore.clear();
    await firebaseSignOut();
}
