import { isBoom } from '@hapi/boom';
import { HttpStatusCode } from 'axios';
import { Optional } from '@bookr-technologies/types';
import { AppContext, NextAppContext } from '~/api';
import { GetServerSideProps } from './pipe';

type PreloadFunc<T> = (context: AppContext, arg: T) => void | Promise<void>;

type PreloadOptions<T, K extends string> = {
    argsGetter?: (context: NextAppContext) => T | Promise<T>;
    propName?: K;
    onReject?: (
        context: NextAppContext,
        error: any,
    ) => void | Optional<
        ReturnType<GetServerSideProps> | Awaited<ReturnType<GetServerSideProps>>
    >;
    onResolve?: (
        context: NextAppContext,
        args: T,
    ) => void | Optional<
        ReturnType<GetServerSideProps> | Awaited<ReturnType<GetServerSideProps>>
    >;
};

export function withPreload<T extends object, K extends string>(
    func: PreloadFunc<T>,
    options: PreloadOptions<T, K> = {},
): GetServerSideProps<T> {
    const callback: GetServerSideProps<T> = async (context) => {
        let args: T = undefined as any;

        try {
            if (options.argsGetter) {
                args = await options.argsGetter(context);
            }

            await func(context, args);
            if (options.onResolve) {
                const results = await options.onResolve(context, args);
                if (results) {
                    return results;
                }
            }
        } catch (error) {
            if (isBoom(error, HttpStatusCode.NotFound)) {
                return {
                    notFound: true,
                };
            }

            if (options.onReject) {
                const resultErr = await options.onReject(context, error);

                if (resultErr) {
                    return resultErr;
                }
            }
        }

        return {
            props: options.propName ? args : ({} as any),
        };
    };

    Object.defineProperty(callback, 'name', {
        value: `withPreload(${func.name || 'anonymous'})`,
    });

    return callback;
}
