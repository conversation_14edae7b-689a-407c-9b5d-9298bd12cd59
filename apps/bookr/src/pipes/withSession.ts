import { silentPromise } from '@bookr-technologies/core';
import { decodeCookieSession } from '@bookr-technologies/firebase/admin';
import { BookrClient, UserModel } from '@bookr-technologies/sdk';
import {
    serverSideBookrClient,
    serverSideNextClient,
    userQueries,
} from '~/api';
import { getSession } from '~/http';
import { AuthSession } from '~/lib/session';
import { getUserConfigStep } from '~/lib/utilities/user';
import { GetServerSideProps } from './pipe';

async function fetchUser(client: BookrClient, uid: string) {
    return silentPromise(userQueries.getUser(client, uid));
}

export const withSession: (
    required?: boolean,
) => GetServerSideProps<{ session: AuthSession | null }> = (required = false) =>
    async function withSessionRunner(context) {
        const session = await getSession(context.req, context.res);
        let claims: Awaited<ReturnType<typeof decodeCookieSession>> | null =
            null;

        try {
            claims = await decodeCookieSession(session);
        } catch (e) {
            if (required) {
                return {
                    redirect: {
                        permanent: true,
                        destination:
                            '/authenticate?redirect=' +
                            encodeURIComponent(context.resolvedUrl),
                    },
                };
            }
        }

        if (claims) {
            context.accessToken = claims.token;
            context.user = { uid: claims.uid };
        }

        context.requestId = session.id;
        context.apiClients = {
            bookr: serverSideBookrClient(context),
            next: serverSideNextClient(context),
        };

        if (claims && claims.uid) {
            context.user = await fetchUser(
                context.apiClients.bookr,
                claims.uid,
            );

            const configStep = getUserConfigStep(context.user as UserModel);
            if (
                configStep > -1 &&
                !context.resolvedUrl.startsWith('/authenticate')
            ) {
                return {
                    redirect: {
                        permanent: true,
                        destination:
                            `/authenticate?step=${configStep}&redirect=` +
                            encodeURIComponent(context.resolvedUrl),
                    },
                };
            }
        }

        return {
            props: {
                session: claims
                    ? {
                          uid: claims.uid,
                          token: claims.token,
                      }
                    : null,
            },
        };
    };
