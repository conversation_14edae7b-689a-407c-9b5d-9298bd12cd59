import { isAxiosError } from 'axios';
import { GetServerSidePropsResult } from 'next';
import { captureError } from '@bookr-technologies/analytics';
import { parallel } from '@bookr-technologies/core';
import { BusinessModel } from '@bookr-technologies/sdk';
import {
    businessKeys,
    getParam,
    NextAppContext,
    preloadGetBusiness,
    preloadGetBusinessFavorite,
    preloadGetBusinessReviews,
    queryClient,
} from '~/api';
import { SeoMeta } from '~/components/SeoHead';

export function withBusinessPage() {
    return async function withBusinessPagePipe(
        ctx: NextAppContext,
    ): Promise<
        GetServerSidePropsResult<{ businessId: string; seoMeta: SeoMeta }>
    > {
        let businessId = getParam(ctx, 'slug');
        if (!businessId) {
            return { notFound: true };
        }

        try {
            await preloadGetBusiness(ctx, { businessId });
        } catch (e) {
            if (isAxiosError(e) && e.response?.status === 404) {
                return { notFound: true };
            } else {
                captureError(e);
            }
        }
        const business = queryClient.getQueryData<BusinessModel>(
            businessKeys.getBusiness(businessId),
        );
        businessId = business?.id ?? businessId ?? '';

        if (!business) {
            return { notFound: true };
        }

        await parallel([
            preloadGetBusinessReviews(ctx, {
                businessId,
                pageable: { page: 0, size: 10 },
            }),
            ctx.user && preloadGetBusinessFavorite(ctx, { businessId }),
        ]);

        const sameAs = [];
        if (business?.instagramURL) {
            sameAs.push(business.instagramURL);
        }
        if (business?.facebookURL) {
            sameAs.push(business.facebookURL);
        }
        if (business?.websiteURL) {
            sameAs.push(business.websiteURL);
        }

        return {
            props: {
                businessId,
                seoMeta: {
                    title: `${business?.name} - Bookr`,
                    description: business?.description,
                    image:
                        business?.profilePicture || business?.photos?.[0] || '',
                    name: business?.name,
                    url: `https://bookr.ro/b/${business?.slug}`,
                    telephone: business?.phoneNumber || '',
                    formattedAddress: business?.formattedAddress,
                    latitude: business?.latitude || 0,
                    longitude: business?.longitude || 0,
                    openingHours: business?.workingHours || [],
                    sameAs,
                    reviewInfo: business?.reviewInfo || {},
                },
            },
        };
    };
}
