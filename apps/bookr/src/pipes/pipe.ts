import { PreviewData, Redirect } from 'next/types';
import { ParsedUrlQuery } from 'querystring';
import { getRuntimeConfig } from '@bookr-technologies/config';
import { timing } from '@bookr-technologies/core/utilities/debug';
import { UnionToIntersection } from '@bookr-technologies/types';
import { NextAppContext } from '~/api';
import { getSession } from '~/http';

export type GetServerSideProps<
    P extends { [key: string]: any } = { [key: string]: any },
    Q extends ParsedUrlQuery = ParsedUrlQuery,
    D extends PreviewData = PreviewData,
> = (
    context: NextAppContext<Q, D>,
) => Promise<{ props: P } | { redirect: Redirect } | { notFound: true }>;

type Params<P extends GetServerSideProps[]> =
    P[number] extends GetServerSideProps<infer U> ? U : never;

export type PropsOf<T extends GetServerSideProps<any>> =
    T extends GetServerSideProps<infer U> ? U : never;

function setupContext(context: NextAppContext) {
    context.contextValues = context.contextValues ?? {};
}

async function runPipe<P extends GetServerSideProps, C extends NextAppContext>(
    pipe: P,
    context: C,
    skipTimes = false,
) {
    const session = await getSession(context.req, context.res);
    const pipeName = pipe.name || 'anonymous';
    const timeframe = `${session.id} -- run pipe: [${pipeName}]`;
    skipTimes =
        getRuntimeConfig().production ||
        skipTimes ||
        pipeName === 'pipeAsyncRunner';

    try {
        return await timing(timeframe, () => pipe(context), skipTimes);
    } catch (e) {
        !skipTimes && console.error('pipe error [%s]:', pipeName, e);
        throw e;
    }
}

/**
 * Start -> A --+
 *       -> B --+--> Resolve
 *       -> C --+
 * @param pipes
 */
export function pipeAsync<
    P extends GetServerSideProps[],
    U extends UnionToIntersection<Params<P>>,
>(...pipes: P): GetServerSideProps<U extends object ? U : Record<string, any>> {
    async function pipeAsyncRunner(context: NextAppContext) {
        setupContext(context);

        const result = await Promise.all(
            pipes.map((pipe) => runPipe(pipe, context, true)),
        );

        if (result.find((r) => 'notFound' in r && r.notFound)) {
            return { notFound: true };
        }

        const redirectResult = result.find(
            (r) => 'redirect' in r && r.redirect,
        );
        if (
            redirectResult &&
            'redirect' in redirectResult &&
            redirectResult.redirect
        ) {
            return redirectResult;
        }

        const props = result.reduce((acc, r) => {
            if ('props' in r) {
                return { ...acc, ...(r.props as any) };
            }

            return acc;
        }, {});

        return { props } as any;
    }

    Object.defineProperty(pipeAsyncRunner, 'name', {
        value: `pipeAsync(${pipes.map((p) => p.name).join(', ')})`,
    });

    return pipeAsyncRunner;
}

/**
 * Start -> A -> B -> C -> Resolve
 * @param pipes
 */
export function pipe<
    P extends GetServerSideProps[],
    U extends UnionToIntersection<Params<P>>,
>(...pipes: P): GetServerSideProps<U extends object ? U : Record<string, any>> {
    async function pipeRunner(context: NextAppContext) {
        setupContext(context);
        let props: any = {};
        for (const pipe of pipes) {
            context.contextValues.previousProps = {
                ...context.contextValues.previousProps,
                ...props,
            };

            const result = await runPipe(pipe, context);
            if (!('props' in result)) {
                return result;
            }

            const resultProps = result.props;
            props = { ...props, ...resultProps };
            context.contextValues.previousProps = {
                ...context.contextValues.previousProps,
                ...props,
            };
        }

        return {
            props: {
                ...props,
            },
        };
    }

    Object.defineProperty(pipeRunner, 'name', {
        value: `pipe(${pipes.map((p) => p.name).join(', ')})`,
    });

    return pipeRunner;
}
