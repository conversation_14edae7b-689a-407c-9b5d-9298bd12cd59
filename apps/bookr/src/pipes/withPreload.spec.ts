import { notFound } from '@hapi/boom';
import { withPreload } from '~/pipes/withPreload';

describe('bookr#pipes/withPreload', () => {
    it('should correctly treat not found exceptions', async () => {
        const preloader = jest.fn(() => {
            // nothing
        });

        const pipe = withPreload(preloader, {
            argsGetter: () => {
                throw notFound();

                return { id: '123' };
            },
        });

        const results = await pipe({} as any);

        expect(preloader).not.toBeCalled();
        expect(results).toEqual({
            notFound: true,
        });
    });
});
