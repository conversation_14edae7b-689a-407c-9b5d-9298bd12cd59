import { UserModel } from '@bookr-technologies/sdk';
import { NextAppContext } from '~/api';
import { getUserConfigStep } from '~/lib/utilities/user';

export async function withAuthenticated(context: NextAppContext) {
    const { redirect: redirectPath } = context.query;

    const redirect =
        (Array.isArray(redirectPath) ? redirectPath[0] : redirectPath) ?? '/';

    try {
        const user = context.user as UserModel;

        if (getUserConfigStep(user) === -1) {
            return {
                redirect: {
                    permanent: true,
                    destination: redirect,
                },
            };
        }
    } catch (e) {
        // nothing
    }

    return {
        props: {
            redirect,
        },
    };
}
