import { notFound } from '@hapi/boom';
import { preloadGetUser } from '~/api';
import { withPreload } from '~/pipes/withPreload';

export function withUser(required = false) {
    return withPreload(preloadGetUser, {
        argsGetter: (context) => {
            const uid = context.user?.uid ?? '';
            if (!uid && required) {
                throw notFound();
            }

            return { uid };
        },
    });
}
