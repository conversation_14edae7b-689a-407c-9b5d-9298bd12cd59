import { silentPromise, str } from '@bookr-technologies/core';
import { NextAppContext, userQueries } from '~/api';
import { getUserConfigStep } from '~/lib/utilities/user';

export async function withConfiguredUser(ctx: NextAppContext) {
    const redirect = decodeURIComponent(str(ctx.query.redirect)) ?? '/';
    if (ctx.user?.uid) {
        const user = await silentPromise(
            userQueries.getUser(ctx.apiClients.bookr, ctx.user.uid),
        );

        if (getUserConfigStep(user) === -1) {
            return {
                redirect: {
                    permanent: true,
                    destination: redirect,
                },
            };
        }
    }

    return {
        props: { redirect },
    };
}
