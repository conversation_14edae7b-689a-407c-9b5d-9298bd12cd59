import { HttpStatusCode } from 'axios';
import { NextApiResponse } from 'next';

export class HttpResponse<T = any> {
    constructor(
        public data: T,
        public statusCode?: HttpStatusCode,
    ) {}

    public static ok() {
        return new HttpResponse({ ok: true }, HttpStatusCode.Ok);
    }

    public wrap(): { data: any } {
        return {
            data: this.data,
        };
    }

    public getStatus() {
        return this.statusCode || HttpStatusCode.Ok;
    }

    public send(response: NextApiResponse) {
        response.status(this.getStatus()).json(this.wrap());
    }
}
