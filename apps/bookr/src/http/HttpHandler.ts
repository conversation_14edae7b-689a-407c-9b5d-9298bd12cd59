import { badRequest, isBoom, methodNotAllowed, internal } from '@hapi/boom';
import Cookies from 'cookies';
import { NextApiRequest, NextApiResponse } from 'next';
import { Session } from 'next-session/lib/types';
import * as Yup from 'yup';
import { HttpResponse } from './HttpResponse';
import { getSession } from './HttpSession';

export abstract class HttpHandler {
    protected request!: NextApiRequest;
    protected response!: NextApiResponse;
    protected cookies!: Cookies;
    protected session!: Session;

    protected init(): void | Promise<void> {
        //
    }

    protected get(): HttpResponse | Promise<HttpResponse> {
        throw methodNotAllowed();
    }

    protected post(): HttpResponse | Promise<HttpResponse> {
        throw methodNotAllowed();
    }

    protected put(): HttpResponse | Promise<HttpResponse> {
        throw methodNotAllowed();
    }

    protected delete(): HttpResponse | Promise<HttpResponse> {
        throw methodNotAllowed();
    }

    public async handle(
        request: NextApiRequest,
        response: NextApiResponse,
    ): Promise<void> {
        this.request = request;
        this.response = response;

        await this.setup();
        await this.init();

        const result = await this.handleMethod();
        await result.send(this.response);
        await this.teardown();
    }

    private async handleMethod() {
        try {
            switch (String(this.request.method).toLowerCase()) {
                case 'get':
                    return await this.get();
                case 'post':
                    return await this.post();
                case 'put':
                    return await this.put();
                case 'delete':
                    return await this.delete();
                default:
                    return this.mapError(methodNotAllowed());
            }
        } catch (e) {
            return this.mapError(e);
        }
    }

    protected async validate<T extends Yup.ObjectShape>(shape: T) {
        const schema = Yup.object().shape(shape);

        try {
            return await schema.validate(this.request.body, {
                abortEarly: false,
            });
        } catch (e) {
            const { errors, message } = e as Yup.ValidationError;
            throw badRequest(message, errors);
        }
    }

    private async setup() {
        this.cookies = new Cookies(this.request, this.response);
        this.session = await getSession(this.request, this.response);
    }

    private async teardown() {
        await this.session.commit();
    }

    private mapError(e: unknown) {
        const error = isBoom(e) ? e : internal('An internal error occurred.');
        const payload = { ...error.output.payload };

        return new HttpResponse(payload, error.output.statusCode);
    }
}
