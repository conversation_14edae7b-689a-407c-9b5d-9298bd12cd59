import RedisStore from 'connect-redis';
import nextSession from 'next-session';
import { promisifyStore } from 'next-session/lib/compat';
import { getRedisClient } from '~/lib/redis';

export const getSession = (
    ...args: Parameters<ReturnType<typeof nextSession>>
) =>
    nextSession({
        store: promisifyStore(
            new RedisStore({
                client: getRedisClient(),
                prefix: 'bkr:',
            }),
        ),
        cookie: {
            secure: process.env.NODE_ENV === 'production',
            sameSite: 'lax',
        },
    })(...args);
