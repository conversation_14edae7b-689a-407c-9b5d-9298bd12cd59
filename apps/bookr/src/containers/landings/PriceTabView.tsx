import Container from '@mui/material/Container';
import Tab from '@mui/material/Tab';
import Tabs from '@mui/material/Tabs';
import { alpha } from '@mui/material/styles';
import { styled } from '@mui/material/styles';
import { SyntheticEvent, useMemo, useState } from 'react';
import { useI18n } from '@bookr-technologies/i18n';
import {
    PeriodTypeEnum,
    SubscriptionPlanTypeEnum,
} from '@bookr-technologies/sdk';
import { getPlanName } from '~/containers/landings/util';
import {
    ExtendedSubscriptionPlanModel,
    isSubscriptionPlanCustom,
    isSubscriptionPlanRecommended,
} from '~/lib/utilities/subscriptions';
import { PlanFeatures } from './PlanFeatures';
import { PricesPlanView } from './PricesPlanView';

const Root = styled(Container)(({ theme }) => ({
    backgroundColor: theme.palette.background.default,
    '.PriceTabView-sticky': {
        position: 'sticky',
        top: 0,
        zIndex: theme.zIndex.appBar + 1,
        backdropFilter: 'blur(14px) saturate(280%)',
        backgroundColor: 'rgba(255, 255, 255, 0.7)',
        margin: theme.spacing(0, -3),
        padding: theme.spacing(0, 3),
        width: `calc(100% + ${theme.spacing(6)})`,
    },

    '.MuiTabs-indicator': {
        display: 'none',
    },
    '.MuiTab-root': {
        padding: theme.spacing(1.5, 2.5),
        margin: theme.spacing(1, 1, 1, 0),
        minHeight: 0,
        fontWeight: 600,
    },
    '.MuiTab-root.Mui-selected': {
        backgroundColor: alpha(theme.palette.info.main, 0.1),
        color: theme.palette.info.main,
        borderRadius: 14,
    },
}));

interface PriceTabViewProps {
    period: PeriodTypeEnum;
    plans: ExtendedSubscriptionPlanModel[];
}

export function PriceTabView({ period, plans }: PriceTabViewProps) {
    const { t } = useI18n();
    const [planType, setPlanType] = useState(
        SubscriptionPlanTypeEnum.Professional,
    );

    const handlePricingPlanTabChange = (
        event: SyntheticEvent,
        newValue: string,
    ) => {
        setPlanType(newValue as SubscriptionPlanTypeEnum);
    };

    const activePlan = useMemo(
        () => plans.find((plan) => plan.planType === planType),
        [plans, planType],
    );

    return (
        <Root>
            <section className={'PriceTabView-sticky'}>
                <Tabs
                    variant={'scrollable'}
                    value={planType}
                    onChange={handlePricingPlanTabChange}
                >
                    {plans.map((plan) => (
                        <Tab
                            disableRipple
                            key={plan.planType}
                            value={plan.planType}
                            label={getPlanName(t, plan.planType)}
                        />
                    ))}
                </Tabs>
                {activePlan ? (
                    <PricesPlanView
                        recommended={isSubscriptionPlanRecommended(activePlan)}
                        custom={isSubscriptionPlanCustom(activePlan)}
                        price={activePlan.price}
                        planType={activePlan.planType}
                        period={period}
                        planName={getPlanName(t, activePlan.planType)}
                    />
                ) : null}
            </section>
            <PlanFeatures planType={planType} />
        </Root>
    );
}
