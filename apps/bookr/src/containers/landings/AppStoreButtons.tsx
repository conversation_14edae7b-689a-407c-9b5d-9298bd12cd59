import Button from '@mui/material/Button';
import Grid, { GridProps } from '@mui/material/Grid';
import Image from 'next/image';
import React from 'react';
import { hrefTarget } from '@bookr-technologies/core';
import appStore from './assets/app-store.png';
import googlePlay from './assets/google-play.png';

export function AppStoreButtons({ ...rest }: GridProps) {
    return (
        <Grid
            container
            alignItems={'center'}
            justifyContent={'center'}
            {...rest}
        >
            <Button
                {...hrefTarget(
                    'https://play.google.com/store/apps/details?id=com.bookr.app',
                )}
            >
                <Image src={googlePlay} alt="GooglePlay" />
            </Button>

            <Button
                {...hrefTarget(
                    'https://apps.apple.com/us/app/bookr-online-booking-app/id1547131136',
                )}
            >
                <Image src={appStore} alt="AppStore" />
            </Button>
        </Grid>
    );
}

export default AppStoreButtons;
