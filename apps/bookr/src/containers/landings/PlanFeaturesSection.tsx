import Box from '@mui/material/Box';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import { useI18n } from '@bookr-technologies/i18n';
import { FeatureValue } from './FeatureValue';

interface Props {
    title: string;
    features?: readonly { title: string; value: string | number | boolean }[];
    onlyValues?: boolean;
    onlyFeatures?: boolean;
}

export function PlanFeaturesSection({
    title,
    features,
    onlyValues,
    onlyFeatures,
}: Props) {
    const { t } = useI18n();
    if (!features?.length) {
        return null;
    }

    return (
        <Grid container pb={5}>
            <Typography
                variant={'body2'}
                color={'textSecondary'}
                fontWeight={600}
                textTransform={'uppercase'}
            >
                {onlyValues ? <>&nbsp;</> : t(title as any)}
            </Typography>
            {features.map((feature, index) => (
                <Grid container key={index} alignItems={'center'} py={2}>
                    {!onlyValues ? (
                        <Box flexGrow={1}>
                            <Typography variant={'body1'} fontWeight={600}>
                                {t(feature.title as any)}
                            </Typography>
                        </Box>
                    ) : null}
                    {!onlyFeatures ? (
                        <Box
                            px={1}
                            display={onlyValues ? 'flex' : 'inline-flex'}
                            width={onlyValues ? '100%' : 'auto'}
                            alignItems={'center'}
                            justifyContent={'center'}
                        >
                            <FeatureValue value={feature.value} />
                        </Box>
                    ) : null}
                </Grid>
            ))}
        </Grid>
    );
}
