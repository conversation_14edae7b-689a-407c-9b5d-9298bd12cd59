import Container from '@mui/material/Container';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import Image from 'next/image';
import React from 'react';
import { useI18n } from '@bookr-technologies/i18n';
import categoryBarber from './assets/barber.png';
import categoryHairstyling from './assets/hairstyling.png';
import categoryMakeUp from './assets/make-up.png';
import categoryMassage from './assets/massage.png';
import categoryNails from './assets/nails.png';

const Root = styled('div')(({ theme }) => ({
    backgroundColor: '#070707',
    '.text-white': {
        color: '#fff',
    },
    '.Category-card': {
        backgroundColor: '#333',
        borderRadius: 20,
        height: 280,
        position: 'relative',
        overflow: 'hidden',
        width: '100%',
        '@media screen and (max-width: 400px)': {
            maxWidth: 300,
        },
    },
    '.Category-headline': {
        color: '#fff',
        position: 'absolute',
        top: 0,
        left: 0,
        padding: theme.spacing(1.25, 2.25),
        zIndex: 5,
    },
    '.Category-image': {
        position: 'absolute',
        left: '-1%',
        right: '-1%',
        bottom: 0,
        width: '102%',
        height: 'auto',
        objectFit: 'cover',
        zIndex: 1,
    },

    '.Category-overlay': {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        width: '100%',
        height: '100%',
        backgroundImage:
            'linear-gradient(0deg, #070707 0%, rgba(0, 0, 0, 0) 75%)',
        zIndex: 3,
    },
}));

const categories = [
    { label: 'categories.hairstyling.name', image: categoryHairstyling },
    { label: 'categories.manicure.name', image: categoryNails },
    { label: 'categories.massage.name', image: categoryMassage },
    { label: 'categories.makeup.name', image: categoryMakeUp },
    { label: 'categories.barber.name', image: categoryBarber },
] as const;

export function BeautyCategories() {
    const { t } = useI18n();

    return (
        <Root>
            <Container disableGutters>
                <Stack>
                    <Typography
                        variant={'h4'}
                        component={'h2'}
                        align={'center'}
                        fontWeight={800}
                        mb={8}
                        className={'text-white'}
                    >
                        {t('landings.beauty.businessesInBeauty')}
                    </Typography>

                    <StyledCategoriesHolder>
                        {categories.map(({ label, image }, index) => (
                            <div key={index} className={'Category-card'}>
                                <Image
                                    className={'Category-image'}
                                    src={image}
                                    alt={label}
                                    priority
                                />
                                <div className={'Category-overlay'} />
                                <Typography
                                    variant={'subtitle1'}
                                    fontWeight={800}
                                    className={'Category-headline'}
                                >
                                    {t(label)}
                                </Typography>
                            </div>
                        ))}
                    </StyledCategoriesHolder>
                </Stack>
            </Container>
        </Root>
    );
}

export const StyledCategoriesHolder = styled('div')(({ theme }) => ({
    display: 'flex',
    overflowX: 'auto',
    width: '100%',
    maxWidth: '100%',
    gap: theme.spacing(2),
    padding: theme.spacing(2),
    justifyContent: 'center',
    scrollbarWidth: 'none',
    msOverflowStyle: 'none',
    '&::-webkit-scrollbar': {
        display: 'none',
    },
    '.Category-card': {
        width: '100%',
        minWidth: 220,
        maxWidth: 220,
    },
    [theme.breakpoints.down(1240)]: {
        justifyContent: 'flex-start',
    },
}));
