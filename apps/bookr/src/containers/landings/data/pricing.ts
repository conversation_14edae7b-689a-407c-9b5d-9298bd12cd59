const allFeatures = [
    {
        title: 'landings.pricing.booking',
        features: [
            { title: 'landings.pricing.staffMembers', value: 'unlimited' },
            { title: 'landings.pricing.businessListing', value: true },
            { title: 'landings.pricing.ownWebsite', value: true },
            { title: 'landings.pricing.accessToCommunity', value: true },
            { title: 'landings.pricing.activityHistory', value: true },
            { title: 'landings.pricing.unlimitedBookings', value: true },
            { title: 'landings.pricing.googleIntegration', value: true },
            { title: 'landings.pricing.waitingList', value: true },
        ],
    },
    {
        title: 'landings.pricing.businessManagement',
        features: [
            { title: 'landings.pricing.salesReports', value: true },
            { title: 'landings.pricing.performanceIndicators', value: true },
            { title: 'landings.pricing.topProfessionals', value: true },
            { title: 'landings.pricing.teamManagement', value: true },
        ],
    },
    {
        title: 'landings.pricing.clientsManagement',
        features: [
            { title: 'landings.pricing.customerRecord', value: true },
            { title: 'landings.pricing.importClients', value: true },
            { title: 'landings.pricing.listClients', value: true },
            { title: 'landings.pricing.clientsDocuments', value: true },
        ],
    },
    {
        title: 'landings.pricing.notifications',
        features: [
            { title: 'landings.pricing.automaticNotifications', value: true },
            { title: 'landings.pricing.reminders', value: true },
            {
                title: 'landings.pricing.personalisedNotifications',
                value: true,
            },
            { title: 'landings.pricing.smsNotifications', value: true },
        ],
    },
    {
        title: 'landings.pricing.assistance',
        features: [
            { title: 'landings.pricing.clientSupport', value: 'anytime' },
        ],
    },
] as const;

export const plansData = {
    free: {
        sections: [
            {
                title: 'landings.pricing.booking',
                features: [
                    { title: 'landings.pricing.staffMembers', value: 1 },
                    { title: 'landings.pricing.businessListing', value: true },
                    { title: 'landings.pricing.ownWebsite', value: true },
                    {
                        title: 'landings.pricing.accessToCommunity',
                        value: true,
                    },
                    { title: 'landings.pricing.activityHistory', value: true },
                    {
                        title: 'landings.pricing.unlimitedBookings',
                        value: true,
                    },
                    {
                        title: 'landings.pricing.googleIntegration',
                        value: true,
                    },
                    { title: 'landings.pricing.waitingList', value: false },
                ],
            },
            {
                title: 'landings.pricing.businessManagement',
                features: [
                    { title: 'landings.pricing.salesReports', value: false },
                    {
                        title: 'landings.pricing.performanceIndicators',
                        value: false,
                    },
                    {
                        title: 'landings.pricing.topProfessionals',
                        value: false,
                    },
                    {
                        title: 'landings.pricing.teamManagement',
                        value: 'limited',
                    },
                ],
            },
            {
                title: 'landings.pricing.clientsManagement',
                features: [
                    { title: 'landings.pricing.customerRecord', value: false },
                    { title: 'landings.pricing.importClients', value: false },
                    { title: 'landings.pricing.listClients', value: false },
                    {
                        title: 'landings.pricing.clientsDocuments',
                        value: false,
                    },
                ],
            },
            {
                title: 'landings.pricing.notifications',
                features: [
                    {
                        title: 'landings.pricing.automaticNotifications',
                        value: true,
                    },
                    { title: 'landings.pricing.reminders', value: true },
                    {
                        title: 'landings.pricing.personalisedNotifications',
                        value: false,
                    },
                    { title: 'landings.pricing.smsNotifications', value: true },
                ],
            },
            {
                title: 'landings.pricing.assistance',
                features: [
                    { title: 'landings.pricing.clientSupport', value: false },
                ],
            },
        ],
    },

    standard: {
        sections: [
            {
                title: 'landings.pricing.booking',
                features: [
                    {
                        title: 'landings.pricing.staffMembers',
                        value: 'unlimited',
                    },
                    { title: 'landings.pricing.businessListing', value: true },
                    { title: 'landings.pricing.ownWebsite', value: true },
                    {
                        title: 'landings.pricing.accessToCommunity',
                        value: true,
                    },
                    { title: 'landings.pricing.activityHistory', value: true },
                    {
                        title: 'landings.pricing.unlimitedBookings',
                        value: true,
                    },
                    {
                        title: 'landings.pricing.googleIntegration',
                        value: true,
                    },
                    { title: 'landings.pricing.waitingList', value: true },
                ],
            },
            {
                title: 'landings.pricing.businessManagement',
                features: [
                    {
                        title: 'landings.pricing.salesReports',
                        value: 'limited',
                    },
                    {
                        title: 'landings.pricing.performanceIndicators',
                        value: 'limited',
                    },
                    {
                        title: 'landings.pricing.topProfessionals',
                        value: false,
                    },
                    { title: 'landings.pricing.teamManagement', value: true },
                ],
            },
            {
                title: 'landings.pricing.clientsManagement',
                features: [
                    { title: 'landings.pricing.customerRecord', value: true },
                    { title: 'landings.pricing.importClients', value: false },
                    { title: 'landings.pricing.listClients', value: true },
                    {
                        title: 'landings.pricing.clientsDocuments',
                        value: false,
                    },
                ],
            },
            {
                title: 'landings.pricing.notifications',
                features: [
                    {
                        title: 'landings.pricing.automaticNotifications',
                        value: true,
                    },
                    { title: 'landings.pricing.reminders', value: true },
                    {
                        title: 'landings.pricing.personalisedNotifications',
                        value: false,
                    },
                    { title: 'landings.pricing.smsNotifications', value: true },
                ],
            },
            {
                title: 'landings.pricing.assistance',
                features: [
                    {
                        title: 'landings.pricing.clientSupport',
                        value: 'limited',
                    },
                ],
            },
        ],
    },

    professional: {
        sections: allFeatures,
    },

    custom: {
        sections: allFeatures,
    },
} as const;
