import GlobalStyles from '@mui/material/GlobalStyles';
import React from 'react';
import { BeautyCards } from './BeautyCards';
import { BeautyCategories } from './BeautyCategories';
import { BeautyFeatures } from './BeautyFeatures';
import { BeautyHero } from './BeautyHero';
import { HomeConclusion } from './HomeConclusion';
import { HomePricingPlans } from './HomePricingPlans';

const globalStyles$ = (
    <GlobalStyles
        styles={{
            '.RootLayoutHeader--root.RootLayoutHeader--root': {
                backgroundColor: '#070707',
                border: 'none',
                '.Logo-root *': {
                    fill: '#fff',
                },
            },
        }}
    />
);

export function BeautyContainer() {
    return (
        <>
            {globalStyles$}
            <BeautyHero />
            <BeautyCategories />
            <BeautyCards />
            <BeautyFeatures />
            <HomePricingPlans />
            <HomeConclusion />
        </>
    );
}
