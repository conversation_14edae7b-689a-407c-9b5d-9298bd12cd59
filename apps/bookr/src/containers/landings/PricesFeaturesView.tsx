import Grid, { GridProps } from '@mui/material/Grid';
import { styled } from '@mui/material/styles';
import clsx from 'clsx';
import { PlanFeatures } from './PlanFeatures';

interface Props extends GridProps {
    planName: string;
    recommended: boolean;
}

const Root = styled(Grid)(({ theme }) => ({
    position: 'relative',
    borderRadius: theme.spacing(0, 0, 2, 2),
    '&.PricesPlanView-recommended': {
        backgroundColor: '#f6f6f6',
    },
}));

export function PricesFeaturesView({
    recommended,
    planName,
    className,
    ...rest
}: Props) {
    const classes = clsx('PricesPlanView-root', className, {
        'PricesPlanView-recommended': recommended,
    });

    return (
        <Root
            container
            direction={'column'}
            alignItems={'center'}
            justifyContent={'flex-start'}
            className={classes}
            {...rest}
        >
            <PlanFeatures planType={planName} onlyValues />
        </Root>
    );
}
