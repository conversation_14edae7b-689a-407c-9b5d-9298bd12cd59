import Container from '@mui/material/Container';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import { useI18n } from '@bookr-technologies/i18n';
import { ApplyNowButton } from '~/containers/landings/ApplyNowButton';

export function HomeConclusion() {
    const { t } = useI18n();

    return (
        <StyledSection>
            <Container>
                <Grid
                    container
                    direction={'column'}
                    alignItems={'center'}
                    justifyContent={'center'}
                >
                    <Typography
                        align={'center'}
                        variant={'h2'}
                        component={'h3'}
                        className={'HomeConclusion-heading'}
                    >
                        {t('landings.conclusionTitle')}
                    </Typography>
                    <Typography
                        align={'center'}
                        variant={'h5'}
                        component={'p'}
                        className={'HomeConclusion-subheading'}
                    >
                        {t('landings.conclusionDescription')}
                    </Typography>
                    <ApplyNowButton
                        variant={'contained'}
                        color={'info'}
                        size={'large'}
                        disableElevation
                    >
                        {t('getStarted')}
                    </ApplyNowButton>
                </Grid>
            </Container>
        </StyledSection>
    );
}

const StyledSection = styled('section')(({ theme }) => ({
    backgroundColor: '#fff',
    padding: '84px 0 110px',
    [theme.breakpoints.down('md')]: {
        padding: '72px 0',
    },

    '.HomeConclusion-heading, .HomeConclusion-subheading': {
        color: '#111',
    },
    '.HomeConclusion-heading': {
        fontSize: 46,
        fontWeight: 800,
        lineHeight: '60px',
        marginBottom: 14,
        [theme.breakpoints.down('md')]: {
            fontSize: 32,
            lineHeight: '42px',
        },
        [theme.breakpoints.down('sm')]: {
            br: {
                display: 'none',
            },
        },
    },
    '.HomeConclusion-subheading': {
        fontSize: 16,
        fontWeight: 600,
        lineHeight: '24px',
        marginBottom: 46,
    },
}));
