import CheckIcon from '@mui/icons-material/Check';
import LoadingButton from '@mui/lab/LoadingButton';
import Grid from '@mui/material/Grid';
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import Paper from '@mui/material/Paper';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import { useMemo } from 'react';
import { getRuntimeConfig } from '@bookr-technologies/config';
import { str } from '@bookr-technologies/core';
import { useEvent, useNotification } from '@bookr-technologies/hooks';
import { useI18n } from '@bookr-technologies/i18n';
import { clsx } from '@bookr-technologies/ui';
import { useCreateCheckoutSessionMutation } from '~/api/mutations/stripe';
import { getPlanName } from '~/containers/landings/util';
import {
    ExtendedSubscriptionPlanModel,
    isSubscriptionPlanCustom,
    isSubscriptionPlanRecommended,
} from '~/lib/utilities/subscriptions';

interface Props {
    plan: ExtendedSubscriptionPlanModel;
    landings?: boolean;
}

const Root = styled(Paper)(({ theme }) => ({
    padding: theme.spacing(4),
    position: 'relative',
    display: 'flex',
    flexDirection: 'column',
    flex: '1 1 auto',
    boxShadow: '0 2px 6px rgba(5, 16, 55, 0.1)',
    '&.Plan-recommended': {
        boxShadow: '0 11px 20px rgba(5, 16, 55, 0.1)',
    },
    '.MuiListItem-root': {
        padding: theme.spacing(0.75, 0),
    },
    '.MuiListItemIcon-root': {
        minWidth: 34,
    },
    '.MuiButton-root': {
        fontSize: 14,
        fontWeight: 600,
    },
    '.Plan-recommendedBadge': {
        height: theme.spacing(4),
        lineHeight: theme.spacing(4),
        padding: theme.spacing(0, 2),
        display: 'inline-flex',
        backgroundColor: theme.palette.info.main,
        color: theme.palette.info.contrastText,
        borderRadius: 9,
        position: 'absolute',
        top: 0,
        left: '50%',
        transform: 'translate(-50%, -50%)',
        boxShadow: '0 2px 6px rgba(5, 16, 55, 0.1)',
    },
}));

export function Plan({ plan, landings }: Props) {
    const { t } = useI18n();
    const notification = useNotification();
    const custom = isSubscriptionPlanCustom(plan);
    const recommended = isSubscriptionPlanRecommended(plan);
    const session = useCreateCheckoutSessionMutation();

    const href = useMemo(() => {
        const runtimeConfig = getRuntimeConfig();

        if (custom) {
            return 'mailto:<EMAIL>?subject=Custom%20price%20quote';
        }

        return runtimeConfig.urls.dashboard;
    }, [custom]);

    const features = useMemo(
        () =>
            str(
                t(
                    `pricing.plans.${plan.planType.toLowerCase()}.features` as any,
                ),
            )
                .split('\n')
                .map((feature) => feature.trim())
                .filter(Boolean),
        [plan.planType, t],
    );

    const handleChoose = useEvent(() => {
        if (!landings) {
            session.mutate(
                {
                    priceId: plan.priceId,
                    successUrl: 'auto',
                    cancelUrl: new URL(
                        `${window.location.pathname}?action=cancel`,
                        window.location.origin,
                    ).toString(),
                },
                {
                    onSuccess: (data) => {
                        if (data.sessionUrl) {
                            window.location.href = data.sessionUrl;
                            return;
                        }

                        window.location.reload();
                    },
                    onError: () => notification.error(t('somethingWentWrong')),
                },
            );
        }
    });

    return (
        <Root className={clsx({ 'Plan-recommended': recommended })}>
            {recommended ? (
                <Typography
                    className={'Plan-recommendedBadge'}
                    variant={'body2'}
                    color={'primary'}
                    fontWeight={600}
                >
                    {t('recommended')}
                </Typography>
            ) : null}

            <Typography variant={'h4'} fontWeight={800}>
                {getPlanName(t, plan.planType)}
            </Typography>
            <Typography variant={'h5'} fontWeight={600} mb={2}>
                {custom ? t('pricing.plans.custom.price') : plan.price}
            </Typography>

            <List>
                {plan.previousPlan ? (
                    <ListItem>
                        <ListItemIcon>
                            <CheckIcon color={'inherit'} />
                        </ListItemIcon>
                        <ListItemText
                            primary={t('pricing.planBenefitsFrom', {
                                plan: getPlanName(
                                    t,
                                    plan.previousPlan.planType,
                                ),
                            })}
                        />
                    </ListItem>
                ) : null}

                {features.map((feature, index) => (
                    <ListItem key={index}>
                        <ListItemIcon>
                            <CheckIcon color={'info'} />
                        </ListItemIcon>
                        <ListItemText primary={feature} />
                    </ListItem>
                ))}
            </List>

            <Grid container flexGrow={1} alignItems={'flex-end'} mt={3}>
                <LoadingButton
                    loading={session.isLoading}
                    variant={'contained'}
                    color={recommended ? 'info' : 'inherit'}
                    onClick={handleChoose}
                    href={landings ? href : undefined}
                    size={'large'}
                    fullWidth
                    disableElevation
                >
                    {custom
                        ? t('contactUs')
                        : t('pricing.choosePlan', {
                              plan: getPlanName(t, plan.planType),
                          })}
                </LoadingButton>
            </Grid>
        </Root>
    );
}
