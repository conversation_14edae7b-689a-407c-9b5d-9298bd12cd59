import Container from '@mui/material/Container';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import { styled, Theme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import React from 'react';
import { useI18n } from '@bookr-technologies/i18n';
import { ApplyNowButton } from './ApplyNowButton';
import { FeatureCard, FeatureCardImage } from './FeatureCard';
import dashboardView from './assets/dashboard-view.jpg';
import interaction from './assets/interaction.jpg';
import management from './assets/management.jpg';
import notifications from './assets/notifications.jpg';
import stats from './assets/stats.jpg';

const Root = styled('div')(({ theme }) => ({
    padding: theme.spacing(14, 0, 5),
    backgroundColor: '#070707',
    [theme.breakpoints.down('sm')]: {
        padding: theme.spacing(8, 0, 5),
    },
    '.BeautyCards-cardContent': {
        padding: theme.spacing(6.25),
        [theme.breakpoints.down('sm')]: {
            padding: theme.spacing(4, 4),
        },
    },
    '& .FeaturedCard-root .headline': {
        [theme.breakpoints.down('sm')]: {
            fontSize: 26,
            fontWeight: 600,
        },
    },
    '& .FeaturedCard-root .subHeadline': {
        color: '#AFAFAF !important',
        [theme.breakpoints.down('sm')]: {
            fontSize: 16,
            fontWeight: 600,
        },
    },
    '& .FeaturedCard-root': {
        backgroundColor: '#1f1f1f !important',
        marginTop: theme.spacing(6),
        [theme.breakpoints.down('sm')]: {
            marginTop: theme.spacing(4),
        },
    },
    '& .FeatureCardImage-root': {
        maxWidth: 362,
        '&.mobileRight': {
            marginLeft: 'auto',
            marginRight: '0 !important',
        },
        [theme.breakpoints.down('sm')]: {
            maxWidth: 240,
            marginLeft: 'auto',
            marginRight: 'auto',
        },
    },
    '.FeaturedCard-root .MuiTypography-root, .text-white': {
        color: '#fff',
    },
}));

export function BeautyCards() {
    const { t } = useI18n();

    const isDownMd = useMediaQuery<Theme>((theme) =>
        theme.breakpoints.down('md'),
    );

    return (
        <Root>
            <Container>
                <Grid
                    container
                    maxWidth={'850px !important'}
                    mt={4}
                    mx={'auto'}
                >
                    <Typography
                        variant={isDownMd ? 'h4' : 'h3'}
                        component={'h2'}
                        align={'center'}
                        fontWeight={800}
                        mb={8}
                        className={'text-white'}
                    >
                        {t('landings.beauty.section1Heading')}
                    </Typography>
                    <FeatureCard
                        inverted
                        className={'BeautyCards-dashboardView'}
                    >
                        <Grid
                            item
                            xs
                            className={
                                'BeautyCards-cardContent BeautyCards-dashboardViewContent'
                            }
                        >
                            <Typography
                                variant={'h3'}
                                fontWeight={800}
                                mb={2.5}
                                className={'headline'}
                            >
                                {t('landings.beauty.feature1Title')}
                            </Typography>
                            <Typography
                                variant={'subtitle1'}
                                color={'textSecondary'}
                                fontSize={18}
                                className={'subHeadline'}
                                fontWeight={600}
                                mb={4}
                            >
                                {t('landings.beauty.feature1Description')}
                            </Typography>

                            <ApplyNowButton
                                variant={'contained'}
                                color={'info'}
                                size={'medium'}
                                disableElevation
                            >
                                {t('getStarted')}
                            </ApplyNowButton>
                        </Grid>
                        <FeatureCardImage
                            src={dashboardView}
                            alt={t('landings.beauty.feature1Title')}
                            className="mobileRight"
                        />
                    </FeatureCard>
                    <FeatureCard inverted className={'BeautyCards-stats'}>
                        <FeatureCardImage
                            src={stats}
                            alt={'Indicatori de performanță'}
                        />
                        <Grid
                            item
                            xs
                            className={
                                'BeautyCards-cardContent BeautyCards-statsContent'
                            }
                        >
                            <Typography
                                variant={'h3'}
                                fontWeight={800}
                                mb={2.5}
                                className={'headline'}
                            >
                                {t('landings.beauty.feature3Title')}
                            </Typography>
                            <Typography
                                variant={'subtitle1'}
                                color={'textSecondary'}
                                fontSize={18}
                                className={'subHeadline'}
                                fontWeight={600}
                            >
                                {t('landings.beauty.feature3Description')}
                            </Typography>
                        </Grid>
                    </FeatureCard>
                    <FeatureCard inverted className={'BeautyCards-interaction'}>
                        <Grid
                            item
                            xs
                            className={
                                'BeautyCards-cardContent BeautyCards-interactionContent'
                            }
                        >
                            <Typography
                                variant={'h3'}
                                fontWeight={800}
                                mb={2.5}
                                className={'headline'}
                            >
                                {t('landings.beauty.feature5Title')}
                            </Typography>
                            <Typography
                                variant={'subtitle1'}
                                color={'textSecondary'}
                                fontSize={18}
                                className={'subHeadline'}
                                fontWeight={600}
                                mb={4}
                            >
                                {t('landings.beauty.feature5Description')}
                            </Typography>

                            <ApplyNowButton
                                variant={'contained'}
                                color={'info'}
                                size={'medium'}
                                disableElevation
                            >
                                {t('getStarted')}
                            </ApplyNowButton>
                        </Grid>
                        <FeatureCardImage
                            src={interaction}
                            alt={t('landings.beauty.feature5Title')}
                        />
                    </FeatureCard>

                    <FeatureCard
                        inverted
                        className={'BeautyCards-notifications'}
                    >
                        <FeatureCardImage
                            src={notifications}
                            alt={t('landings.beauty.feature4Title')}
                            sx={{ mt: 2 }}
                        />

                        <Grid
                            item
                            xs
                            className={
                                'BeautyCards-cardContent BeautyCards-notificationsContent'
                            }
                        >
                            <Typography
                                variant={'h3'}
                                fontWeight={800}
                                mb={2.5}
                                className={'headline'}
                            >
                                {t('landings.beauty.feature4Title')}
                            </Typography>
                            <Typography
                                variant={'subtitle1'}
                                color={'textSecondary'}
                                fontSize={18}
                                className={'subHeadline'}
                                fontWeight={600}
                                mb={4}
                            >
                                {t('landings.beauty.feature4Description')}
                            </Typography>
                        </Grid>
                    </FeatureCard>
                    <FeatureCard inverted className={'BeautyCards-management'}>
                        <Grid
                            item
                            xs
                            className={
                                'BeautyCards-cardContent BeautyCards-managementContent'
                            }
                        >
                            <Typography
                                variant={'h3'}
                                fontWeight={800}
                                mb={2.5}
                                className={'headline'}
                            >
                                {t('landings.beauty.businessManagement')}
                            </Typography>
                            <Typography
                                variant={'subtitle1'}
                                color={'textSecondary'}
                                fontSize={18}
                                className={'subHeadline'}
                                fontWeight={600}
                                mb={4}
                            >
                                {t(
                                    'landings.beauty.businessManagementDescription',
                                )}
                            </Typography>
                        </Grid>
                        <FeatureCardImage
                            src={management}
                            alt={'Gestionarea afacerii'}
                        />
                    </FeatureCard>
                </Grid>
            </Container>
        </Root>
    );
}
