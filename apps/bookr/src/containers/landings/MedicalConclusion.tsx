import Box from '@mui/material/Box';
import Container from '@mui/material/Container';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import React from 'react';
import { useI18n } from '@bookr-technologies/i18n';
import { ApplyNowButton } from './ApplyNowButton';

export function MedicalConclusion() {
    const { t } = useI18n();
    return (
        <Box bgcolor={'#fff'} py={14}>
            <Container>
                <Grid
                    container
                    direction={'column'}
                    alignItems={'center'}
                    justifyContent={'center'}
                    maxWidth={840}
                    mx={'auto'}
                >
                    <Typography
                        align={'center'}
                        variant={'h3'}
                        component={'h3'}
                        fontWeight={800}
                        mb={2}
                    >
                        {t('landings.medical.conclusionTitle')}
                    </Typography>
                    <Typography
                        align={'center'}
                        variant={'body1'}
                        component={'p'}
                        fontWeight={600}
                        color={'textSecondary'}
                        mb={6}
                    >
                        {t('landings.medical.conclusionDescription')}
                    </Typography>
                    <ApplyNowButton
                        variant={'contained'}
                        color={'info'}
                        size={'large'}
                        disableElevation
                        sx={{ minWidth: 240 }}
                    >
                        {t('startNow')}
                    </ApplyNowButton>
                </Grid>
            </Container>
        </Box>
    );
}
