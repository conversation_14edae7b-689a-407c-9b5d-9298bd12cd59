import Container from '@mui/material/Container';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import { styled, Theme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import Image from 'next/image';
import { useI18n } from '@bookr-technologies/i18n';
import { ApplyNowButton } from './ApplyNowButton';
import MedicalHeroImage from './assets/medical/medical-hero.jpg';

const Root = styled('div')(({ theme }) => ({
    display: 'flex',
    backgroundColor: '#111',
    padding: theme.spacing(16, 0, 18, 0),

    '.Hero-headline, .Hero-subHeadline': {
        marginBottom: theme.spacing(3),
        color: '#fff',
        maxWidth: 440,
    },
    '.Hero-button': {
        borderRadius: 20,
        padding: theme.spacing(2, 4),
        maxWidth: 260,
    },
    '.Hero-image': {
        width: '100%',
        height: '100%',
        padding: theme.spacing(8),
    },
}));

export function MedicalHero() {
    const { t } = useI18n();
    const isDownMd = useMediaQuery<Theme>((theme) =>
        theme.breakpoints.down('md'),
    );

    return (
        <Root>
            <Container>
                <Grid container>
                    <Grid
                        item
                        xs={12}
                        md={6}
                        container
                        direction={'column'}
                        alignItems={isDownMd ? 'center' : 'flex-start'}
                        justifyContent={'center'}
                    >
                        <Typography
                            className={'Hero-headline'}
                            variant={'h2'}
                            component={'h1'}
                            fontWeight={800}
                            align={isDownMd ? 'center' : 'left'}
                        >
                            {t('landings.medical.heroTitle')}
                        </Typography>
                        <Typography
                            className={'Hero-subHeadline'}
                            variant={'subtitle1'}
                            fontWeight={600}
                            align={isDownMd ? 'center' : 'left'}
                        >
                            {t('landings.medical.heroSubtitle')}
                        </Typography>
                        <ApplyNowButton
                            variant={'contained'}
                            color={'secondary'}
                            className={'Hero-button'}
                            size={'large'}
                            fullWidth
                        >
                            {t('startNow')}
                        </ApplyNowButton>
                    </Grid>
                    {!isDownMd && (
                        <Grid
                            item
                            xs={6}
                            container
                            alignItems={'center'}
                            justifyContent={'flex-end'}
                        >
                            <Image
                                className={'Hero-image'}
                                src={MedicalHeroImage}
                                alt="Medical hero"
                            />
                        </Grid>
                    )}
                </Grid>
            </Container>
        </Root>
    );
}
