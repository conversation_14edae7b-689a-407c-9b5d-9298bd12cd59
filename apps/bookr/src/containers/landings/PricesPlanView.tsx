import Button from '@mui/material/Button';
import Grid, { GridProps } from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import { styled, Theme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import { useMemo } from 'react';
import { getRuntimeConfig } from '@bookr-technologies/config';
import { useI18n } from '@bookr-technologies/i18n';
import {
    PeriodTypeEnum,
    SubscriptionPlanTypeEnum,
} from '@bookr-technologies/sdk';
import { clsx } from '@bookr-technologies/ui/utils';

interface Props extends GridProps {
    planName: string;
    price: string;
    period: PeriodTypeEnum;
    planType: SubscriptionPlanTypeEnum;
    recommended: boolean;
    custom: boolean;
}

const Root = styled(Grid)(({ theme }) => ({
    position: 'relative',
    padding: theme.spacing(5, 2),
    borderRadius: theme.spacing(2, 2, 0, 0),
    [theme.breakpoints.down('lg')]: {
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'flex-end',
        justifyContent: 'space-between',
        padding: theme.spacing(2, 0, 3),
    },
    '&.PricesPlanView-recommended': {
        [theme.breakpoints.up('lg')]: {
            backgroundColor: '#f6f6f6',
        },
    },
    '.PricesPlanView-recommendedBadge': {
        height: theme.spacing(4),
        lineHeight: theme.spacing(4),
        padding: theme.spacing(0, 2),
        display: 'inline-flex',
        backgroundColor: theme.palette.info.main,
        color: theme.palette.info.contrastText,
        borderRadius: 9,
        boxShadow: '0 2px 6px rgba(5, 16, 55, 0.1)',
        [theme.breakpoints.down('lg')]: {
            marginBottom: theme.spacing(2),
        },
        [theme.breakpoints.up('lg')]: {
            position: 'absolute',
            top: 0,
            left: '50%',
            transform: 'translate(-50%, -50%)',
        },
    },
    '.PricesPlanView-content': {
        marginBottom: theme.spacing(5),
        [theme.breakpoints.down('lg')]: {
            marginBottom: theme.spacing(0),
        },
    },
    '.PricesPlanView-action': {
        [theme.breakpoints.down('lg')]: {
            padding: theme.spacing(0.5, 2),
        },
    },
}));

export function PricesPlanView({
    recommended,
    custom,
    price,
    period,
    planName,
    planType,
    className,
    ...rest
}: Props) {
    const { t } = useI18n();
    const classes = clsx('PricesPlanView-root', className, {
        'PricesPlanView-recommended': recommended,
    });
    const isDownXl = useMediaQuery<Theme>((theme) =>
        theme.breakpoints.down('xl'),
    );

    const href = useMemo(() => {
        const runtimeConfig = getRuntimeConfig();

        if (custom) {
            return 'mailto:<EMAIL>?subject=Custom%20price%20quote';
        }

        return runtimeConfig.urls.dashboard;
    }, [custom]);

    return (
        <Root
            container
            direction={'column'}
            alignItems={'center'}
            justifyContent={'flex-end'}
            className={classes}
            {...rest}
        >
            <div className={'PricesPlanView-content'}>
                {recommended ? (
                    <Typography
                        className={'PricesPlanView-recommendedBadge'}
                        variant={'body2'}
                        color={'primary'}
                        fontWeight={600}
                    >
                        {t('recommended')}
                    </Typography>
                ) : null}

                <Typography
                    variant={isDownXl ? 'h5' : 'h4'}
                    fontWeight={800}
                    align={isDownXl ? 'left' : 'center'}
                    mb={1}
                >
                    {planName}
                </Typography>
                <Typography
                    variant={isDownXl ? 'body1' : 'h5'}
                    fontWeight={600}
                    align={isDownXl ? 'left' : 'center'}
                >
                    {t(`pricing.plans.${planType.toLowerCase()}.price` as any, {
                        defaultValue: t('planPrice', {
                            period: t(period.toLowerCase() as any),
                            price,
                        }),
                    })}
                </Typography>
            </div>

            <Button
                variant={'contained'}
                size={isDownXl ? 'small' : 'medium'}
                color={recommended ? 'info' : 'inherit'}
                href={href}
                className={'PricesPlanView-action'}
            >
                {custom ? t('contactUs') : t('startNow')}
            </Button>
        </Root>
    );
}
