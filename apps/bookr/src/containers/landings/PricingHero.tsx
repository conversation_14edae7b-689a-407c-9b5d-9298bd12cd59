import Grid, { GridProps } from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import React from 'react';
import { useI18n } from '@bookr-technologies/i18n';

export function PricingHero({ children, ...rest }: GridProps) {
    const { t } = useI18n();

    return (
        <Grid
            container
            direction={'column'}
            alignItems={'center'}
            justifyContent={'center'}
            pt={18}
            pb={10}
            px={2}
            {...rest}
        >
            <Typography
                variant={'h2'}
                component={'h1'}
                fontWeight={800}
                align={'center'}
                mb={4}
                maxWidth={800}
            >
                {t('pricing.title')}
            </Typography>
            <Typography
                variant={'body1'}
                component={'h2'}
                fontWeight={600}
                align={'center'}
                color={'textSecondary'}
                maxWidth={554}
                mb={4}
            >
                {t('pricing.subtitle')}
            </Typography>

            {children}
        </Grid>
    );
}
