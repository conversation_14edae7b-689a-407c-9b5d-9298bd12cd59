import Grid, { GridProps } from '@mui/material/Grid';
import { styled } from '@mui/material/styles';
import Image, { ImageProps } from 'next/image';
import { num } from '@bookr-technologies/core';
import { clsx } from '@bookr-technologies/ui';

const Root = styled(Grid)`
    display: flex;
    box-shadow: 0 2px 6px rgba(5, 16, 55, 0.1);
    border-radius: 20px;
    background-color: #fff;
    position: relative;
    overflow: hidden;
    flex: 1 1 auto;
    z-index: 1;
    &.inverted {
        background-color: #111;
    }
`;

interface FeatureCardProps extends GridProps {
    inverted?: boolean;
}

export function FeatureCard({
    inverted,
    className,
    ...rest
}: FeatureCardProps) {
    return (
        <Root
            container
            className={clsx('FeaturedCard-root', className, { inverted })}
            {...rest}
        />
    );
}

export const FeatureCardImage = styled(
    ({
        className,
        alt,
        ...rest
    }: Omit<ImageProps, 'alt'> & { alt?: string }) => (
        <Image
            className={className + ' FeatureCardImage-root'}
            alt={alt || 'Featured card'}
            {...rest}
        />
    ),
)`
    & {
        position: relative;
        display: inline-flex;
        flex: 1 1 auto;
        height: auto;
        object-fit: contain;
        object-position: bottom;
        max-width: ${({ width = 0 }) =>
            num(width) > 0 ? width + 'px' : '100%'};
        z-index: -1;
    }
`;
