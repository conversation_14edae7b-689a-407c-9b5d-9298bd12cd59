import Container from '@mui/material/Container';
import Grid from '@mui/material/Grid';
import { styled } from '@mui/material/styles';
import { useI18n } from '@bookr-technologies/i18n';
import { PeriodTypeEnum } from '@bookr-technologies/sdk';
import { getPlanName } from '~/containers/landings/util';
import {
    ExtendedSubscriptionPlanModel,
    isSubscriptionPlanCustom,
    isSubscriptionPlanRecommended,
} from '~/lib/utilities/subscriptions';
import { PlanFeatures } from './PlanFeatures';
import { PricesFeaturesView } from './PricesFeaturesView';
import { PricesPlanView } from './PricesPlanView';

interface Props {
    period: PeriodTypeEnum;
    plans: ExtendedSubscriptionPlanModel[];
}

const Root = styled('section')(({ theme }) => ({
    '.MuiContainer-root': {
        maxWidth: 1440,
        margin: theme.spacing(2, 'auto', 10, 'auto'),
    },
    '.PricesView-sticky': {
        position: 'sticky',
        top: 16,
        zIndex: 3,
        '&:not(.PricesPlanView-recommended)': {
            backgroundColor: 'rgba(255, 255, 255, 0.7)',
            backdropFilter: 'blur(14px) saturate(280%)',
            top: 0,
            paddingTop: theme.spacing(7),
        },
    },
}));

export function PricesView({ plans, period }: Props) {
    const { t } = useI18n();

    return (
        <Root>
            <Container>
                <Grid
                    container
                    className={'PricesView-sticky'}
                    alignItems={'flex-end'}
                >
                    <Grid item xs>
                        <PlanFeatures planType={'standard'} onlyFeatures />
                    </Grid>
                    {plans.map((plan) =>
                        plan.price ? (
                            <Grid
                                item
                                xs
                                maxWidth={'268px !important'}
                                key={plan.priceId}
                            >
                                <PricesPlanView
                                    planName={getPlanName(t, plan.planType)}
                                    price={plan.price}
                                    period={period}
                                    planType={plan.planType}
                                    recommended={isSubscriptionPlanRecommended(
                                        plan,
                                    )}
                                    custom={isSubscriptionPlanCustom(plan)}
                                    className={'PricesView-sticky'}
                                />
                                <PricesFeaturesView
                                    planName={getPlanName(t, plan.planType)}
                                    recommended={isSubscriptionPlanRecommended(
                                        plan,
                                    )}
                                />
                            </Grid>
                        ) : null,
                    )}
                </Grid>
            </Container>
        </Root>
    );
}
