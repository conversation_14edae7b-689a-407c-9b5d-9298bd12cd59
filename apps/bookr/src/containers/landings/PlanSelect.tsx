import CircularProgress from '@mui/material/CircularProgress';
import Grid from '@mui/material/Grid';
import Tab from '@mui/material/Tab';
import Tabs from '@mui/material/Tabs';
import { alpha, styled, Theme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import React, { useCallback, useMemo, useState } from 'react';
import { useI18n } from '@bookr-technologies/i18n';
import {
    PeriodTypeEnum,
    SubscriptionPlanTypeEnum,
} from '@bookr-technologies/sdk';
import { useGetSubscriptionPlansQuery } from '~/api';
import {
    filerSubscriptionPlansByPeriod,
    getSortedPlans,
} from '~/lib/utilities/subscriptions';
import { Plan } from './Plan';

interface Props {
    period: PeriodTypeEnum;
    withCustom?: boolean;
    landings?: boolean;
}

const Root = styled('div')(({ theme }) => ({
    '.MuiTabs-indicator': {
        display: 'none',
    },
    '.MuiTabs-root': {
        width: '100%',
    },
    '.MuiTab-root': {
        padding: theme.spacing(1.5, 2.5),
        margin: theme.spacing(1, 1, 1, 0),
        minHeight: 0,
        fontWeight: 600,
    },
    '.MuiTab-root.Mui-selected': {
        backgroundColor: alpha(theme.palette.info.main, 0.1),
        color: theme.palette.info.main,
        borderRadius: 14,
    },
}));

export function PlanSelect({ period, landings, withCustom }: Props) {
    const plans = useGetSubscriptionPlansQuery(true);
    const { t } = useI18n();
    const [planType, setPlanType] = useState(
        SubscriptionPlanTypeEnum.Professional,
    );
    const isDownLg = useMediaQuery<Theme>((theme) =>
        theme.breakpoints.down('lg'),
    );

    const isLoading = plans.isLoading;
    const list = useMemo(
        () =>
            getSortedPlans(plans.data).filter(
                filerSubscriptionPlansByPeriod(period),
            ),
        [period, plans.data],
    );

    const activePlan = useMemo(
        () =>
            list.find(
                (plan) =>
                    String(plan.planType).toLowerCase() ===
                    String(planType).toLowerCase(),
            ),
        [planType, list],
    );

    const handleTabChange = useCallback(
        (event: React.SyntheticEvent, newValue: string) =>
            setPlanType(newValue as SubscriptionPlanTypeEnum),
        [],
    );

    const plans$ = useMemo(() => {
        if (isDownLg) {
            return (
                <>
                    <Tabs
                        variant={'scrollable'}
                        value={planType}
                        onChange={handleTabChange}
                    >
                        <Tab
                            disableRipple
                            value={SubscriptionPlanTypeEnum.Free}
                            label={t('pricing.plans.free.name')}
                        />
                        <Tab
                            disableRipple
                            value={SubscriptionPlanTypeEnum.Standard}
                            label={t('pricing.plans.standard.name')}
                        />
                        <Tab
                            disableRipple
                            value={SubscriptionPlanTypeEnum.Professional}
                            label={t('pricing.plans.professional.name')}
                        />
                        <Tab
                            disableRipple
                            value={SubscriptionPlanTypeEnum.Custom}
                            label={t('pricing.plans.custom.name')}
                        />
                    </Tabs>
                    {activePlan ? (
                        <Grid container mt={isDownLg ? 4 : 0}>
                            <Plan plan={activePlan} landings={landings} />
                        </Grid>
                    ) : null}
                </>
            );
        }

        return list.map((plan) => (
            <Grid item xs={withCustom ? 3 : 4} key={plan.priceId} container>
                <Plan plan={plan} landings={landings} />
            </Grid>
        ));
    }, [
        isDownLg,
        list,
        planType,
        handleTabChange,
        t,
        activePlan,
        landings,
        withCustom,
    ]);

    if (isLoading) {
        return (
            <Grid container alignItems={'center'} justifyContent={'center'}>
                <CircularProgress />
            </Grid>
        );
    }

    return (
        <Root>
            <Grid
                container
                direction={isDownLg ? 'column' : 'row'}
                spacing={isDownLg ? 0 : 2.5}
            >
                {plans$}
            </Grid>
        </Root>
    );
}
