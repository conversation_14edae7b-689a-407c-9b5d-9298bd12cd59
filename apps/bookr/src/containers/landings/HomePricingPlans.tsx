import Container from '@mui/material/Container';
import Grid from '@mui/material/Grid';
import Link from '@mui/material/Link';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import React, { useState } from 'react';
import { useI18n } from '@bookr-technologies/i18n';
import { PeriodTypeEnum } from '@bookr-technologies/sdk';
import { PeriodSwitch } from './PeriodSwitch';
import { PlanSelect } from './PlanSelect';
import { PricingHero } from './PricingHero';

const Root = styled('div')({
    backgroundColor: '#fff',
    '&, .MuiContainer-root': {
        width: '100%',
    },
    '.MuiContainer-root': {
        maxWidth: '1440px !important',
    },
});

export function HomePricingPlans() {
    const { t } = useI18n();
    const [period, setPeriod] = useState(PeriodTypeEnum.Monthly);

    return (
        <Root>
            <Container>
                <PricingHero>
                    <PeriodSwitch value={period} onChange={setPeriod} />
                    {period === PeriodTypeEnum.Monthly ? (
                        <Typography
                            variant={'body2'}
                            color={'info.main'}
                            fontWeight={600}
                            align={'center'}
                            mt={1}
                        >
                            {t('pricing.promoText')}
                        </Typography>
                    ) : null}
                </PricingHero>
                <PlanSelect period={period} withCustom landings />
                <Grid
                    container
                    alignItems={'center'}
                    justifyContent={'center'}
                    pt={8}
                    pb={5}
                >
                    <Link
                        href={`/pricing?period=${period.toLowerCase()}`}
                        color={'info.main'}
                        target="_blank"
                        rel="noopener noreferrer"
                    >
                        {t('comparePlans')}
                    </Link>
                </Grid>
            </Container>
        </Root>
    );
}
