import CheckIcon from '@mui/icons-material/Check';
import CloseIcon from '@mui/icons-material/Close';
import Typography from '@mui/material/Typography';
import React from 'react';
import { useI18n } from '@bookr-technologies/i18n';

interface Props {
    value: string | number | boolean;
}
export function FeatureValue({ value }: Props) {
    const { t } = useI18n();

    if (typeof value === 'boolean') {
        return value ? (
            <CheckIcon color={'info'} />
        ) : (
            <CloseIcon color={'error'} />
        );
    }

    return (
        <Typography
            variant={'body2'}
            fontWeight={600}
            color={'textSecondary'}
            minHeight={24}
        >
            {t(String(value) as any)}
        </Typography>
    );
}
