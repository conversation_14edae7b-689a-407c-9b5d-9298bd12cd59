import GlobalStyles from '@mui/material/GlobalStyles';
import Typography from '@mui/material/Typography';
import { styled, Theme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import { useRouter } from 'next/router';
import React, { useEffect, useMemo, useState } from 'react';
import { useI18n } from '@bookr-technologies/i18n';
import { PeriodTypeEnum } from '@bookr-technologies/sdk';
import { useGetSubscriptionPlansQuery } from '~/api';
import { DisplayContent } from '~/components/DisplayContent';
import { PeriodSwitch } from '~/containers/landings/PeriodSwitch';
import { PriceTabView } from '~/containers/landings/PriceTabView';
import { PricesView } from '~/containers/landings/PricesView';
import { getSortedPlans } from '~/lib/utilities/subscriptions';
import { PricingHero } from './PricingHero';

const globalStyles$ = (
    <GlobalStyles
        styles={{
            '.RootLayoutHeader--root.RootLayoutHeader--root': {
                position: 'relative',
            },
        }}
    />
);

export function PricingContainer() {
    const { t } = useI18n();
    const { query } = useRouter();

    const isDownLg = useMediaQuery<Theme>((theme) =>
        theme.breakpoints.down('lg'),
    );
    const [period, setPeriod] = useState(() =>
        query.period
            ? (String(query.period).toUpperCase() as PeriodTypeEnum)
            : PeriodTypeEnum.Monthly,
    );

    const plans = useGetSubscriptionPlansQuery(true);
    const sortedPlans = useMemo(
        () =>
            getSortedPlans(plans.data).filter((plan) => plan.period === period),
        [period, plans.data],
    );
    useEffect(() => {
        if (query.period) {
            setPeriod(String(query.period).toUpperCase() as PeriodTypeEnum);
        } else {
            setPeriod(PeriodTypeEnum.Monthly);
        }
    }, [query.period]);
    return (
        <Root>
            {globalStyles$}
            <PricingHero>
                <PeriodSwitch value={period} onChange={setPeriod} />

                {period === PeriodTypeEnum.Monthly ? (
                    <Typography
                        variant={'body2'}
                        color={'info.main'}
                        fontWeight={600}
                        align={'center'}
                        mt={1}
                    >
                        {t('pricing.promoText')}
                    </Typography>
                ) : null}
            </PricingHero>

            <DisplayContent>
                {!isDownLg ? (
                    <PricesView period={period} plans={sortedPlans} />
                ) : (
                    <PriceTabView period={period} plans={sortedPlans} />
                )}
            </DisplayContent>
        </Root>
    );
}

const Root = styled('div')(({ theme }) => ({
    backgroundColor: theme.palette.background.paper,
}));
