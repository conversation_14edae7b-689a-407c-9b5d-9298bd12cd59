import LoadingButton from '@mui/lab/LoadingButton';
import Avatar from '@mui/material/Avatar';
import Box from '@mui/material/Box';
import Divider from '@mui/material/Divider';
import Grid from '@mui/material/Grid';
import Paper from '@mui/material/Paper';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import { useSetAtom } from 'jotai';
import { useRouter } from 'next/router';
import { formatCurrency } from '@bookr-technologies/core';
import { useEvent } from '@bookr-technologies/hooks';
import { useI18n } from '@bookr-technologies/i18n';
import { CategoriesNameEnum, CategoryModel } from '@bookr-technologies/sdk';
import { Optional } from '@bookr-technologies/types';
import { palette } from '@bookr-technologies/ui/styles';
import { categoryComponents } from '~/components/CategoryIcon';
import { ReviewInfo } from '~/components/ReviewInfo';
import { subscriptionAppointmentsAtom } from '~/containers/subscriptions/store';
import { RouteLinks } from '~/lib/routes';

interface Props {
    serviceName: string;
    price: number;
    currency: string;
    numberOfSessions: number;
    businessName: string;
    categories: CategoryModel[];
    businessImage: Optional<string>;
    noOfReviews: Optional<number>;
    averageRating: Optional<number>;
    createdAppointments: number;
    allAppointmentsHaveBeenCreated: boolean;
    subscriptionId: string;
}

export function SubscriptionServiceCard({
    serviceName,
    businessName,
    categories,
    businessImage,
    noOfReviews,
    averageRating,
    price,
    currency,
    numberOfSessions,
    createdAppointments,
    allAppointmentsHaveBeenCreated,
    subscriptionId,
}: Props) {
    const { t, locale } = useI18n();
    const router = useRouter();
    const setSubscriptionAppointments = useSetAtom(
        subscriptionAppointmentsAtom,
    );
    const firstCategory = categories?.[0];
    const CategoryIcon =
        categoryComponents[firstCategory?.name] ??
        categoryComponents[CategoriesNameEnum.OTHER];

    const handleFinish = useEvent(() => {
        // noinspection JSIgnoredPromiseFromCall
        router.push(RouteLinks.appointments()).then(() => {
            if (subscriptionId && subscriptionId !== 'new') {
                setSubscriptionAppointments((prev) => {
                    prev[subscriptionId] = null as any;
                    delete prev[subscriptionId];

                    return { ...prev };
                });
            }
        });
    });

    return (
        <Stack gap={2}>
            <StyledPaper>
                <Stack gap={3}>
                    <Grid container gap={2}>
                        <Box
                            width={{
                                xs: '100%',
                                sm: '40%',
                            }}
                            maxWidth={{
                                xs: '100%',
                                sm: 150,
                            }}
                        >
                            <StyledAvatar
                                variant={'rounded'}
                                {...(businessImage
                                    ? { src: businessImage }
                                    : {})}
                            >
                                <CategoryIcon width={64} height={64} />
                            </StyledAvatar>
                        </Box>
                        <Stack justifyContent={'space-between'}>
                            <Stack>
                                <Typography
                                    variant={'footnote'}
                                    fontWeight={600}
                                    color={'textSecondary'}
                                >
                                    {serviceName}
                                </Typography>
                                <Typography
                                    variant={'callout'}
                                    fontWeight={800}
                                >
                                    {businessName}
                                </Typography>
                            </Stack>

                            {noOfReviews &&
                            averageRating &&
                            noOfReviews > 0 &&
                            averageRating > 0 ? (
                                <ReviewInfo
                                    noOfReviews={noOfReviews}
                                    averageRating={averageRating}
                                />
                            ) : null}
                        </Stack>
                    </Grid>

                    <Typography variant={'title3'} fontWeight={800}>
                        {t('subscriptionDetails')}
                    </Typography>

                    <Grid
                        container
                        alignItems={'center'}
                        justifyContent={'space-between'}
                        gap={1}
                    >
                        <Typography variant={'subhead'} fontWeight={600}>
                            {t('numberOfAppointments')}:
                        </Typography>

                        <Typography
                            variant={'subhead'}
                            fontWeight={600}
                            color={'textSecondary'}
                        >
                            {t('outOf', {
                                current: createdAppointments,
                                total: numberOfSessions,
                            })}
                        </Typography>
                    </Grid>

                    <Grid
                        container
                        alignItems={'center'}
                        justifyContent={'space-between'}
                        gap={1}
                    >
                        <Typography variant={'subhead'} fontWeight={600}>
                            {t('pricePerAppointment')}:
                        </Typography>

                        <Typography
                            variant={'subhead'}
                            fontWeight={600}
                            color={'textSecondary'}
                        >
                            {formatCurrency(price, currency, locale)}
                        </Typography>
                    </Grid>

                    <Divider />

                    <Grid
                        container
                        alignItems={'center'}
                        justifyContent={'space-between'}
                        gap={1}
                    >
                        <Typography variant={'subhead'} fontWeight={600}>
                            {t('totalPrice')}:
                        </Typography>

                        <Typography
                            variant={'subhead'}
                            fontWeight={600}
                            color={'textSecondary'}
                        >
                            {formatCurrency(
                                createdAppointments * price,
                                currency,
                                locale,
                            )}
                        </Typography>
                    </Grid>
                </Stack>
            </StyledPaper>
            <LoadingButton
                variant={'contained'}
                size={'large'}
                disabled={!allAppointmentsHaveBeenCreated}
                onClick={handleFinish}
            >
                {t('finishSubscription')}
            </LoadingButton>
        </Stack>
    );
}

const StyledPaper = styled(Paper)(({ theme }) => ({
    backgroundColor: palette.extended.backgroundSecondary,
    padding: theme.spacing(3),
}));

const StyledAvatar = styled(Avatar)(() => ({
    width: '100%',
    paddingBottom: '82%',
    borderRadius: 8,
    '.CategoryIcon-root, .MuiAvatar-img': {
        position: 'absolute',
        top: '50%',
        left: '50%',
        transform: 'translate(-50%, -50%)',
    },
}));
