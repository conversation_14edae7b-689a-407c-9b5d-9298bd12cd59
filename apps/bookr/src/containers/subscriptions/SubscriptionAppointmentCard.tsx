import CloseIcon from '@mui/icons-material/Close';
import CircularProgress from '@mui/material/CircularProgress';
import Grid from '@mui/material/Grid';
import IconButton from '@mui/material/IconButton';
import Paper from '@mui/material/Paper';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import addMinutes from 'date-fns/addMinutes';
import parseISO from 'date-fns/parseISO';
import { useAtom } from 'jotai';
import { useEvent, useNotification } from '@bookr-technologies/hooks';
import { useFormatter, useI18n } from '@bookr-technologies/i18n';
import { AppointmentModel } from '@bookr-technologies/sdk';
import { clsx } from '@bookr-technologies/ui';
import { palette } from '@bookr-technologies/ui/styles';
import { useCancelAppointmentMutation } from '~/api';
import { subscriptionAppointmentsAtom } from '~/containers/subscriptions/store';

interface Props {
    subscriptionId: string;
    appointmentId: number;
    dateTime: string;
    duration: number;
}

const Root = styled(Paper)(({ theme }) => ({
    padding: theme.spacing(2, 2, 2, 3),
    backgroundColor: palette.extended.backgroundSecondary,
    '&.loading': {
        opacity: 0.5,
        pointerEvents: 'none',
    },
}));

export function SubscriptionAppointmentCard({
    subscriptionId,
    appointmentId,
    dateTime,
    duration,
}: Props) {
    const { t } = useI18n();
    const formatter = useFormatter();
    const [subscriptionAppointments, setSubscriptionAppointments] = useAtom(
        subscriptionAppointmentsAtom,
    );
    const cancelAppointmentMutation = useCancelAppointmentMutation();
    const notifications = useNotification();

    const parsedDate = parseISO(dateTime);
    const date = formatter.datetime(parsedDate, 'PP');
    const from = formatter.datetime(parsedDate, 'p');
    const to = formatter.datetime(addMinutes(parsedDate, duration), 'p');

    const handleCancelAppointment = useEvent(() => {
        cancelAppointmentMutation.mutate(appointmentId, {
            onSuccess: () => {
                const subscription =
                    subscriptionAppointments.data?.[subscriptionId];
                if (!subscription) {
                    return;
                }

                setSubscriptionAppointments((prev) => {
                    const subscription = prev[subscriptionId];
                    if (!subscription) {
                        return prev;
                    }

                    subscription.appointments =
                        subscription.appointments.filter(
                            (appointment: AppointmentModel) =>
                                appointment.id !== appointmentId,
                        );

                    return {
                        ...prev,
                        [subscriptionId]: { ...subscription },
                    };
                });

                notifications.success(t('appointmentCanceledSuccessfully'));
            },
            onError: () => {
                notifications.error(
                    t('somethingWentWrongDuringAppointmentCanceling'),
                );
            },
        });
    });

    return (
        <Root
            className={clsx({ loading: cancelAppointmentMutation.isLoading })}
        >
            <Grid
                container
                gap={2}
                alignItems={'center'}
                justifyContent={'space-between'}
                flexWrap={'nowrap'}
            >
                <Stack flexGrow={1}>
                    <Typography variant={'body'} fontWeight={600} mb={1}>
                        {date}
                    </Typography>
                    <Typography
                        variant={'subhead'}
                        fontWeight={600}
                        color={'textSecondary'}
                    >
                        {from} - {to}
                    </Typography>
                </Stack>

                <IconButton onClick={handleCancelAppointment}>
                    {cancelAppointmentMutation.isLoading ? (
                        <CircularProgress size={24} />
                    ) : (
                        <CloseIcon />
                    )}
                </IconButton>
            </Grid>
        </Root>
    );
}
