import Dialog from '@mui/material/Dialog';
import { useDialogState } from '@bookr-technologies/hooks';
import { BookServiceVariantProps } from '~/components/BookNowButton/types';
import { BookServiceVariant } from '~/components/BookNowButton/variants/BookServiceVariant';

export const BookSubscriptionAppointmentDialogName =
    'BookSubscriptionAppointmentDialog';

export function BookSubscriptionAppointmentDialog({
    ...rest
}: BookServiceVariantProps) {
    const dialog = useDialogState(BookSubscriptionAppointmentDialogName, {
        responsiveFullscreen: true,
    });

    return (
        <Dialog fullWidth maxWidth={'sm'} {...dialog.props}>
            <BookServiceVariant {...rest} />
        </Dialog>
    );
}
