import * as uuid from 'uuid';
import { persistentAtom } from '@bookr-technologies/core';
import { AppointmentModel } from '@bookr-technologies/sdk';

export interface SubscriptionAppointments {
    id: string;
    serviceId: number;
    appointments: AppointmentModel[];
}

export const subscriptionAppointmentsAtom = persistentAtom<
    Record<string, SubscriptionAppointments>
>('subscriptions', {});

export function createSubscriptionAppointment(serviceId: number) {
    const id = uuid.v4();
    return {
        id,
        serviceId,
        appointments: [],
    };
}
