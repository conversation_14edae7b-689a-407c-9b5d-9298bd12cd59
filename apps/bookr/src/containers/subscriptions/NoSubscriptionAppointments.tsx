import AddIcon from '@mui/icons-material/Add';
import Button from '@mui/material/Button';
import { useDialogState } from '@bookr-technologies/hooks';
import { useI18n } from '@bookr-technologies/i18n';
import { palette } from '@bookr-technologies/ui/styles';
import { NoDataBanner } from '~/components/NoDataBanner';
import { BookSubscriptionAppointmentDialogName } from './BookSubscriptionAppointmentDialog';
import CalendarAsset from './assets/calendar.png';

export function NoSubscriptionAppointments() {
    const { t } = useI18n();
    const dialog = useDialogState(BookSubscriptionAppointmentDialogName);

    return (
        <NoDataBanner
            title={t('addFirstAppointmentOfSubscription')}
            src={CalendarAsset}
            alt={'calendar'}
            titleProps={{ variant: 'title3' }}
            sx={{
                bgcolor: palette.extended.backgroundSecondary,
                minHeight: 0,
                '.NoDataBanner-content': { padding: '20px 28px' },
                '.NoDataBanner-image': { minWidth: 320 },
            }}
        >
            <Button
                variant={'contained'}
                color={'info'}
                startIcon={<AddIcon />}
                onClick={dialog.open}
            >
                {t('addAppointment')}
            </Button>
        </NoDataBanner>
    );
}
