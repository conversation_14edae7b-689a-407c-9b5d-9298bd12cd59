import AddIcon from '@mui/icons-material/Add';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Chip from '@mui/material/Chip';
import Container from '@mui/material/Container';
import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import { useAtom } from 'jotai';
import { useRouter } from 'next/router';
import { useEffect } from 'react';
import { num, str } from '@bookr-technologies/core';
import {
    useDebouncedEvent,
    useDialogState,
    useEvent,
    useValueRef,
} from '@bookr-technologies/hooks';
import { useI18n } from '@bookr-technologies/i18n';
import {
    useBusinessQuery,
    useCancelAppointmentMutation,
    useGetServiceQuery,
} from '~/api';
import { BackgroundColor } from '~/components/BackgroundColor';
import { DisplayContent } from '~/components/DisplayContent';
import { RouteLinks } from '~/lib/routes';
import {
    BookSubscriptionAppointmentDialog,
    BookSubscriptionAppointmentDialogName,
} from './BookSubscriptionAppointmentDialog';
import { NoSubscriptionAppointments } from './NoSubscriptionAppointments';
import { SubscriptionAppointmentCard } from './SubscriptionAppointmentCard';
import { SubscriptionServiceCard } from './SubscriptionServiceCard';
import {
    createSubscriptionAppointment,
    SubscriptionAppointments,
    subscriptionAppointmentsAtom,
} from './store';

interface Props {
    serviceId: number;
    subscriptionId?: 'new' | string;
    businessId: string;
}

const StyledTitle = styled(Typography)(({ theme }) => ({
    [theme.breakpoints.down('sm')]: {
        fontSize: 20,
    },
}));

const getSubscription = (
    subscriptions: Record<string, SubscriptionAppointments>,
    serviceId: string | undefined,
) => (subscriptions && serviceId ? subscriptions[serviceId] : null);

export function NewSubscriptionContainer({
    serviceId,
    subscriptionId,
    businessId,
}: Props) {
    const { t } = useI18n();
    const router = useRouter();
    const bookSubscriptionAppointmentDialog = useDialogState(
        BookSubscriptionAppointmentDialogName,
    );

    const [subscriptionAppointments, setSubscriptionAppointments] = useAtom(
        subscriptionAppointmentsAtom,
    );
    const { data: service } = useGetServiceQuery({ serviceId });
    const { data: business } = useBusinessQuery(businessId);

    const staff = business?.staffMembers?.find((st) =>
        st.services.some((s) => s.id === serviceId),
    );

    const cancelAppointment = useCancelAppointmentMutation();

    const subscription = getSubscription(
        subscriptionAppointments.data,
        subscriptionId,
    );

    const createdAppointments = subscription?.appointments?.length ?? 0;
    const hasAppointments = createdAppointments > 0;
    const noSubscription = !subscriptionAppointments.loading && !subscription;
    const allAppointmentsHaveBeenCreated =
        createdAppointments === service?.numberOfSessions;
    const handleAppointmentSubmit = useEvent((appointment) => {
        if (subscription) {
            subscription.appointments?.push(appointment);
            setSubscriptionAppointments((prev) => ({
                ...prev,
                [subscription.id]: subscription,
            }));
            bookSubscriptionAppointmentDialog.close();
        }
    });

    const createSubscription = useDebouncedEvent(() => {
        const newSubscription = createSubscriptionAppointment(serviceId);
        setSubscriptionAppointments({
            ...subscriptionAppointments.data,
            [newSubscription.id]: newSubscription,
        });

        // noinspection JSIgnoredPromiseFromCall
        router.replace(
            RouteLinks.subscriptionPage(
                newSubscription.id,
                serviceId,
                businessId,
            ),
        );
    }, 100);

    useEffect(() => {
        if (noSubscription) {
            createSubscription();
        }
    }, [createSubscription, noSubscription]);

    const subscriptionAppointmentRef = useValueRef(
        subscription?.appointments ?? [],
    );

    useEffect(() => {
        router.beforePopState(({ as }) => {
            const currentPath = router.asPath;
            const appointments = subscriptionAppointmentRef.current ?? [];

            if (appointments.length === 0) {
                return true;
            }

            if (as !== currentPath) {
                if (confirm(t('subscriptionLeavingConfirmationMessage'))) {
                    appointments.forEach(({ id }) =>
                        cancelAppointment.mutate(id),
                    );

                    return true;
                } else {
                    window.history.pushState(null, '', currentPath);
                    return false;
                }
            }

            return true;
        });

        return () => {
            router.beforePopState(() => true);
        };
    }, [cancelAppointment, router, subscriptionAppointmentRef, t]);

    return (
        <Container>
            <BackgroundColor color={'#fff'} />
            <Grid
                container
                py={{
                    xs: 3,
                    lg: 6,
                }}
                gap={{
                    xs: 3,
                    lg: 10,
                }}
                flexWrap={'nowrap'}
                flexDirection={{
                    xs: 'column',
                    lg: 'row',
                }}
            >
                <Grid item flexGrow={1} flexDirection={'column'}>
                    <Grid
                        container
                        alignItems={'center'}
                        justifyContent={'space-between'}
                    >
                        <StyledTitle variant={'largeTitle'} fontWeight={800}>
                            {t('createASubscription')}
                        </StyledTitle>

                        <Chip
                            variant={'filled'}
                            color={'info'}
                            size={'small'}
                            label={t('outOf', {
                                current: createdAppointments,
                                total: service?.numberOfSessions,
                            })}
                        />
                    </Grid>
                    <Stack mt={2}>
                        <DisplayContent
                            noResults={!hasAppointments}
                            slots={{
                                NoResults: NoSubscriptionAppointments,
                            }}
                        >
                            <Stack gap={2} mb={2}>
                                {subscription?.appointments?.map(
                                    (appointment) => (
                                        <SubscriptionAppointmentCard
                                            key={appointment.id}
                                            dateTime={appointment.dateTime}
                                            duration={
                                                appointment.service.duration
                                            }
                                            appointmentId={appointment.id}
                                            subscriptionId={
                                                subscriptionId ?? ''
                                            }
                                        />
                                    ),
                                )}
                            </Stack>
                            {!allAppointmentsHaveBeenCreated ? (
                                <Grid container justifyContent={'flex-end'}>
                                    <Button
                                        variant={'contained'}
                                        color={'info'}
                                        startIcon={<AddIcon />}
                                        onClick={
                                            bookSubscriptionAppointmentDialog.open
                                        }
                                    >
                                        {t('addAppointment')}
                                    </Button>
                                </Grid>
                            ) : null}
                        </DisplayContent>
                    </Stack>
                </Grid>

                <Box
                    width={'100%'}
                    maxWidth={{
                        lg: 420,
                    }}
                >
                    <SubscriptionServiceCard
                        serviceName={str(service?.name)}
                        price={num(service?.pricePerSession)}
                        currency={str(service?.currency, 'RON')}
                        numberOfSessions={num(service?.numberOfSessions)}
                        averageRating={num(business?.reviewInfo?.averageRating)}
                        noOfReviews={num(business?.reviewInfo?.noOfReviews)}
                        categories={business?.categories ?? []}
                        businessImage={str(business?.profilePicture)}
                        businessName={str(business?.name)}
                        createdAppointments={createdAppointments}
                        allAppointmentsHaveBeenCreated={
                            allAppointmentsHaveBeenCreated
                        }
                        subscriptionId={subscriptionId ?? ''}
                    />
                </Box>
            </Grid>

            {service && staff ? (
                <BookSubscriptionAppointmentDialog
                    disableBack
                    businessName={str(business?.name)}
                    service={service}
                    staffMember={staff}
                    onSubmit={handleAppointmentSubmit}
                />
            ) : null}
        </Container>
    );
}

export default NewSubscriptionContainer;
