import { Settings2Icon } from 'lucide-react';
import { useDialog } from '@bookr-technologies/hooks';
import { useI18n } from '@bookr-technologies/i18n';
import { Button } from '~/components/ui/button';
import { Typography } from '~/components/ui/typography';
import {
    SearchContainerFilters,
    SearchContainerFiltersProps,
} from './SearchContainerFilters';

export function SearchContainerHeader({
    totals,
    onFilters,
}: {
    totals: number;
    onFilters: SearchContainerFiltersProps['onSubmit'];
}) {
    const { props, open } = useDialog();
    const { t } = useI18n();

    return (
        <header
            className={
                'flex items-center justify-between gap-2 sticky top-[var(--header-height)] lg:top-0 z-20 bg-bg-primary py-2 px-4 lg:py-5 lg:px-8'
            }
        >
            <div className={'flex flex-col gap-1'}>
                <Typography variant={'callout'} weight={'semi'}>
                    {t('results')}
                </Typography>
                <Typography
                    variant={'callout'}
                    weight={'semi'}
                    color={'tertiary'}
                >
                    {t('searchResults', { totals })}
                </Typography>
            </div>
            <div className={'flex justify-end'}>
                <Button variant={'tertiary'} size="medium" onClick={open}>
                    <Settings2Icon />
                    <span>{t('filters')}</span>
                </Button>

                <SearchContainerFilters onSubmit={onFilters} {...props} />
            </div>
        </header>
    );
}
