import CircularProgress from '@mui/material/CircularProgress';
import { useRouter } from 'next/router';
import { useEffect, useMemo, useState } from 'react';
import { castArray, num } from '@bookr-technologies/core';
import { useDebouncedEvent } from '@bookr-technologies/hooks';
import { collectPages, useBusinessSearchQuery } from '~/api';
import { RouteLinks } from '~/lib/routes';
import { getSearchArguments } from '~/lib/utilities/search';
import { cn } from '~/lib/utils';
import {
    SearchContainerFiltersProps,
    SearchFilters,
} from './SearchContainerFilters';
import { SearchContainerHeader } from './SearchContainerHeader';
import { MapOptions, SearchContainerMap } from './SearchContainerMap';
import { SearchContainerResults } from './SearchContainerResults';

export function SearchContainer() {
    const router = useRouter();
    const searchArgs = useMemo(
        () =>
            getSearchArguments(
                castArray(router.query?.args ?? []),
                router.query,
            ),
        [router.query],
    );

    const [latLng, setLatLng] = useState(searchArgs.latLng);
    const [radius, setRadius] = useState(searchArgs.radius);
    const [filters, setFilters] = useState<SearchFilters | null>({
        sort: (searchArgs.sort || 'recommended') as SearchFilters['sort'],
        businessType: searchArgs.instantBooking ? 'instant' : 'standard',
        priceRange:
            searchArgs.minPrice || searchArgs.maxPrice
                ? [searchArgs.minPrice || null, searchArgs.maxPrice || null]
                : [null, null],
    });
    const [mapOptions, setMapOptions] = useState<Partial<MapOptions>>({});
    const searchQuery = useBusinessSearchQuery(
        {
            ...searchArgs,
            radius,
            latLng,
            instantBooking: filters?.businessType === 'instant',
            ...(filters?.sort ? { sort: filters?.sort } : {}),
            ...(filters?.priceRange?.[0]
                ? { minPrice: filters?.priceRange?.[0] }
                : {}),
            ...(filters?.priceRange?.[1]
                ? { maxPrice: filters?.priceRange?.[1] }
                : {}),
        },
        {
            enabled: !!(radius && latLng),
        },
    );

    const results = collectPages(searchQuery.data, 'hits');
    const totals = results.nbHits;
    const { hits = [] } = results || {};

    const navigate = useDebouncedEvent(() => {
        const pathname = RouteLinks.search({
            ...searchArgs,
            latLng: latLng || searchArgs.latLng,
            radius: num(radius),
            instantBooking: filters?.businessType === 'instant',
            minPrice: num(filters?.priceRange?.[0]),
            maxPrice: num(filters?.priceRange?.[1]),
            sort: filters?.sort,
        });

        if (router.asPath !== pathname) {
            router.replace(pathname);
        }
    }, 300);

    const handleCenter = useDebouncedEvent(
        ({
            latitude,
            longitude,
            radius,
        }: {
            latitude: number;
            longitude: number;
            radius: number;
        }) => {
            setRadius(String(Math.round(radius)));
            setLatLng(`${latitude},${longitude}`);
            navigate();
        },
        300,
    );

    const handleMore = () => searchQuery.fetchNextPage();

    const handleFilters: SearchContainerFiltersProps['onSubmit'] = (
        filters,
    ) => {
        setFilters(filters);
        navigate();
    };

    useEffect(() => setLatLng(searchArgs.latLng), [searchArgs.latLng]);
    useEffect(() => setRadius(searchArgs.radius), [searchArgs.radius]);

    return (
        <div
            className={
                'flex flex-col-reverse lg:flex-row grow relative lg:h-[calc(100vh-82px)] bg-bg-primary'
            }
        >
            <aside
                className={cn(
                    'flex flex-col grow w-full lg:w-1/2 gap-4 lg:overflow-y-scroll max-h-full',
                    mapOptions.minimized
                        ? 'lg:max-w-[400px]'
                        : 'lg:max-w-[800px]',
                )}
            >
                <SearchContainerHeader
                    totals={totals}
                    onFilters={handleFilters}
                />
                {searchQuery.isLoading ? (
                    <div className={'flex grow justify-center'}>
                        <CircularProgress size={32} />
                    </div>
                ) : searchQuery.error ? (
                    <div></div>
                ) : (
                    <SearchContainerResults
                        hits={hits}
                        hasMore={!!searchQuery.hasNextPage}
                        loading={searchQuery.isFetchingNextPage}
                        onMore={handleMore}
                        className={cn(
                            'grid gap-x-8 gap-y-10 pb-6 px-4 lg:px-8 grid-cols-1',
                            !mapOptions.minimized &&
                                'sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-2',
                        )}
                    />
                )}
            </aside>
            <aside className={'flex grow w-full lg:w-1/2 min-h-[64vh]'}>
                <SearchContainerMap
                    hits={hits}
                    latLng={latLng}
                    onCenter={handleCenter}
                    options={mapOptions}
                    onOptions={setMapOptions}
                    ready={router.isReady}
                />
            </aside>
        </div>
    );
}
