import CloseIcon from '@mui/icons-material/Close';
import EventAvailableIcon from '@mui/icons-material/EventAvailable';
import Button from '@mui/material/Button';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import {
    AdvancedMarker,
    AdvancedMarkerProps,
    APIProvider,
    InfoWindow,
    Map,
    MapCameraChangedEvent,
    useAdvancedMarkerRef,
    useMap,
} from '@vis.gl/react-google-maps';
import {
    ChevronLeftIcon,
    ChevronRightIcon,
    MinusIcon,
    NavigationIcon,
    PlusIcon,
    StarIcon,
} from 'lucide-react';
import Image from 'next/image';
import { RefObject, useEffect, useMemo, useRef, useState } from 'react';
import { getClientConfig } from '@bookr-technologies/config';
import { useLocation } from '@bookr-technologies/hooks';
import { useI18n } from '@bookr-technologies/i18n';
import {
    CategoriesNameEnum,
    SearchBusinessModel,
} from '@bookr-technologies/sdk';
import { StyledBadge } from '~/components/BusinessCard/styles';
import { categoryComponents } from '~/components/CategoryIcon';
import { cn } from '~/lib/utils';

const { googleMaps } = getClientConfig();

// Default location is set to Cluj-Napoca, Cluj, Romania
const defaultLocation = {
    latitude: 46.7712,
    longitude: 23.6036,
};

const calculateRadius = (
    zoom: number,
    lat: number,
    rootRef: RefObject<HTMLDivElement | null>,
) => {
    const pixels = Math.max(
        rootRef.current?.clientWidth || 100,
        rootRef.current?.clientHeight || 100,
    );

    return (
        ((156543.03392 * Math.cos((lat * Math.PI) / 180)) / Math.pow(2, zoom)) *
        pixels
    );
};

export const AdvancedMarkerWithRef = (
    props: Omit<AdvancedMarkerProps, 'onClick'> & {
        onClick(marker: google.maps.marker.AdvancedMarkerElement): void;
    },
) => {
    const { children, onClick, ...advancedMarkerProps } = props;
    const [markerRef, marker] = useAdvancedMarkerRef();

    const handleClick = () => {
        if (marker) {
            onClick(marker);
        }
    };

    return (
        <AdvancedMarker
            onClick={handleClick}
            ref={markerRef}
            {...advancedMarkerProps}
        >
            {children}
        </AdvancedMarker>
    );
};

function MapPin({
    hit,
    selected,
    onClick,
}: {
    hit: SearchBusinessModel;
    selected: boolean;
    onClick(
        hit: SearchBusinessModel,
        marker: google.maps.marker.AdvancedMarkerElement,
    ): void;
}) {
    const hasRating = hit.averageRating > 0;

    function handleClick(marker: google.maps.marker.AdvancedMarkerElement) {
        onClick(hit, marker);
    }

    return (
        <AdvancedMarkerWithRef
            onClick={handleClick}
            position={hit._geoloc}
            zIndex={selected ? 20 : hasRating ? 10 : 1}
            className={cn(
                'bg-black text-[0] px-2 rounded-full z-10',
                selected && 'bg-white border-[3px] border-black',
                !hasRating && 'w-6',
                selected && !hasRating && 'w-8 h-8',
            )}
        >
            <div
                className={cn(
                    'inline-flex justify-center items-center gap-1 h-6',
                    selected && 'h-8',
                )}
            >
                {hasRating ? (
                    <>
                        <StarIcon
                            size={12}
                            className={cn(
                                'text-content-inverse mt-[-1px]',
                                selected && 'text-primary',
                            )}
                        />
                        <Typography
                            variant={'caption1'}
                            className={selected ? 'text-primary' : 'text-white'}
                            fontWeight={700}
                        >
                            {hit.averageRating.toFixed(1)}
                        </Typography>
                    </>
                ) : (
                    <div className={'w-2 h-2 rounded-full bg-white'} />
                )}
            </div>
            <div
                className={
                    'bg-black z-0 rounded-b-full absolute w-1 h-[10px] left-1/2 bottom-0 -translate-x-1/2 translate-y-full'
                }
            />
        </AdvancedMarkerWithRef>
    );
}

export type MapOptions = {
    minimized: boolean;
};

export function SearchContainerMap({
    onCenter,
    onOptions,
    hits,
    options,
    latLng,
    ready,
}: {
    hits: SearchBusinessModel[];
    options: Partial<MapOptions>;
    onOptions: (options: Partial<MapOptions>) => void;
    onCenter: (center: {
        latitude: number;
        longitude: number;
        zoom: number;
        radius: number;
    }) => void;
    latLng?: string | undefined;
    ready: boolean;
}) {
    const rootRef = useRef<HTMLDivElement>(null);
    const { t } = useI18n();

    const userLocation = useLocation({});
    const latLngLocation = useMemo(
        () =>
            latLng
                ? {
                      latitude: Number(latLng.split(',')[0]),
                      longitude: Number(latLng.split(',')[1]),
                  }
                : null,
        [latLng],
    );

    const location = latLngLocation || userLocation || defaultLocation;

    const [state, setState] = useState<{
        latitude: number;
        longitude: number;
        zoom: number;
        radius: number;
    }>({
        ...(location || {}),
        zoom: 14,
        radius: calculateRadius(14, location.latitude, rootRef),
    });

    const [errorImages, setErrorImages] = useState<Record<string, boolean>>({});
    const [selectedMarker, setSelectedMarker] =
        useState<google.maps.marker.AdvancedMarkerElement | null>(null);
    const [selectedBusiness, setSelectedBusiness] =
        useState<SearchBusinessModel | null>(null);

    function updateState(detail: MapCameraChangedEvent['detail']) {
        const longitude = detail.center.lng;
        const latitude = detail.center.lat;
        const zoom = detail.zoom;
        const radius = calculateRadius(zoom, latitude, rootRef);

        setState((prevState) => ({
            ...prevState,
            longitude,
            latitude,
            zoom,
            radius,
        }));

        onCenter({
            longitude: Number(longitude.toFixed(3)),
            latitude: Number(latitude.toFixed(3)),
            zoom,
            radius,
        });
    }

    function handleCloseInfoWindow() {
        setSelectedBusiness(null);
        setSelectedMarker(null);
    }

    function handleSelect(
        hit: SearchBusinessModel,
        marker: google.maps.marker.AdvancedMarkerElement,
    ) {
        setSelectedBusiness(hit);
        setSelectedMarker(marker);
    }

    const CategoryComponent =
        categoryComponents[
            (selectedBusiness?.categories?.[0] as CategoriesNameEnum) ||
                CategoriesNameEnum.OTHER
        ] || categoryComponents[CategoriesNameEnum.OTHER];

    const mapCenter = useMemo(
        () => ({
            lat:
                latLngLocation?.latitude ||
                location?.latitude ||
                defaultLocation.latitude,
            lng:
                latLngLocation?.longitude ||
                location?.longitude ||
                defaultLocation.longitude,
        }),
        [
            latLngLocation?.latitude,
            latLngLocation?.longitude,
            location?.latitude,
            location?.longitude,
        ],
    );

    useEffect(() => {
        if (latLngLocation) {
            setState((prevState) => ({
                ...prevState,
                latitude: latLngLocation.latitude,
                longitude: latLngLocation.longitude,
                radius: calculateRadius(
                    state.zoom,
                    latLngLocation.latitude,
                    rootRef,
                ),
            }));
        }
    }, [latLngLocation, state.zoom]);

    return (
        <div className={'flex grow bg-gray-200 relative'} ref={rootRef}>
            <APIProvider apiKey={googleMaps.apiKey}>
                {ready ? (
                    <Map
                        defaultCenter={mapCenter}
                        mapId={'a7d3af1668a8d88d'}
                        center={
                            state
                                ? {
                                      lat: state.latitude,
                                      lng: state.longitude,
                                  }
                                : undefined
                        }
                        className={'grow'}
                        zoom={state.zoom}
                        gestureHandling={'greedy'}
                        disableDefaultUI={true}
                        onBoundsChanged={(event) => updateState(event.detail)}
                        onZoomChanged={(event) => updateState(event.detail)}
                        onCenterChanged={(event) => updateState(event.detail)}
                    >
                        {hits.map((hit) => (
                            <MapPin
                                key={hit.id}
                                hit={hit}
                                selected={hit.id === selectedBusiness?.id}
                                onClick={handleSelect}
                            />
                        ))}

                        {selectedMarker && selectedBusiness ? (
                            <InfoWindow
                                anchor={selectedMarker}
                                pixelOffset={[0, -2]}
                                onCloseClick={handleCloseInfoWindow}
                                className={
                                    'bg-white w-80 min-h-[260px] relative'
                                }
                                minWidth={320}
                                headerDisabled
                            >
                                <header className={'w-full bg-gray-200'}>
                                    <div
                                        className={
                                            'absolute top-0 left-0 right-0 flex justify-between items-center p-2 z-20'
                                        }
                                    >
                                        {('services' in selectedBusiness &&
                                            !!selectedBusiness.services
                                                ?.length) ||
                                        selectedBusiness.hasServices ? (
                                            <StyledBadge
                                                sx={{ position: 'relative' }}
                                            >
                                                <EventAvailableIcon
                                                    fontSize={'small'}
                                                />
                                                <Typography
                                                    variant={'caption1'}
                                                    fontWeight={700}
                                                    color={'inherit'}
                                                >
                                                    {t('instantBooking')}
                                                </Typography>
                                            </StyledBadge>
                                        ) : (
                                            <div />
                                        )}
                                        <StyledBadge
                                            as={'button'}
                                            sx={{ position: 'relative' }}
                                            onClick={handleCloseInfoWindow}
                                            className={'p-1 rounded-full'}
                                        >
                                            <CloseIcon
                                                className={'text-white'}
                                            />
                                        </StyledBadge>
                                    </div>
                                    {selectedBusiness.photos?.[0] &&
                                    selectedBusiness.profilePicture ? (
                                        <Image
                                            className={cn(
                                                'w-full h-40 object-cover z-10',
                                                errorImages[
                                                    selectedBusiness.id
                                                ] && 'opacity-0',
                                            )}
                                            width={320}
                                            height={160}
                                            alt={selectedBusiness.name}
                                            src={
                                                selectedBusiness.photos?.[0] &&
                                                selectedBusiness.profilePicture
                                            }
                                            onError={() =>
                                                setErrorImages((state) => ({
                                                    ...state,
                                                    [selectedBusiness?.id]:
                                                        true,
                                                }))
                                            }
                                        />
                                    ) : (
                                        <div
                                            className={
                                                'w-full h-40 flex items-center justify-center'
                                            }
                                        >
                                            <CategoryComponent
                                                width={64}
                                                height={64}
                                            />
                                        </div>
                                    )}
                                </header>
                                <main className={'p-3 flex flex-col gap-4'}>
                                    <div
                                        className={
                                            'flex items-start justify-between'
                                        }
                                    >
                                        <Typography
                                            variant={'subhead'}
                                            fontWeight={700}
                                        >
                                            {selectedBusiness.name}
                                        </Typography>

                                        {selectedBusiness.averageRating > 0 ? (
                                            <Typography
                                                variant={'footnote'}
                                                fontWeight={600}
                                                className={
                                                    'inline-flex items-center gap-1'
                                                }
                                            >
                                                <StarIcon
                                                    size={16}
                                                    className={'text-primary'}
                                                />
                                                {selectedBusiness.averageRating.toFixed(
                                                    1,
                                                )}
                                            </Typography>
                                        ) : null}
                                    </div>

                                    <div className={'flex flex-col gap-1'}>
                                        {selectedBusiness.categories[0] ? (
                                            <Typography
                                                variant={'footnote'}
                                                fontWeight={600}
                                            >
                                                {t(
                                                    `categories.${selectedBusiness.categories[0].toLowerCase()}.name` as any,
                                                )}
                                            </Typography>
                                        ) : null}
                                        <Typography
                                            variant={'caption1'}
                                            fontWeight={600}
                                            color={'textSecondary'}
                                        >
                                            {selectedBusiness.formattedAddress}
                                        </Typography>
                                    </div>
                                </main>
                            </InfoWindow>
                        ) : null}
                    </Map>
                ) : null}
                <SearchContainerControls
                    defaultLocation={location}
                    options={options}
                    onOptions={onOptions}
                />
            </APIProvider>
        </div>
    );
}

function SearchContainerControls({
    defaultLocation,
    options,
    onOptions,
}: {
    defaultLocation: {
        latitude: number;
        longitude: number;
    };
    options: Partial<MapOptions>;
    onOptions: (options: Partial<MapOptions>) => void;
}) {
    const { t } = useI18n();
    const map = useMap();

    return (
        <>
            <div
                className={
                    'hidden lg:flex flex-col absolute top-4 left-4 lg:top-8 lg:left-8 z-10 bg-bg-primary rounded-md shadow-[0_11px_20px_rgba(0,0,0,0.1)] border border-bg-secondary'
                }
            >
                {options.minimized ? (
                    <Button
                        className={
                            'min-h-6 min-w-6 lg:min-h-12 lg:min-w-12 rounded-none'
                        }
                        onClick={() =>
                            onOptions({
                                ...options,
                                minimized: false,
                            })
                        }
                    >
                        <ChevronRightIcon />
                        <span>{t('maximizeList')}</span>
                    </Button>
                ) : (
                    <IconButton
                        className={
                            'min-h-6 min-w-6 lg:min-h-12 lg:min-w-12 rounded-none'
                        }
                        onClick={() =>
                            onOptions({
                                ...options,
                                minimized: true,
                            })
                        }
                    >
                        <ChevronLeftIcon />
                    </IconButton>
                )}
            </div>

            <div
                className={
                    'flex flex-col absolute top-4 right-4 lg:top-8 lg:right-8 z-10 gap-6'
                }
            >
                <div
                    className={
                        'flex flex-col bg-bg-primary rounded-md shadow-[0_11px_20px_rgba(0,0,0,0.1)] border border-bg-secondary'
                    }
                >
                    <IconButton
                        className={
                            'min-h-6 min-w-6 lg:min-h-12 lg:min-w-12 rounded-none border-b border-solid border-b-gray-200'
                        }
                        onClick={() => {
                            map?.setZoom((map?.getZoom() || 0) + 1);
                        }}
                    >
                        <PlusIcon />
                    </IconButton>

                    <IconButton
                        className={
                            'min-h-6 min-w-6 lg:min-h-12 lg:min-w-12 rounded-none'
                        }
                        onClick={() => {
                            map?.setZoom(
                                Math.max((map?.getZoom() || 0) - 1, 1),
                            );
                        }}
                    >
                        <MinusIcon />
                    </IconButton>
                </div>

                <div
                    className={
                        'flex flex-col bg-bg-primary rounded-md shadow-[0_11px_20px_rgba(0,0,0,0.1)] border border-bg-secondary'
                    }
                >
                    <IconButton
                        className={
                            'min-h-6 min-w-6 lg:min-h-12 lg:min-w-12 rounded-none'
                        }
                        onClick={() => {
                            map?.setCenter({
                                lat: defaultLocation.latitude,
                                lng: defaultLocation.longitude,
                            });
                        }}
                    >
                        <NavigationIcon />
                    </IconButton>
                </div>
            </div>
        </>
    );
}
