import LoadingButton from '@mui/lab/LoadingButton';
import Grid from '@mui/material/Grid';
import { HTMLAttributes } from 'react';
import { useI18n } from '@bookr-technologies/i18n';
import { SearchBusinessModel } from '@bookr-technologies/sdk';
import { BusinessCard } from '~/components/BusinessCard';

export function SearchContainerResults({
    hits,
    hasMore,
    loading,
    onMore,
    ...rest
}: HTMLAttributes<HTMLDivElement> & {
    hits: SearchBusinessModel[];
    hasMore: boolean;
    loading: boolean;
    onMore: () => void;
}) {
    const { t } = useI18n();

    return (
        <div {...rest}>
            {hits.map((hit) => (
                <BusinessCard key={hit.id} business={hit} displayServices />
            ))}

            {hasMore && hits.length ? (
                <Grid container justifyContent={'center'}>
                    <LoadingButton
                        variant={'contained'}
                        color={'primary'}
                        size={'medium'}
                        onClick={onMore}
                        loading={loading}
                    >
                        {t('viewMore')}
                    </LoadingButton>
                </Grid>
            ) : null}
        </div>
    );
}
