import CloseIcon from '@mui/icons-material/Close';
import EventAvailable from '@mui/icons-material/EventAvailable';
import StorefrontIcon from '@mui/icons-material/Storefront';
import Button from '@mui/material/Button';
import Card from '@mui/material/Card';
import Dialog, { DialogProps } from '@mui/material/Dialog';
import Divider from '@mui/material/Divider';
import FormControl from '@mui/material/FormControl';
import FormControlLabel from '@mui/material/FormControlLabel';
import IconButton from '@mui/material/IconButton';
import InputAdornment from '@mui/material/InputAdornment';
import Radio from '@mui/material/Radio';
import RadioGroup from '@mui/material/RadioGroup';
import Slider from '@mui/material/Slider';
import TextField from '@mui/material/TextField';
import Typography from '@mui/material/Typography';
import useMediaQuery from '@mui/material/useMediaQuery';
import { useFormik } from 'formik';
import { clamp, num } from '@bookr-technologies/core';
import { useI18n } from '@bookr-technologies/i18n';
import { SortTypeEnum } from '@bookr-technologies/sdk/bookr/enums/SortTypeEnum';
import { cn } from '~/lib/utils';

type Range = [min: number | null, max: number | null];

export const DefaultSearchFilters = {
    sort: SortTypeEnum.RECOMMENDED,
    businessType: 'all',
    priceRange: [null, null] as Range,
    addressedFor: 'all',
};

export type SearchFilters = Partial<typeof DefaultSearchFilters>;

export type SearchContainerFiltersProps = Omit<DialogProps, 'onSubmit'> & {
    onSubmit(values: SearchFilters): void | Promise<void>;
};

export function SearchContainerFilters({
    onSubmit,
    ...rest
}: SearchContainerFiltersProps) {
    const { t } = useI18n();
    const isFullscreen = useMediaQuery('(max-width: 768px)');

    const formik = useFormik({
        initialValues: DefaultSearchFilters,
        async onSubmit(values) {
            await onSubmit(values);
            handleClose();
        },
    });

    const handleClose = () => rest?.onClose?.({}, 'backdropClick');

    return (
        <Dialog fullScreen={isFullscreen} fullWidth maxWidth={'sm'} {...rest}>
            <header
                className={
                    'flex justify-between items-center px-4 py-3 border-b border-bg-secondary'
                }
            >
                <Typography variant={'title2'} fontWeight={'700'}>
                    {t('advancedFilters')}
                </Typography>

                <IconButton onClick={handleClose} size={'small'}>
                    <CloseIcon />
                </IconButton>
            </header>

            <main className={'px-4 py-6 flex flex-col grow gap-8'}>
                <Sort
                    value={formik.values.sort}
                    onChange={(value) => formik.setFieldValue('sort', value)}
                />
                <BusinessType
                    value={formik.values.businessType}
                    onChange={(value) =>
                        formik.setFieldValue('businessType', value)
                    }
                />
                <PriceRange
                    min={0}
                    max={10000}
                    currency={'RON'}
                    value={formik.values.priceRange}
                    onChange={(value) =>
                        formik.setFieldValue('priceRange', value)
                    }
                />
                {/*<AddressedFor*/}
                {/*    value={formik.values.addressedFor}*/}
                {/*    onChange={(value) =>*/}
                {/*        formik.setFieldValue('addressedFor', value)*/}
                {/*    }*/}
                {/*/>*/}
            </main>

            <footer
                className={
                    'flex justify-between items-center p-4 border-t border-bg-secondary'
                }
            >
                <Button
                    variant={'subtle'}
                    onClick={() =>
                        formik.resetForm({
                            values: { ...DefaultSearchFilters },
                        })
                    }
                >
                    {t('deleteFilters')}
                </Button>
                <Button
                    variant={'contained'}
                    onClick={() => formik.handleSubmit()}
                    disabled={formik.isSubmitting}
                >
                    {t('applyFilters')}
                </Button>
            </footer>
        </Dialog>
    );
}

function Sort({
    value,
    onChange,
}: {
    value: string;
    onChange: (value: string) => void;
}) {
    const { t } = useI18n();

    return (
        <div className={'flex flex-col gap-2'}>
            <FormControl>
                <Typography
                    component={'label'}
                    variant={'body'}
                    fontWeight={'700'}
                    id="sort-group"
                    className={'mb-2'}
                >
                    {t('sortBy')}
                </Typography>
                <RadioGroup
                    aria-labelledby="sort-group"
                    value={value}
                    onChange={({ target }) => onChange(target.value)}
                >
                    <FormControlLabel
                        value={SortTypeEnum.RECOMMENDED}
                        control={<Radio />}
                        label={t('recommended')}
                        classes={{
                            root: 'h-8',
                            label: 'font-semibold text-[13px] leading-[20px]',
                        }}
                    />
                    <FormControlLabel
                        value={SortTypeEnum.DISTANCE}
                        control={<Radio />}
                        label={t('nearest')}
                        classes={{
                            root: 'h-8',
                            label: 'font-semibold text-[13px] leading-[20px]',
                        }}
                    />
                    <FormControlLabel
                        value={SortTypeEnum.RATING}
                        control={<Radio />}
                        label={t('mostAppreciated')}
                        classes={{
                            root: 'h-8',
                            label: 'font-semibold text-[13px] leading-[20px]',
                        }}
                    />
                    <FormControlLabel
                        value={SortTypeEnum.MIN_PRICE}
                        control={<Radio />}
                        label={t('smallestPrice')}
                        classes={{
                            root: 'h-8',
                            label: 'font-semibold text-[13px] leading-[20px]',
                        }}
                    />
                </RadioGroup>
            </FormControl>
        </div>
    );
}

function BusinessType({
    value,
    onChange,
}: {
    value: string;
    onChange: (value: string) => void;
}) {
    const { t } = useI18n();

    return (
        <div className={'flex flex-col gap-2'}>
            <Typography variant={'body'} fontWeight={'700'}>
                {t('businessType')}
            </Typography>

            <div className={'grid grid-cols-1 md:grid-cols-2 gap-2'}>
                <Card
                    variant={'outlined'}
                    className={cn(
                        'cursor-pointer hover:border-primary p-4 flex flex-col',
                        value === 'all' && 'border-primary bg-bg-secondary',
                    )}
                    onClick={() => onChange('all')}
                >
                    <StorefrontIcon
                        fontSize={'small'}
                        className={'text-gray-400'}
                    />
                    <Typography
                        variant={'footnote'}
                        fontWeight={'700'}
                        className={'mt-2 mb-1'}
                    >
                        {t('allBusinesses')}
                    </Typography>
                    <Typography
                        variant={'caption1'}
                        className={'text-content-tertiary'}
                    >
                        {t('allBusinessesFilterDescription')}
                    </Typography>
                </Card>

                <Card
                    variant={'outlined'}
                    className={cn(
                        'cursor-pointer hover:border-primary p-4 flex flex-col',
                        value === 'instant' && 'border-primary bg-bg-secondary',
                    )}
                    onClick={() => onChange('instant')}
                >
                    <EventAvailable
                        fontSize={'small'}
                        className={'text-gray-400'}
                    />
                    <Typography
                        variant={'footnote'}
                        fontWeight={'700'}
                        className={'mt-2 mb-1'}
                    >
                        {t('instantBusiness')}
                    </Typography>
                    <Typography
                        variant={'caption1'}
                        className={'text-content-tertiary'}
                    >
                        {t('instantBusinessFilterDescription')}
                    </Typography>
                </Card>
            </div>
        </div>
    );
}

function PriceRange({
    value,
    onChange,
    min,
    max,
    currency,
}: {
    min: number;
    max: number;
    currency: string;
    value: Range;
    onChange: (value: Range) => void;
}) {
    const { t } = useI18n();

    return (
        <div className={'flex flex-col gap-2'}>
            <Typography variant={'body'} fontWeight={'700'}>
                {t('priceRange')}
            </Typography>

            <div className={'flex flex-col px-4 gap-4'}>
                <Slider
                    min={min}
                    max={max}
                    value={[value[0] ?? min, value[1] ?? max]}
                    onChange={(_, newValue) => onChange(newValue as Range)}
                    valueLabelDisplay="auto"
                    valueLabelFormat={(value) => `${value} ${currency}`}
                    classes={{
                        rail: 'bg-gray-200',
                        thumb: 'w-7 h-7 bg-white border-2 border-primary shadow-[0_2px_6px_rgba(5,16,55,0.1)]',
                    }}
                />
                <div className={'flex items-center gap-4 lg:gap-8 -mx-4'}>
                    <TextField
                        type={'number'}
                        className={'w-full'}
                        label={t('minimum')}
                        variant="outlined"
                        value={num(value[0], min)}
                        inputProps={{ min, max }}
                        onChange={({ target }) =>
                            onChange([
                                clamp(num(target.value), min, max),
                                value[1],
                            ])
                        }
                        InputProps={{
                            endAdornment: (
                                <InputAdornment
                                    position="end"
                                    className={'mb-0'}
                                >
                                    <span className={'font-semibold'}>
                                        {currency}
                                    </span>
                                </InputAdornment>
                            ),
                        }}
                    />
                    <Divider className={'w-4 lg:w-8'} />
                    <TextField
                        type={'number'}
                        className={'w-full'}
                        label={t('maximum')}
                        variant="outlined"
                        value={num(value[1], max)}
                        inputProps={{ min, max }}
                        onChange={({ target }) =>
                            onChange([
                                value[0],
                                clamp(num(target.value), min, max),
                            ])
                        }
                        InputProps={{
                            endAdornment: (
                                <InputAdornment
                                    position="end"
                                    className={'mb-0'}
                                >
                                    <span className={'font-semibold'}>
                                        {currency}
                                    </span>
                                </InputAdornment>
                            ),
                        }}
                    />
                </div>
            </div>
        </div>
    );
}

// function AddressedFor() {
//     const { t } = useI18n();
//
//     return (
//         <div className={'flex flex-col gap-2'}>
//             <Typography variant={'body'} fontWeight={'700'}>
//                 {t('addressedFor')}
//             </Typography>
//         </div>
//     );
// }
