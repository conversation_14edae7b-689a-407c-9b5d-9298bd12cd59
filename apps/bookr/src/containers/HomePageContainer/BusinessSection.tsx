import NavigateBeforeIcon from '@mui/icons-material/NavigateBefore';
import NavigateNextIcon from '@mui/icons-material/NavigateNext';
import Box from '@mui/material/Box';
import Container from '@mui/material/Container';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import { Theme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import { useElementsScroller } from '@bookr-technologies/hooks';
import { SearchBusinessModel } from '@bookr-technologies/sdk';
import { BusinessCard } from '~/components/BusinessCard';
import { DisplayContent } from '~/components/DisplayContent';
import { StyledBusinessesHolder, StyledNavigationButton } from './styles';

interface BusinessSectionProps {
    title: string;
    loading: boolean;
    results: SearchBusinessModel[];
}

export function BusinessSection({
    title,
    loading,
    results,
}: BusinessSectionProps) {
    const scroller = useElementsScroller(24);
    const isXs = useMediaQuery<Theme>((theme) => theme.breakpoints.down('sm'));

    if (!loading && !results.length) {
        return null;
    }

    return (
        <Container>
            <Grid
                container
                alignItems={'center'}
                justifyContent={'space-between'}
                gap={isXs ? 2 : 3}
                mb={2}
            >
                <Typography
                    variant={isXs ? 'title3' : 'largeTitle'}
                    fontWeight={800}
                >
                    {title}
                </Typography>

                <Box display={'flex'} gap={1.5} ml={2}>
                    <StyledNavigationButton
                        onClick={scroller.previous}
                        disabled={scroller.isFirst}
                    >
                        <NavigateBeforeIcon />
                    </StyledNavigationButton>
                    <StyledNavigationButton
                        onClick={scroller.next}
                        disabled={scroller.isLast}
                    >
                        <NavigateNextIcon />
                    </StyledNavigationButton>
                </Box>
            </Grid>

            <DisplayContent loading={loading}>
                <StyledBusinessesHolder ref={scroller.elementRef}>
                    {results.map((result) => (
                        <div key={result.id} className={'BusinessSection-item'}>
                            <BusinessCard business={result} />
                        </div>
                    ))}
                </StyledBusinessesHolder>
            </DisplayContent>
        </Container>
    );
}
