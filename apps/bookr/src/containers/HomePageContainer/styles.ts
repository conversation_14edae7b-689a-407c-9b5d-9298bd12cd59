import ButtonBase from '@mui/material/ButtonBase';
import IconButton from '@mui/material/IconButton';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import Image from 'next/image';
import { palette } from '@bookr-technologies/ui/styles';

export const StyledMainSectionContainer = styled('div', {
    name: 'MainSection',
})(({ theme }) => ({
    padding: theme.spacing(10, 3),
    backgroundColor: palette.gray.gray900,
    borderRadius: 12,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
    overflow: 'hidden',
    [theme.breakpoints.down('sm')]: {
        padding: theme.spacing(5, 3),
    },
}));

export const StyledMainSectionContent = styled(Stack)(({ theme }) => ({
    maxWidth: 600,
    gap: theme.spacing(7),
    position: 'relative',
    zIndex: 2,
}));

export const StyledMainSectionTitle = styled(Typography)(({ theme }) => ({
    textAlign: 'center',
    fontSize: 48,
    fontWeight: 800,
    lineHeight: 1.16,
    letterSpacing: -1.68,
    color: palette.foundation.white,
    [theme.breakpoints.down('sm')]: {
        fontSize: 28,
    },
}));

export const StyledMainSectionSubtitle = styled(Typography)(({ theme }) => ({
    textAlign: 'center',
    fontSize: 18,
    fontWeight: 600,
    lineHeight: 1.66,
    letterSpacing: 0.09,
    color: palette.foundation.white,
    [theme.breakpoints.down('sm')]: {
        fontSize: 14,
    },
}));

export const StyledMainSectionBackground = styled(Image as any)(() => ({
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    width: '100%',
    height: '100%',
    zIndex: 1,
    objectFit: 'cover',
    objectPosition: 'center bottom',
    pointerEvents: 'none',
}));

export const StyledCategoryCard = styled(ButtonBase)(({ theme }) => ({
    padding: theme.spacing(1.5),
    borderRadius: 12,
    minWidth: 220,
    height: 240,
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'flex-start',
    overflow: 'hidden',
    '.CategoryCard-name': {
        lineHeight: 1.5,
        textAlign: 'center',
        width: '100%',
        maxWidth: 140,
        wordSpacing: 140,
        transition: theme.transitions.create(['transform'], {
            easing: theme.transitions.easing.easeInOut,
            duration: theme.transitions.duration.complex,
        }),
    },
    '.CategoryCard-image': {
        transform: 'translateY(30px) scale(0.9)',
        opacity: 1,
        filter: 'blur(0px)',
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        pointerEvents: 'none',
        transition: theme.transitions.create(
            ['transform', 'opacity', 'filter'],
            {
                easing: theme.transitions.easing.easeInOut,
                duration: theme.transitions.duration.complex,
            },
        ),
    },
    '.CategoryCard-button': {
        position: 'absolute',
        bottom: theme.spacing(2),
        left: theme.spacing(2),
        right: theme.spacing(2),
        justifyContent: 'space-between',
        opacity: 0,
        transform: 'translateY(20px) scale(0.9)',
        pointerEvents: 'none',
        transition: theme.transitions.create(['opacity', 'transform'], {
            easing: theme.transitions.easing.easeInOut,
            duration: theme.transitions.duration.shorter,
        }),
        '&, &:hover': {
            backgroundColor: palette.foundation.white,
            color: palette.foundation.primary,
        },
    },
    '&:hover': {
        '.CategoryCard-name': {
            transform: 'translateY(50%) scale(1.05)',
        },
        '.CategoryCard-image': {
            transform: 'translateY(0) scale(1)',
            filter: 'blur(20px)',
            opacity: 0.65,
        },
        '.CategoryCard-button': {
            opacity: 1,
            transform: 'translateY(0) scale(1)',
        },
    },
}));

export const StyledCategoriesHolder = styled('div')(({ theme }) => ({
    display: 'flex',
    overflowX: 'auto',
    width: '100%',
    maxWidth: '100%',
    gap: theme.spacing(2.5),
    paddingBottom: theme.spacing(2),
    paddingRight: theme.spacing(2.5),
    scrollbarWidth: 'none',
    msOverflowStyle: 'none',
    '&::-webkit-scrollbar': {
        display: 'none',
    },
}));

export const StyledCategoriesControls = styled('div')(() => ({
    display: 'flex',
    flexGrow: 1,
    justifyContent: 'flex-end',
    '.MuiButton-root': {
        backgroundColor: palette.foundation.white,
    },
}));

export const StyledNavigationButton = styled(IconButton)(({ theme }) => ({
    backgroundColor: palette.extended.backgroundPrimary,
    border: `1px solid ${palette.foundation.white}`,
    transition: theme.transitions.create(['border-color', 'background-color']),
    '&, *': {
        borderRadius: 10,
    },
    '&:hover': {
        borderColor: palette.extended.borderPrimary,
        backgroundColor: palette.extended.backgroundSecondary,
    },
    '&:disabled': {
        borderColor: palette.extended.borderDisabled,
    },
}));

export const StyledBusinessesHolder = styled('div')(({ theme }) => ({
    display: 'flex',
    flexDirection: 'row',
    flexWrap: 'nowrap',
    gap: theme.spacing(3),
    borderRadius: 12,
    overflowX: 'auto',
    scrollbarWidth: 'none',
    msOverflowStyle: 'none',
    '&::-webkit-scrollbar': {
        display: 'none',
    },
    '.BusinessSection-item': {
        flexShrink: 0,
        width: '18vw',
        minWidth: '220px',
        maxWidth: '300px',
    },
}));
