import NavigateBeforeIcon from '@mui/icons-material/NavigateBefore';
import NavigateNextIcon from '@mui/icons-material/NavigateNext';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Container from '@mui/material/Container';
import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { Theme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import Link from 'next/link';
import { useMemo, useRef } from 'react';
import {
    useElementsScroller,
    useLocation,
    useWindowEvent,
} from '@bookr-technologies/hooks';
import { useI18n } from '@bookr-technologies/i18n';
import { useGetCategoriesSuspenseQuery } from '~/api';
import { DisplayContent } from '~/components/DisplayContent';
import { RouteLinks } from '~/lib/routes';
import { CategoryCard } from './CategoryCard';
import {
    StyledCategoriesControls,
    StyledCategoriesHolder,
    StyledNavigationButton,
} from './styles';

export function Categories() {
    const containerRef = useRef<HTMLDivElement>(null);
    const scroller = useElementsScroller(20);
    const location = useLocation();

    const { t } = useI18n();
    const categoriesQuery = useGetCategoriesSuspenseQuery();
    const isXs = useMediaQuery<Theme>((theme) => theme.breakpoints.down('sm'));

    const filteredCategories = useMemo(() => {
        const categories = categoriesQuery.data ?? [];

        return categories.filter(
            (category) => category.imageUrl && category.categoryRank > 1,
        );
    }, [categoriesQuery.data]);

    useWindowEvent('resize', () => {
        if (containerRef.current && scroller.elementRef.current) {
            const paddingLeft = isXs ? 16 : 24;

            if (
                scroller.elementRef.current.clientWidth >=
                scroller.elementRef.current.scrollWidth
            ) {
                scroller.elementRef.current.style.paddingLeft = `${paddingLeft}px`;
                scroller.elementRef.current.style.paddingRight = `${paddingLeft}px`;
                scroller.elementRef.current.style.justifyContent = isXs
                    ? 'flex-start'
                    : 'center';

                return;
            }

            scroller.elementRef.current.style.paddingLeft = `${
                containerRef.current.offsetLeft + paddingLeft
            }px`;
        }
    });

    return (
        <DisplayContent
            loading={categoriesQuery.isLoading}
            noResults={!filteredCategories.length}
            disabledSlots={{ NoResults: true, Error: true }}
        >
            <Stack gap={isXs ? 2 : 3}>
                <Container ref={containerRef}>
                    <Grid container alignItems={'center'}>
                        <Typography
                            variant={isXs ? 'title3' : 'largeTitle'}
                            fontWeight={800}
                        >
                            {t('bestCategories')}
                        </Typography>

                        <StyledCategoriesControls>
                            {!isXs ? (
                                <Button
                                    variant={'contained'}
                                    color={'secondary'}
                                    LinkComponent={Link}
                                    href={RouteLinks.search({ location })}
                                >
                                    {t('viewAll')}
                                </Button>
                            ) : null}
                            <Box display={'flex'} gap={1.5} ml={2}>
                                <StyledNavigationButton
                                    onClick={scroller.previous}
                                    disabled={scroller.isFirst}
                                >
                                    <NavigateBeforeIcon />
                                </StyledNavigationButton>
                                <StyledNavigationButton
                                    onClick={scroller.next}
                                    disabled={scroller.isLast}
                                >
                                    <NavigateNextIcon />
                                </StyledNavigationButton>
                            </Box>
                        </StyledCategoriesControls>
                    </Grid>
                </Container>
                <StyledCategoriesHolder ref={scroller.elementRef}>
                    {filteredCategories.map((category) => (
                        <CategoryCard
                            key={category.name}
                            name={category.name}
                            color={category.color}
                            imageUrl={category.imageUrl}
                            location={`${location?.latitude},${location?.longitude}`}
                        />
                    ))}
                </StyledCategoriesHolder>
            </Stack>
        </DisplayContent>
    );
}
