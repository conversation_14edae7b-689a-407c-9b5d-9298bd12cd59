import Box from '@mui/material/Box';
import Container from '@mui/material/Container';
import GlobalStyles from '@mui/material/GlobalStyles';
import Stack from '@mui/material/Stack';
import { Theme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import { useEffect, useRef } from 'react';
import { clamp, num } from '@bookr-technologies/core';
import { useElement, useScroll } from '@bookr-technologies/hooks';
import { useI18n } from '@bookr-technologies/i18n';
import { SearchBar } from '~/components/SearchBar';
import MainSectionBackground from './assets/MainSectionBackground.jpg';
import {
    StyledMainSectionBackground,
    StyledMainSectionContainer,
    StyledMainSectionContent,
    StyledMainSectionSubtitle,
    StyledMainSectionTitle,
} from './styles';

export function MainSection() {
    const rootRef = useRef<HTMLDivElement>(null);
    const searchBarHolderRef = useRef<HTMLDivElement>(null);
    const backgroundRef = useRef<HTMLImageElement>(null);
    const { t } = useI18n();
    const header = useElement('.Header-searchBar');
    const isXs = useMediaQuery<Theme>((theme) => theme.breakpoints.down('sm'));
    const scroll = useScroll();

    const rootMaxOffset =
        num(rootRef.current?.clientTop) + num(rootRef.current?.clientHeight);

    const step = clamp(scroll.y / rootMaxOffset, 0, 1);
    const backgroundScale = 1 + step * 0.3;
    const backgroundOpacity = 1 - step * 0.5;
    const searchBarOffset = -(50 * step);
    const searchBarOpacity = 1 - (step > 0.74 ? (step - 0.74) / 0.26 : 0);

    useEffect(() => {
        const el = header.get();
        if (el) {
            const percent = searchBarOpacity * 1.3;
            el.style.opacity = `${1 - percent}`;
            el.style.transform = `translateY(${Math.min(
                0,
                searchBarOpacity * -6,
            )}px) scale(${1 - searchBarOpacity * 0.02}`;

            el.style.pointerEvents = 1 - percent > 0.4 ? 'all' : 'none';
        }

        if (searchBarHolderRef.current) {
            searchBarHolderRef.current.style.transform = `translateY(${searchBarOffset}px)`;
            searchBarHolderRef.current.style.opacity = `${searchBarOpacity}`;
        }
    }, [header, searchBarOpacity, searchBarOffset]);

    useEffect(() => {
        if (backgroundRef.current) {
            backgroundRef.current.style.transform = `scale(${backgroundScale})`;
            backgroundRef.current.style.opacity = `${backgroundOpacity}`;
        }
    }, [backgroundOpacity, backgroundScale]);

    const cleanupTimeoutRef = useRef<any>(null);

    useEffect(
        () => {
            if (cleanupTimeoutRef.current) {
                clearTimeout(cleanupTimeoutRef.current);
                cleanupTimeoutRef.current = null;
            }

            return () => {
                cleanupTimeoutRef.current = setTimeout(() => {
                    const el = header.get();
                    if (el) {
                        el.style.transition = 'all 0.2s ease-in-out';
                        el.style.transform = 'translate(0,0)';
                        el.style.opacity = '1';
                        setTimeout(() => {
                            el.style.removeProperty('transition');
                            el.style.removeProperty('pointer-events');
                        }, 500);
                    }
                }, 100);
            };
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [],
    );

    return (
        <Container>
            {globalStyle}
            <StyledMainSectionContainer ref={rootRef}>
                <StyledMainSectionContent>
                    <Stack gap={2}>
                        <StyledMainSectionTitle variant={'largeTitle'}>
                            {t('discoverBestBusinessesNearYou')}
                        </StyledMainSectionTitle>

                        <StyledMainSectionSubtitle variant={'title3'}>
                            {t('discoverBestBusinessesNearYouDescription')}
                        </StyledMainSectionSubtitle>
                    </Stack>

                    {!isXs ? (
                        <Box ref={searchBarHolderRef}>
                            <SearchBar />
                        </Box>
                    ) : null}
                </StyledMainSectionContent>
                <StyledMainSectionBackground
                    ref={backgroundRef}
                    src={MainSectionBackground}
                    blurDataURL={MainSectionBackground.blurDataURL}
                    alt={'MainSection'}
                    priority
                />
            </StyledMainSectionContainer>
        </Container>
    );
}

const globalStyle = (
    <GlobalStyles styles={{ '.Header-searchBar': { opacity: 0 } }} />
);
