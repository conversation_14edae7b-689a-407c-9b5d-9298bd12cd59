import Stack from '@mui/material/Stack';
import { Suspense } from 'react';
import { useLocation } from '@bookr-technologies/hooks';
import { useI18n } from '@bookr-technologies/i18n';
import {
    useGetBestBusinessesQuery,
    useGetBookedBusinessesQuery,
    useGetNearByBusinessesQuery,
    useGetRecentBusinessesQuery,
} from '~/api';
import { BusinessSection } from './BusinessSection';
import { Categories } from './Categories';
import { MainSection } from './MainSection';

export function HomePageContainer() {
    const { t } = useI18n();
    const location = useLocation();
    const latLng = location
        ? `${location.latitude},${location.longitude}`
        : undefined;

    const bestBusinessesQuery = useGetBestBusinessesQuery({ latLng });
    const nearByBusinessesQuery = useGetNearByBusinessesQuery({ latLng });
    const recentBusinessesQuery = useGetRecentBusinessesQuery();
    const bookedBusinessesQuery = useGetBookedBusinessesQuery();

    return (
        <Stack
            py={2}
            gap={{
                xs: 2,
                sm: 7,
            }}
        >
            <MainSection />
            <Suspense fallback={null}>
                <Categories />
            </Suspense>
            <Suspense fallback={null}>
                <BusinessSection
                    title={t('theBestBusinesses')}
                    loading={bestBusinessesQuery.isFetching}
                    results={bestBusinessesQuery.data ?? []}
                />
            </Suspense>
            <Suspense fallback={null}>
                <BusinessSection
                    title={t('nearByBusinesses')}
                    loading={nearByBusinessesQuery.isFetching}
                    results={nearByBusinessesQuery.data ?? []}
                />
            </Suspense>
            <Suspense fallback={null}>
                <BusinessSection
                    title={t('theLatestBusinesses')}
                    loading={recentBusinessesQuery.isFetching}
                    results={recentBusinessesQuery.data ?? []}
                />
            </Suspense>
            <Suspense fallback={null}>
                <BusinessSection
                    title={t('bookedBusinesses')}
                    loading={bookedBusinessesQuery.isFetching}
                    results={bookedBusinessesQuery.data ?? []}
                />
            </Suspense>
        </Stack>
    );
}
