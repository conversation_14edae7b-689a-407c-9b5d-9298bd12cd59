import VisibilityIcon from '@mui/icons-material/Visibility';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import Image from 'next/image';
import Link from 'next/link';
import { useMemo } from 'react';
import { titleCase } from '@bookr-technologies/core';
import { useI18n } from '@bookr-technologies/i18n';
import { Optional } from '@bookr-technologies/types';
import { RouteLinks } from '~/lib/routes';
import { StyledCategoryCard } from './styles';

interface CategoryProps {
    name: string;
    color: string;
    location: string;
    imageUrl: Optional<string>;
}

export function CategoryCard({
    name,
    color,
    imageUrl,
    location,
}: CategoryProps) {
    const { t } = useI18n();

    const localizedName = `categories.${name.toLowerCase()}.name`;
    const translatedName = t(localizedName as any, {
        defaultValue: titleCase(name),
    });

    const extra = useMemo(
        () => ({
            sx: { backgroundColor: color },
            href: RouteLinks.search({
                category: name,
                location,
            }),
        }),
        [color, location, name],
    );

    return (
        <StyledCategoryCard LinkComponent={Link} {...extra}>
            {imageUrl ? (
                <Image
                    className={'CategoryCard-image'}
                    src={imageUrl}
                    width={220}
                    height={260}
                    alt={name}
                />
            ) : null}
            <Typography
                className={'CategoryCard-name'}
                variant={'body'}
                fontWeight={800}
                color={'#fff'}
            >
                {translatedName}
            </Typography>

            <Button
                component={'div'}
                variant={'contained'}
                className={'CategoryCard-button'}
                startIcon={<VisibilityIcon />}
            >
                {t('viewMore')}
                <Box width={18} height={18} />
            </Button>
        </StyledCategoryCard>
    );
}
