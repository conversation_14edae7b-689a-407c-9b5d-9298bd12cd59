import {
    AppointmentModel,
    AppointmentStatusEnum,
} from '@bookr-technologies/sdk';

export function mapAppointmentsByStatus(appointments: AppointmentModel[]) {
    return (appointments ?? []).reduce(
        (acc, appointment) => {
            acc[appointment.status].push(appointment);

            return acc;
        },
        {
            [AppointmentStatusEnum.CREATED]: [],
            [AppointmentStatusEnum.PENDING]: [],
            [AppointmentStatusEnum.IN_PROGRESS]: [],
            [AppointmentStatusEnum.CONFIRMED]: [],
            [AppointmentStatusEnum.CANCELLED]: [],
            [AppointmentStatusEnum.COMPLETED]: [],
            [AppointmentStatusEnum.NO_SHOW]: [],
        } as Record<AppointmentStatusEnum, AppointmentModel[]>,
    );
}

export function sortAppointmentsByDate(appointments: AppointmentModel[]) {
    return appointments.sort((a, b) => {
        const aDate = new Date(a.dateTime);
        const bDate = new Date(b.dateTime);

        return aDate.getTime() < bDate.getTime() ? 1 : -1;
    });
}
