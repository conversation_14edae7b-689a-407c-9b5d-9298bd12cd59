import Button from '@mui/material/Button';
import Chip from '@mui/material/Chip';
import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { addMinutes } from 'date-fns';
import parseISO from 'date-fns/parseISO';
import Link from 'next/link';
import { Fragment } from 'react';
import { num, str } from '@bookr-technologies/core';
import { useLocation } from '@bookr-technologies/hooks';
import { useI18n } from '@bookr-technologies/i18n';
import {
    AppointmentModel,
    AppointmentStatusEnum,
} from '@bookr-technologies/sdk';
import { DisplayContent } from '~/components/DisplayContent';
import { RouteLinks } from '~/lib/routes';
import { AppointmentCard } from '../AppointmentCard';
import { AppointmentsListProps } from './types';

export function AppointmentsList({
    activeAppointments,
    pastAppointments,
    selectedAppointmentId,
}: AppointmentsListProps) {
    const { t } = useI18n();
    const activeAppointmentsCount = activeAppointments.length;
    const pastAppointmentsCount = pastAppointments.length;
    const location = useLocation();

    function renderAppointmentCard(appointment: AppointmentModel) {
        const startDate = parseISO(appointment.dateTime);
        const endDate = addMinutes(
            startDate,
            num(appointment?.service?.duration),
        );

        return (
            <AppointmentCard
                key={appointment.id}
                appointmentId={appointment.id}
                startDate={startDate}
                endDate={endDate}
                businessName={str(appointment?.staff?.business?.name)}
                serviceName={str(appointment?.service?.name)}
                currency={str(appointment?.service?.currency, 'LEI')}
                price={num(appointment?.service?.price)}
                status={appointment?.status ?? AppointmentStatusEnum.CREATED}
                selected={appointment.id === selectedAppointmentId}
            />
        );
    }

    return (
        <Stack gap={2.5} flexGrow={1}>
            <Stack>
                <Grid container gap={1} alignItems={'center'}>
                    <Typography variant={'title1'} fontWeight={800}>
                        {t('activeAppointments')}
                    </Typography>
                    <Chip
                        label={activeAppointmentsCount}
                        color={'primary'}
                        size={'small'}
                    />
                </Grid>
                <DisplayContent
                    noResults={!activeAppointmentsCount}
                    slots={{
                        NoResults: (
                            <Stack alignItems={'flex-start'} gap={1} mt={1}>
                                <Typography
                                    variant={'subhead'}
                                    fontWeight={600}
                                    color={'textSecondary'}
                                    maxWidth={320}
                                >
                                    {t('noActiveAppointments')}
                                </Typography>
                                <Button
                                    variant={'contained'}
                                    color={'primary'}
                                    LinkComponent={Link}
                                    href={RouteLinks.search({ location })}
                                >
                                    {t('Book now')}
                                </Button>
                            </Stack>
                        ),
                    }}
                >
                    <Stack gap={1.25} mt={1}>
                        {activeAppointments.map(renderAppointmentCard)}
                    </Stack>
                </DisplayContent>
            </Stack>

            <DisplayContent
                noResults={!pastAppointmentsCount}
                slots={{
                    NoResults: <Fragment />,
                }}
            >
                <Stack>
                    <Grid container gap={1} alignItems={'center'}>
                        <Typography variant={'title1'} fontWeight={800}>
                            {t('previousAppointments')}
                        </Typography>
                        <Chip
                            label={pastAppointmentsCount}
                            color={'primary'}
                            size={'small'}
                        />
                    </Grid>
                    <Stack gap={1.25} mt={1}>
                        {pastAppointments.map(renderAppointmentCard)}
                    </Stack>
                </Stack>
            </DisplayContent>
        </Stack>
    );
}
