import Chip, { ChipProps } from '@mui/material/Chip';
import { styled } from '@mui/material/styles';
import { useMemo } from 'react';
import { str } from '@bookr-technologies/core';
import { useI18n } from '@bookr-technologies/i18n';
import { AppointmentStatusEnum } from '@bookr-technologies/sdk';

const statusColors = {
    [AppointmentStatusEnum.CREATED]: 'default',
    [AppointmentStatusEnum.PENDING]: 'default',
    [AppointmentStatusEnum.IN_PROGRESS]: 'info',
    [AppointmentStatusEnum.CONFIRMED]: 'info',
    [AppointmentStatusEnum.CANCELLED]: 'error',
    [AppointmentStatusEnum.COMPLETED]: 'info',
    [AppointmentStatusEnum.NO_SHOW]: 'warning',
} as const;

function AppointmentStatusBadgeComponent({
    status,
    ...rest
}: Omit<ChipProps, 'label'> & { status: AppointmentStatusEnum }) {
    const { t } = useI18n();
    const label = useMemo(
        () => str(t(`appointmentStatus.${status}`)),
        [status, t],
    );

    if (!rest.color) {
        rest.color = statusColors[status];
    }

    return <Chip variant={'subtle'} label={label} {...rest} />;
}
export const AppointmentStatusBadge = styled(AppointmentStatusBadgeComponent)(
    () => ({}),
);
