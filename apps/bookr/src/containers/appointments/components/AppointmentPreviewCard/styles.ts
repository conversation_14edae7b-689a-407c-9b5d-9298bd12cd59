import Avatar from '@mui/material/Avatar';
import Button from '@mui/material/Button';
import Paper from '@mui/material/Paper';
import { styled } from '@mui/material/styles';
import { palette } from '@bookr-technologies/ui';

export const StyledCard = styled(Paper)(({ theme }) => ({
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
    width: '100%',
    padding: theme.spacing(4),
    gap: theme.spacing(2),
    [theme.breakpoints.down('sm')]: {
        padding: theme.spacing(2),
    },
}));

export const StyledPaper = styled(Paper)(({ theme }) => ({
    padding: theme.spacing(1.5, 2),
    display: 'flex',
    flexDirection: 'row',
    backgroundColor: palette.extended.backgroundSecondary,
    width: '100%',
    '.MuiTypography-root b': {
        fontWeight: 800,
        color: theme.palette.text.primary,
    },
}));

export const StyledButton = styled(Button)(() => ({
    minWidth: 'calc(33.33334% - 8px)',
    '.MuiSvgIcon-root': {
        marginRight: 8,
        color: palette.foundation.accent,
    },
}));

export const StyledAvatar = styled(Avatar)(() => ({
    width: 56,
    height: 56,
    fontSize: 22,
    fontWeight: 600,
}));
