import CloseIcon from '@mui/icons-material/Close';
import DeleteForeverIcon from '@mui/icons-material/DeleteForever';
import EmailIcon from '@mui/icons-material/EmailOutlined';
import LocationOnIcon from '@mui/icons-material/LocationOnOutlined';
import PhoneInTalkIcon from '@mui/icons-material/PhoneInTalkOutlined';
import TextSmsIcon from '@mui/icons-material/TextsmsOutlined';
import LoadingButton from '@mui/lab/LoadingButton';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import Grid from '@mui/material/Grid';
import IconButton from '@mui/material/IconButton';
import MuiLink from '@mui/material/Link';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import formatDate from 'date-fns/format';
import Link from 'next/link';
import {
    date,
    displayNameInitials,
    formatCurrency,
    hrefTarget,
    num,
    profilePicture,
    str,
} from '@bookr-technologies/core';
import {
    useDialog,
    useEvent,
    useLocale,
    useNotification,
} from '@bookr-technologies/hooks';
import { useI18n } from '@bookr-technologies/i18n';
import { AppointmentStatusEnum } from '@bookr-technologies/sdk';
import { palette } from '@bookr-technologies/ui';
import { useAppointmentQuery, useCancelAppointmentMutation } from '~/api';
import { DisplayContent } from '~/components/DisplayContent';
import { RouteLinks } from '~/lib/routes';
import { AppointmentStatusBadge } from '../AppointmentStatusBadge';
import { StyledAvatar, StyledButton, StyledCard, StyledPaper } from './styles';
import { AppointmentPreviewCardProps } from './types';

export function AppointmentPreviewCard({
    appointmentId,
    enabled,
}: AppointmentPreviewCardProps) {
    const [locale, localeString] = useLocale();
    const { t } = useI18n();
    const notifications = useNotification();
    const deleteDialog = useDialog();
    const { data: appointment, isLoading } = useAppointmentQuery(
        appointmentId,
        { enabled },
    );

    const appointmentCancelMutation = useCancelAppointmentMutation();

    const { staff, service } = appointment ?? {};
    const { business } = staff ?? {};

    const canBeCanceled =
        appointment?.status === AppointmentStatusEnum.CONFIRMED ||
        appointment?.status === AppointmentStatusEnum.CREATED ||
        appointment?.status === AppointmentStatusEnum.PENDING;

    const handleAppointmentCancel = useEvent(async () => {
        try {
            await appointmentCancelMutation.mutateAsync(appointmentId);
            deleteDialog.close();
        } catch (error) {
            notifications.error(t('somethingWentWrong'));
        }
    });

    const renderAppointmentInfo = (dialog?: boolean) => (
        <StyledPaper>
            <Stack gap={1.25}>
                <Typography
                    variant={'subhead'}
                    fontWeight={600}
                    color={'textSecondary'}
                >
                    <b>{t('ID')}:</b>&nbsp; #{str(appointment?.id)}
                </Typography>
                <Typography
                    variant={'subhead'}
                    fontWeight={600}
                    color={'textSecondary'}
                >
                    <b>{t('staffMember')}:</b>&nbsp;
                    {str(staff?.displayName)}
                </Typography>
                <Typography
                    variant={'subhead'}
                    fontWeight={600}
                    color={'textSecondary'}
                >
                    <b>{t('service')}:</b>&nbsp;
                    {str(service?.name)}
                </Typography>
                <Typography
                    variant={'subhead'}
                    fontWeight={600}
                    color={'textSecondary'}
                >
                    <b>{t('price')}:</b>&nbsp;
                    {formatCurrency(
                        num(service?.price),
                        str(service?.currency, 'Lei'),
                        localeString,
                    )}
                </Typography>
                <Typography
                    variant={'subhead'}
                    fontWeight={600}
                    color={'textSecondary'}
                >
                    <b>{t('recurring')}:</b>&nbsp;
                    {t(appointment?.recurrent ? 'yes' : 'no')}
                </Typography>
                {dialog ? (
                    <Typography
                        variant={'subhead'}
                        fontWeight={600}
                        color={'textSecondary'}
                    >
                        <b>{t('dateAndTime')}:</b>&nbsp;
                        {formatDate(date(appointment?.dateTime), 'PPp', {
                            locale,
                        })}
                    </Typography>
                ) : null}
            </Stack>
        </StyledPaper>
    );

    return (
        <DisplayContent loading={isLoading}>
            <StyledCard>
                <AppointmentStatusBadge
                    status={
                        appointment?.status || AppointmentStatusEnum.PENDING
                    }
                    size={'small'}
                />
                <Stack gap={0.5} alignItems={'flex-start'}>
                    <Typography variant={'title1'} fontWeight={800}>
                        {`${str(service?.name)}, ${formatDate(
                            date(appointment?.dateTime),
                            'PP p',
                            { locale },
                        )}`}
                    </Typography>
                    <MuiLink
                        component={Link}
                        href={`/b/${business?.slug || business?.slug}`}
                        variant={'footnote'}
                        color={palette.foundation.accent}
                        fontWeight={600}
                        underline={'hover'}
                    >
                        {str(business?.name)}
                    </MuiLink>
                </Stack>

                <Grid container gap={1}>
                    {appointment?.onlineEvent ? (
                        <Button
                            variant={'contained'}
                            color={'info'}
                            startIcon={<LocationOnIcon />}
                            size={'small'}
                            {...hrefTarget(appointment.onlineEvent.joinUrl)}
                        >
                            {t('joinOnlineMeeting')}
                        </Button>
                    ) : (
                        <Button
                            variant={'contained'}
                            color={'info'}
                            startIcon={<LocationOnIcon />}
                            size={'small'}
                            {...hrefTarget(
                                RouteLinks.googleMapsLocation(
                                    str(business?.formattedAddress),
                                ),
                            )}
                        >
                            {t('viewLocation')}
                        </Button>
                    )}

                    {/*<Button*/}
                    {/*    variant={'subtle'}*/}
                    {/*    color={'info'}*/}
                    {/*    startIcon={<EditCalendarIcon />}*/}
                    {/*    size={'small'}*/}
                    {/*>*/}
                    {/*    {t('editAppointment')}*/}
                    {/*</Button>*/}
                </Grid>

                <Stack gap={1} width={'100%'}>
                    <Typography
                        variant={'footnote'}
                        fontWeight={600}
                        color={'textSecondary'}
                    >
                        {t('staffMember')}
                    </Typography>
                    <StyledPaper>
                        <StyledAvatar src={profilePicture(staff?.photoURL)}>
                            {displayNameInitials(str(staff?.displayName))}
                        </StyledAvatar>
                        <Stack flexGrow={1} pl={2} justifyContent={'center'}>
                            <Typography variant={'body'} fontWeight={800}>
                                {str(staff?.displayName)}
                            </Typography>
                            <Typography
                                variant={'footnote'}
                                fontWeight={600}
                                color={'textSecondary'}
                            >
                                {str(business?.name)}
                            </Typography>
                        </Stack>
                    </StyledPaper>
                    <Grid
                        container
                        gap={1}
                        justifyContent={'center'}
                        width={'auto'}
                        mx={-0.5}
                        flexWrap={'nowrap'}
                    >
                        {staff?.phoneNumber ? (
                            <StyledButton
                                variant={'subtle'}
                                href={`tel:${str(staff?.phoneNumber)}`}
                            >
                                <PhoneInTalkIcon fontSize={'small'} />
                                {t('call')}
                            </StyledButton>
                        ) : null}
                        {staff?.phoneNumber ? (
                            <StyledButton
                                variant={'subtle'}
                                href={`sms:${str(staff?.phoneNumber)}`}
                            >
                                <TextSmsIcon fontSize={'small'} />
                                {t('message')}
                            </StyledButton>
                        ) : null}
                        {staff?.email ? (
                            <StyledButton
                                variant={'subtle'}
                                href={`mailto:${str(staff?.email)}`}
                            >
                                <EmailIcon fontSize={'small'} />
                                {t('email')}
                            </StyledButton>
                        ) : null}
                    </Grid>
                </Stack>

                <Stack gap={1} width={'100%'}>
                    <Typography
                        variant={'footnote'}
                        fontWeight={600}
                        color={'textSecondary'}
                    >
                        {t('appointmentDetails')}
                    </Typography>
                    {renderAppointmentInfo()}
                </Stack>

                {canBeCanceled ? (
                    <Grid container>
                        <LoadingButton
                            variant={'subtle'}
                            color={'error'}
                            fullWidth
                            onClick={deleteDialog.open}
                        >
                            {t('cancel')}
                        </LoadingButton>

                        <Dialog {...deleteDialog.props}>
                            <Stack p={4} gap={3} alignItems={'center'}>
                                <Grid container justifyContent={'flex-end'}>
                                    <IconButton onClick={deleteDialog.close}>
                                        <CloseIcon />
                                    </IconButton>
                                </Grid>

                                <DeleteForeverIcon
                                    sx={{ fontSize: 56 }}
                                    color={'error'}
                                />

                                <Typography
                                    variant={'title1'}
                                    fontWeight={800}
                                    align={'center'}
                                >
                                    {t('doYouWantToDeleteThisAppointment')}
                                </Typography>

                                {renderAppointmentInfo(true)}

                                <Grid container flexWrap={'nowrap'} gap={1.75}>
                                    <Button
                                        fullWidth
                                        variant={'subtle'}
                                        size={'large'}
                                        onClick={handleAppointmentCancel}
                                    >
                                        {t('yesCancel')}
                                    </Button>
                                    <Button
                                        fullWidth
                                        variant={'contained'}
                                        color={'primary'}
                                        size={'large'}
                                        onClick={deleteDialog.close}
                                    >
                                        {t('no')}
                                    </Button>
                                </Grid>
                            </Stack>
                        </Dialog>
                    </Grid>
                ) : null}
            </StyledCard>
        </DisplayContent>
    );
}
