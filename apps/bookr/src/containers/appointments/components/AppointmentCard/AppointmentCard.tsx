import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import formatDate from 'date-fns/format';
import format from 'date-fns/format';
import { formatCurrency } from '@bookr-technologies/core';
import { useLocale } from '@bookr-technologies/hooks';
import { clsx } from '@bookr-technologies/ui';
import { AppointmentStatusBadge } from '../AppointmentStatusBadge';
import { StyledCard, StyledDate } from './styles';
import { AppointmentCardProps } from './types';

export function AppointmentCard({
    appointmentId,
    startDate,
    endDate,
    businessName,
    serviceName,
    price,
    currency,
    status,
    selected,
}: AppointmentCardProps) {
    const [locale, localeName] = useLocale();
    const time =
        format(startDate, 'p', { locale }) +
        ' - ' +
        format(endDate, 'p', { locale });

    return (
        <StyledCard
            href={`/appointments/${appointmentId}`}
            title={format(startDate, 'PPp', { locale })}
            className={clsx({
                'AppointmentCard-selected': selected,
            })}
        >
            <StyledDate>
                <Typography variant={'title3'} fontWeight={800}>
                    {formatDate(startDate, 'dd')}
                </Typography>
                <Typography
                    variant={'footnote'}
                    fontWeight={600}
                    color={'textSecondary'}
                    textTransform={'uppercase'}
                >
                    {formatDate(startDate, 'MMM')}
                </Typography>
            </StyledDate>
            <Stack pl={2} justifyContent={'space-between'} flexGrow={1}>
                <Stack>
                    <Typography variant={'callout'} fontWeight={600}>
                        {businessName}
                    </Typography>
                    <Typography
                        variant={'caption1'}
                        fontWeight={600}
                        color={'textSecondary'}
                    >
                        {serviceName} &bull; {time}
                    </Typography>
                </Stack>

                <Typography variant={'footnote'} fontWeight={600}>
                    {formatCurrency(price, currency, localeName)}
                </Typography>
            </Stack>
            <Stack>
                <AppointmentStatusBadge status={status} />
            </Stack>
        </StyledCard>
    );
}
