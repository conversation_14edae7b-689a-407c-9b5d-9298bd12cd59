import ButtonBase, { ButtonBaseProps } from '@mui/material/ButtonBase';
import { alpha, styled } from '@mui/material/styles';
import Link from 'next/link';
import { palette, shadows } from '@bookr-technologies/ui';

export const StyledCard = styled(
    ({ ...rest }: ButtonBaseProps & { href: string; scroll?: boolean }) => (
        <ButtonBase LinkComponent={Link} {...rest} />
    ),
)(({ theme }) => ({
    width: '100%',
    padding: theme.spacing(2),
    display: 'flex !important',
    flexDirection: 'row',
    backgroundColor: theme.palette.background.paper,
    borderRadius: 12,
    justifyContent: 'flex-start',
    alignItems: 'stretch',
    boxShadow: shadows.transparent,
    outline: `2px solid ${alpha(theme.palette.info.main, 0)}`,
    transition: theme.transitions.create(['box-shadow', 'outline']),
    textAlign: 'left',
    '&:hover, &.AppointmentCard-selected': {
        boxShadow: shadows.medium,
    },
    '&.AppointmentCard-selected': {
        outline: `2px solid ${theme.palette.info.main}`,
    },
}));

export const StyledDate = styled('div')(({ theme }) => ({
    width: 75,
    height: 75,
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    gap: theme.spacing(0.5),
    backgroundColor: palette.extended.backgroundSecondary,
    borderRadius: 12,
    padding: theme.spacing(2, 1.25),
}));
