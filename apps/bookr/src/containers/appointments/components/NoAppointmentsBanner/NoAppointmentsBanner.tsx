import Button from '@mui/material/Button';
import Link from 'next/link';
import { useLocation } from '@bookr-technologies/hooks';
import { useI18n } from '@bookr-technologies/i18n';
import { NoDataBanner } from '~/components/NoDataBanner';
import { RouteLinks } from '~/lib/routes';
import CalendarAsset from './assets/calendar.png';

export function NoAppointmentsBanner() {
    const { t } = useI18n();
    const location = useLocation();

    return (
        <NoDataBanner
            title={t('youDontHaveAnyAppointmentsYet')}
            subtitle={t('youDontHaveAnyAppointmentsYetDetails')}
            src={CalendarAsset}
            alt={'calendar'}
        >
            <Button
                variant={'contained'}
                color={'info'}
                LinkComponent={Link}
                href={RouteLinks.search({ location })}
            >
                {t('exploreNewBusinesses')}
            </Button>
        </NoDataBanner>
    );
}
