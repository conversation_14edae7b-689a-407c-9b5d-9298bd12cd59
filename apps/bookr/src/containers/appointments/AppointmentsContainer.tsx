import LoadingButton from '@mui/lab/LoadingButton';
import Container from '@mui/material/Container';
import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import { styled } from '@mui/material/styles';
import { useRouter } from 'next/router';
import { useMemo } from 'react';
import { num } from '@bookr-technologies/core';
import { useEvent } from '@bookr-technologies/hooks';
import { useI18n } from '@bookr-technologies/i18n';
import { AppointmentStatusEnum } from '@bookr-technologies/sdk';
import { collectPages, useAppointmentsByUserQuery } from '~/api';
import { DisplayContent } from '~/components/DisplayContent';
import { AppointmentPreviewCard } from './components/AppointmentPreviewCard';
import { AppointmentsList } from './components/AppointmentsList';
import { NoAppointmentsBanner } from './components/NoAppointmentsBanner';
import { mapAppointmentsByStatus, sortAppointmentsByDate } from './lib/utils';

export function AppointmentsContainer() {
    const router = useRouter();
    const appointmentId = num(router.query.appointmentId);

    const { t } = useI18n();
    const appointments = useAppointmentsByUserQuery(
        {
            sort: ['dateTime,desc', 'cancelled,asc'],
            size: 10,
        },
        {
            onSuccess: async (data) => {
                if (!appointmentId) {
                    const { content } = collectPages(data);
                    if (content[0]) {
                        await router.push(`/appointments/${content[0].id}`);
                    }
                }
            },
        },
    );

    const isLoading = appointments.isLoading;
    const { content, last: isLastPage } = useMemo(
        () => collectPages(appointments.data),
        [appointments.data],
    );

    const mappedAppointments = useMemo(
        () => mapAppointmentsByStatus(content ?? []),
        [content],
    );

    const activeAppointments = useMemo(
        () =>
            sortAppointmentsByDate(
                mappedAppointments[AppointmentStatusEnum.CREATED].concat(
                    mappedAppointments[AppointmentStatusEnum.IN_PROGRESS],
                    mappedAppointments[AppointmentStatusEnum.PENDING],
                    mappedAppointments[AppointmentStatusEnum.CONFIRMED],
                ),
            ),
        [mappedAppointments],
    );

    const pastAppointments = useMemo(
        () =>
            sortAppointmentsByDate(
                mappedAppointments[AppointmentStatusEnum.COMPLETED].concat(
                    mappedAppointments[AppointmentStatusEnum.CANCELLED],
                ),
            ),
        [mappedAppointments],
    );

    const handleViewMore = useEvent(() => appointments.fetchNextPage());

    return (
        <Container>
            <Stack py={4} alignItems={'center'}>
                <DisplayContent
                    loading={isLoading}
                    noResults={content?.length === 0}
                    error={!!appointments.error}
                    slots={{
                        NoResults: <NoAppointmentsBanner />,
                    }}
                >
                    <Layout container alignItems={'flex-start'} rowSpacing={3}>
                        <Grid
                            container
                            item
                            order={2}
                            xs={12}
                            md={6}
                            pr={{ md: 1.25 }}
                        >
                            <AppointmentsList
                                activeAppointments={activeAppointments}
                                pastAppointments={pastAppointments}
                                selectedAppointmentId={appointmentId}
                            />
                            {!isLoading && !isLastPage ? (
                                <Grid
                                    container
                                    justifyContent={'center'}
                                    my={3}
                                >
                                    <LoadingButton
                                        variant={'subtle'}
                                        color={'info'}
                                        onClick={handleViewMore}
                                        loading={
                                            appointments.isFetchingNextPage
                                        }
                                    >
                                        {t('viewMore')}
                                    </LoadingButton>
                                </Grid>
                            ) : null}
                        </Grid>
                        <Grid
                            order={{ xs: 1, md: 2 }}
                            container
                            item
                            xs={12}
                            md={6}
                            pl={{ md: 1.25 }}
                            position={{
                                xs: 'relative',
                                md: 'sticky',
                            }}
                            top={{
                                xs: 0,
                                md: 98,
                            }}
                        >
                            {appointmentId ? (
                                <AppointmentPreviewCard
                                    appointmentId={appointmentId}
                                    enabled={!isLoading}
                                />
                            ) : null}
                        </Grid>
                    </Layout>
                </DisplayContent>
            </Stack>
        </Container>
    );
}

const Layout = styled(Grid)(() => ({
    maxWidth: 1080,
}));

export default AppointmentsContainer;
