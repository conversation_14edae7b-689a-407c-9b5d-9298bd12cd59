import createEmotionServer from '@emotion/server/create-instance';
import Document, { Head, Html, Main, NextScript } from 'next/document';
import React from 'react';
import { getRuntimeConfig } from '@bookr-technologies/config';
import { createEmotionCache, theme } from '@bookr-technologies/ui';

export default class MyDocument extends Document<{ emotionStyleTags: any }> {
    static async getInitialProps(ctx: any) {
        const originalRenderPage = ctx.renderPage;
        const cache = createEmotionCache();
        const { extractCriticalToChunks } = createEmotionServer(cache);

        ctx.renderPage = () =>
            originalRenderPage({
                enhanceApp: (App: any) =>
                    function EnhanceApp(props: any) {
                        return <App emotionCache={cache} {...props} />;
                    },
            });

        const initialProps = await Document.getInitialProps(ctx);
        // This is important. It prevents Emotion to render invalid HTML.
        // See https://github.com/mui/material-ui/issues/26561#issuecomment-855286153
        const emotionStyles = extractCriticalToChunks(initialProps.html);
        const emotionStyleTags = emotionStyles.styles.map((style: any) => (
            <style
                data-emotion={`${style.key} ${style.ids.join(' ')}`}
                key={style.key}
                // eslint-disable-next-line react/no-danger
                dangerouslySetInnerHTML={{ __html: style.css }}
            />
        ));

        return {
            ...initialProps,
            emotionStyleTags,
        };
    }

    render() {
        const { version, revision } = getRuntimeConfig();

        return (
            <Html lang="en" suppressHydrationWarning>
                <Head>
                    <meta name="emotion-insertion-point" content="" />
                    <meta
                        content={theme.palette.primary.main}
                        name="theme-color"
                    />
                    <meta
                        content={`${version} ${revision}`}
                        name="bkr-app-version"
                    />
                    {this.props.emotionStyleTags}
                </Head>
                <body>
                    <Main />
                    <NextScript />
                </body>
            </Html>
        );
    }
}
