import { CacheProvider, EmotionCache } from '@emotion/react';
import '@fontsource/plus-jakarta-sans/400.css';
import '@fontsource/plus-jakarta-sans/500.css';
import '@fontsource/plus-jakarta-sans/600.css';
import '@fontsource/plus-jakarta-sans/700.css';
import '@fontsource/plus-jakarta-sans/800.css';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import 'keen-slider/keen-slider.min.css';
import { AppProps } from 'next/app';
import { ReactNode, useState } from 'react';
import { initAnalytics } from '@bookr-technologies/analytics';
import { getLocale } from '@bookr-technologies/core';
import { initializeFirebase } from '@bookr-technologies/firebase/lib/init';
import {
    RemoteConfigFlag,
    useBrowser,
    usePageView,
} from '@bookr-technologies/hooks';
import { useFeatureFlagAsync } from '@bookr-technologies/hooks/lib/useFeatureFlag';
import { createEmotionCache, ThemeProvider } from '@bookr-technologies/ui';
import {
    GlobalStyles,
    NotificationProvider,
} from '@bookr-technologies/ui/components';
import { Devtools } from '@bookr-technologies/ui/components/Devtools';
import { AnalyticsScripts } from '~/components/AnalyticsScripts';
import { ErrorPage } from '~/components/ErrorPage';
import { RootLayout } from '~/components/RootLayout';
import { SeoHead, SeoMeta } from '~/components/SeoHead';
import { SessionProvider } from '~/components/SessionProvider';
import { AuthSession } from '~/lib/session';
import '../lib/progressBar';
import '../lib/progressBar/style.css';
import './global.css';

const clientSideEmotionCache = createEmotionCache();

function MaintenanceWrapper(props: { children: ReactNode }) {
    const maintenanceEnabled = useFeatureFlagAsync(
        RemoteConfigFlag.MaintenanceEnabled,
    );

    if (typeof window !== 'undefined' && maintenanceEnabled?.asBoolean()) {
        return (
            <ErrorPage
                variant={'default'}
                title={'Maintenance'}
                message={
                    'We are currently performing maintenance on our servers. We will be back shortly.'
                }
            />
        );
    }

    return <>{props.children}</>;
}

function App({
    Component,
    pageProps,
    emotionCache = clientSideEmotionCache,
    router,
}: AppProps<{
    session: AuthSession;
    seoMeta?: SeoMeta;
}> & { emotionCache?: EmotionCache }) {
    const { session, seoMeta } = pageProps;

    const [queryClient] = useState(
        () =>
            new QueryClient({
                defaultOptions: {
                    queries: {
                        retry: false,
                        refetchOnWindowFocus: false,
                    },
                },
            }),
    );

    useBrowser(() => {
        // noinspection JSIgnoredPromiseFromCall
        initializeFirebase();
        initAnalytics();
    });
    usePageView();

    return (
        <>
            <SeoHead root {...seoMeta} />
            <AnalyticsScripts />
            <QueryClientProvider client={queryClient}>
                <SessionProvider session={session}>
                    <CacheProvider value={emotionCache}>
                        <LocalizationProvider
                            dateAdapter={AdapterDateFns}
                            adapterLocale={getLocale(router.locale || 'en')}
                        >
                            <ThemeProvider>
                                <NotificationProvider>
                                    <MaintenanceWrapper>
                                        <RootLayout>
                                            <GlobalStyles />
                                            <Component {...pageProps} />
                                            <Devtools />
                                        </RootLayout>
                                    </MaintenanceWrapper>
                                </NotificationProvider>
                            </ThemeProvider>
                        </LocalizationProvider>
                    </CacheProvider>
                </SessionProvider>
            </QueryClientProvider>
        </>
    );
}

export default App;
