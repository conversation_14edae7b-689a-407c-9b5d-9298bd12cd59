import Container from '@mui/material/Container';
import { BusinessSearch } from '~/components/BusinessSearch';
import { pipe } from '~/pipes/pipe';
import { withSession } from '~/pipes/withSession';
import { withUser } from '~/pipes/withUser';

function SearchPage() {
    return (
        <Container>
            <BusinessSearch />
        </Container>
    );
}

export const getServerSideProps = pipe(
    withSession(),
    withUser(),
    // withPreload(preloadGetBusinessSearch, {
    //     argsGetter: (ctx) =>
    //         getSearchArguments(castArray(ctx.params?.args ?? []), ctx.query),
    // }),
);

export default SearchPage;
