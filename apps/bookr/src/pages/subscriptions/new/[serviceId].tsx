import dynamic from 'next/dynamic';
import { useRouter } from 'next/router';
import { num, queryValue, str } from '@bookr-technologies/core';
import { withSession } from '~/pipes/withSession';

const NewSubscriptionContainer = dynamic(
    () => import('~/containers/subscriptions/NewSubscriptionContainer'),
    { ssr: false },
);

export function NewSubscriptionPage() {
    const { query } = useRouter();
    const serviceId = num(queryValue(query.serviceId));
    const businessId = str(queryValue(query.businessId));

    return (
        <NewSubscriptionContainer
            serviceId={serviceId}
            subscriptionId={'new'}
            businessId={businessId}
        />
    );
}

export const getServerSideProps = withSession(true);

export default NewSubscriptionPage;
