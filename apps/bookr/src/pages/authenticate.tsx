import CircularProgress from '@mui/material/CircularProgress';
import MuiGlobalStyles from '@mui/material/GlobalStyles';
import Grid from '@mui/material/Grid';
import Paper from '@mui/material/Paper';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { num, silentPromise } from '@bookr-technologies/core';
import { getAuth } from '@bookr-technologies/firebase';
import { useEvent } from '@bookr-technologies/hooks';
import { palette } from '@bookr-technologies/ui';
import { useUserQuery } from '~/api';
import { AuthForm, AuthFormSteps } from '~/components/AuthForm';
import { useSession } from '~/components/SessionProvider';
import { setAuthSession } from '~/lib/session';
import { pipe, PropsOf } from '~/pipes/pipe';
import { withAuthenticated } from '~/pipes/withAuthenticated';
import { withSession } from '~/pipes/withSession';

const PaperStyle = {
    width: '100%',
    maxWidth: 600,
    display: 'flex',
};

const globalStyle = (
    <MuiGlobalStyles
        styles={{
            body: {
                backgroundColor: palette.extended.backgroundPrimary,
            },
        }}
    />
);

function AuthenticatePage({ redirect }: PropsOf<typeof getServerSideProps>) {
    const router = useRouter();
    const { session } = useSession();
    const [loading, setLoading] = useState(true);

    const handleSuccess = useEvent(async () => {
        const user = getAuth().currentUser;
        if (user) {
            await silentPromise(setAuthSession({ user, redirect }));
        }

        setLoading(false);
    });

    const userQuery = useUserQuery({
        skipConfiguredCheck: true,
        enabled: true,
        onSuccess: handleSuccess,
        onError: () => setLoading(false),
    });

    useEffect(() => {
        if (!session?.uid && userQuery.fetchStatus === 'idle') {
            setLoading(false);
        }
    }, [session, userQuery.fetchStatus]);

    return (
        <Grid
            container
            justifyContent={'center'}
            py={{ sm: 4, xs: 0 }}
            flexGrow={1}
        >
            {globalStyle}
            <Paper sx={PaperStyle} elevation={0}>
                {loading ? (
                    <Grid container justifyContent={'center'} padding={4}>
                        <CircularProgress />
                    </Grid>
                ) : (
                    <AuthForm
                        onSuccess={handleSuccess}
                        defaultStep={
                            'step' in router.query
                                ? num(router.query.step)
                                : AuthFormSteps.Default
                        }
                    />
                )}
            </Paper>
        </Grid>
    );
}

export const getServerSideProps = pipe(withSession(false), withAuthenticated);

export default AuthenticatePage;
