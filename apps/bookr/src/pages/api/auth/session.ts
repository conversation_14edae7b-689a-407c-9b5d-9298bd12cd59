import { unauthorized } from '@hapi/boom';
import { string } from 'yup';
import {
    createCookieSession,
    decodeCookieSession,
} from '@bookr-technologies/firebase/admin';
import { create<PERSON><PERSON><PERSON>, <PERSON>ttpHandler, HttpResponse } from '~/http';

class LoginHandler extends HttpHandler {
    protected async post(): Promise<HttpResponse> {
        const data = await this.validate({ token: string() });
        const headerToken = this.request.headers.authorization?.split(' ')[1];

        try {
            await createCookieSession(
                this.session,
                data.token || headerToken || '',
            );
        } catch (error) {
            console.error('Error creating session cookie:', { error });
            throw unauthorized('Unauthorized access');
        }

        return HttpResponse.ok();
    }

    protected async get() {
        const claims = await decodeCookieSession(this.session);

        return new HttpResponse({ uid: claims.uid, token: claims.token });
    }

    protected async delete() {
        await this.session.destroy();

        return HttpResponse.ok();
    }
}

export default createHandler(LoginHandler);
