import { captureError } from '@bookr-technologies/analytics';
import { getServerConfig } from '@bookr-technologies/config';
import { cleanSlashes, tryPromise } from '@bookr-technologies/core';
import { SitemapBusinessModel } from '@bookr-technologies/sdk';
import { NextAppContext } from '~/api';
import { withSession } from '~/pipes/withSession';

const DEFAULT_SITEMAP_URLS = `
    <url>
  <loc>https://bookr.ro/ro</loc>
  <changefreq>daily</changefreq>
  <priority>1.00</priority>
</url>
<url>
  <loc>https://bookr.ro/ro/beauty</loc>
  <changefreq>weekly</changefreq>
  <priority>0.80</priority>
</url>
<url>
  <loc>https://bookr.ro/preturi</loc>
  <changefreq>weekly</changefreq>
  <priority>0.80</priority>
</url>
<url>
  <loc>https://bookr.ro/documents/terms-and-conditions.pdf</loc>
  <changefreq>weekly</changefreq>
  <priority>0.80</priority>
</url>
<url>
  <loc>https://bookr.ro/documents/privacy-policy.pdf</loc>
  <changefreq>weekly</changefreq>
  <priority>0.80</priority>
</url>
<url>
  <loc>https://bookr.ro/ro/business</loc>
  <changefreq>weekly</changefreq>
  <priority>0.80</priority>
</url>
<url>
  <loc>https://bookr.ro/ro/despre</loc>
  <lastmod>2024-08-09T12:02:22+00:00</lastmod>
  <priority>0.64</priority>
</url>
<url>
  <loc>https://bookr.ro/ro/blog</loc>
  <changefreq>weekly</changefreq>
  <priority>0.64</priority>
</url>
<url>
  <loc>https://bookr.ro/ro/preturi</loc>
  <changefreq>weekly</changefreq>
  <priority>0.64</priority>
</url>
<url>
  <loc>https://bookr.ro/ro/noutati</loc>
  <changefreq>weekly</changefreq>
  <priority>0.64</priority>
</url>
<url>
  <loc>https://bookr.ro/ro/barber</loc>
  <lastmod>2024-08-09T12:02:22+00:00</lastmod>
  <priority>0.80</priority>
</url>
<url>
  <loc>https://bookr.ro/ro/masaj</loc>
  <lastmod>2024-08-09T12:02:22+00:00</lastmod>
  <priority>0.80</priority>
</url>
<url>
  <loc>https://bookr.ro/ro/dermatologie</loc>
  <lastmod>2024-08-09T12:02:22+00:00</lastmod>
  <priority>0.80</priority>
</url>
<url>
  <loc>https://bookr.ro/ro/stomatologie</loc>
  <lastmod>2024-08-09T12:02:22+00:00</lastmod>
  <priority>0.80</priority>
</url>
<url>
  <loc>https://bookr.ro/ro/oftalmologie</loc>
  <lastmod>2024-08-09T12:02:22+00:00</lastmod>
  <priority>0.80</priority>
</url>
<url>
  <loc>https://bookr.ro/ro/pet-grooming</loc>
  <lastmod>2024-08-09T12:02:22+00:00</lastmod>
  <priority>0.80</priority>
</url>
<url>
  <loc>https://bookr.ro/ro/psihologie</loc>
  <lastmod>2024-08-09T12:02:22+00:00</lastmod>
  <priority>0.80</priority>
</url>
<url>
  <loc>https://bookr.ro/ro/personal-trainer</loc>
  <lastmod>2024-08-09T12:02:22+00:00</lastmod>
  <priority>0.80</priority>
</url>
<url>
  <loc>https://bookr.ro/ro/post/bine-ai-venit-in-lumea-bookr-o-experienta-mai-buna-pentru-tine</loc>
  <lastmod>2024-08-09T12:02:22+00:00</lastmod>
  <priority>0.64</priority>
</url>
<url>
  <loc>https://bookr.ro/ro/faqs</loc>
  <changefreq>weekly</changefreq>
  <priority>0.64</priority>
</url>
<url>
  <loc>https://bookr.ro/ro/download</loc>
  <lastmod>2024-08-09T12:02:22+00:00</lastmod>
  <priority>0.64</priority>
</url>
<url>
  <loc>https://bookr.ro/en</loc>
  <changefreq>weekly</changefreq>
  <priority>0.64</priority>
</url>
<url>
  <loc>https://bookr.ro/ro/newsletter</loc>
  <changefreq>weekly</changefreq>
  <priority>0.64</priority>
</url>
<url>
  <loc>https://bookr.ro/en/beauty</loc>
  <lastmod>2024-08-09T12:02:22+00:00</lastmod>
  <priority>0.64</priority>
</url>
<url>
  <loc>https://bookr.ro/en/business</loc>
  <lastmod>2024-08-09T12:02:22+00:00</lastmod>
  <priority>0.51</priority>
</url>
<url>
  <loc>https://bookr.ro/ro/post/co-fondatorul-bookr-andrei-covaciu-apare-in-lista-forbes-30-under-30</loc>
  <lastmod>2024-08-09T12:02:22+00:00</lastmod>
  <priority>0.51</priority>
</url>
<url>
  <loc>https://bookr.ro/ro/post/revolutionand-industria-beauty-povestea-hair-community</loc>
  <lastmod>2024-08-09T12:02:22+00:00</lastmod>
  <priority>0.51</priority>
</url>
<url>
  <loc>https://bookr.ro/ro/post/cum-adaugi-poze-pentru-afacerea-ta-in-bookr</loc>
  <lastmod>2024-08-09T12:02:22+00:00</lastmod>
  <priority>0.51</priority>
</url>
<url>
  <loc>https://bookr.ro/ro/post/cum-adaugi-membri-de-personal-in-afacerea-ta-cu-bookr</loc>
  <lastmod>2024-08-09T12:02:22+00:00</lastmod>
  <priority>0.51</priority>
</url>
<url>
  <loc>https://bookr.ro/ro/post/cum-setezi-orele-tale-de-lucru-cu-bookr</loc>
  <lastmod>2024-08-09T12:02:22+00:00</lastmod>
  <priority>0.51</priority>
</url>
<url>
  <loc>https://bookr.ro/en/blog</loc>
  <changefreq>weekly</changefreq>
  <priority>0.51</priority>
</url>
<url>
  <loc>https://bookr.ro/en/pricing</loc>
  <changefreq>weekly</changefreq>
  <priority>0.51</priority>
</url>
<url>
  <loc>https://bookr.ro/ro/update/descopera-bookr-editia-de-toamna</loc>
  <lastmod>2024-08-09T12:02:22+00:00</lastmod>
  <priority>0.51</priority>
</url>
<url>
  <loc>https://bookr.ro/en/updates</loc>
  <lastmod>2024-08-09T12:02:22+00:00</lastmod>
  <priority>0.51</priority>
</url>
<url>
  <loc>https://bookr.ro/en/barber</loc>
  <lastmod>2024-08-09T12:02:22+00:00</lastmod>
  <priority>0.51</priority>
</url>
<url>
  <loc>https://bookr.ro/en/pet-grooming</loc>
  <lastmod>2024-08-09T12:02:22+00:00</lastmod>
  <priority>0.51</priority>
</url>
<url>
  <loc>https://bookr.ro/en/personal-trainer</loc>
  <lastmod>2024-08-09T12:02:22+00:00</lastmod>
  <priority>0.51</priority>
</url>
<url>
  <loc>https://bookr.ro/en/post/welcome-to-the-world-of-bookr-a-better-experience-for-you</loc>
  <lastmod>2024-08-09T12:02:22+00:00</lastmod>
  <priority>0.51</priority>
</url>
<url>
  <loc>https://bookr.ro/en/faqs</loc>
  <changefreq>weekly</changefreq>
  <priority>0.51</priority>
</url>
<url>
  <loc>https://bookr.ro/en/download</loc>
  <lastmod>2024-08-09T12:02:22+00:00</lastmod>
  <priority>0.51</priority>
</url>
<url>
  <loc>https://bookr.ro/en/newsletter</loc>
  <changefreq>weekly</changefreq>
  <priority>0.51</priority>
</url>
<url>
  <loc>https://bookr.ro/en/post/bookr-co-founder-andrei-covaciu-appears-in-forbes-30-under-30-list</loc>
  <lastmod>2024-08-09T12:02:22+00:00</lastmod>
  <priority>0.41</priority>
</url>
<url>
  <loc>https://bookr.ro/en/post/revolutionizing-the-beauty-industry-the-hair-community-story</loc>
  <lastmod>2024-08-09T12:02:22+00:00</lastmod>
  <priority>0.41</priority>
</url>
<url>
  <loc>https://bookr.ro/en/post/how-to-add-photos-for-your-business-to-bookr</loc>
  <lastmod>2024-08-09T12:02:22+00:00</lastmod>
  <priority>0.41</priority>
</url>
<url>
  <loc>https://bookr.ro/en/post/how-to-add-staff-members-to-your-business-with-bookr</loc>
  <lastmod>2024-08-09T12:02:22+00:00</lastmod>
  <priority>0.41</priority>
</url>
<url>
  <loc>https://bookr.ro/en/post/how-to-set-your-working-hours-with-bookr</loc>
  <lastmod>2024-08-09T12:02:22+00:00</lastmod>
  <priority>0.41</priority>
</url>
<url>
  <loc>https://bookr.ro/en/update/descopera-bookr-editia-de-toamna</loc>
  <lastmod>2024-08-09T12:02:22+00:00</lastmod>
  <priority>0.41</priority>
</url>
`;

function getChangeFreq(link: string) {
    if (link.startsWith('/b')) {
        return 'daily';
    }

    return 'weekly';
}

function createLinks(
    links: Set<string>,
    { baseUrl, locales }: Record<string, any>,
) {
    return [...links]
        .map((link) =>
            locales.reduce(
                (acc: string, locale: string) =>
                    `${acc}<url><loc>${cleanSlashes(
                        baseUrl,
                        locale,
                        link,
                    )}</loc><changefreq>${getChangeFreq(
                        link,
                    )}</changefreq></url>`,
                `<url><loc>${cleanSlashes(
                    baseUrl,
                    link,
                )}</loc><changefreq>${getChangeFreq(link)}</changefreq></url>`,
            ),
        )
        .join('');
}

function generateSiteMap({
    baseUrl,
    locales,
    sitemapBusinesses,
}: Record<string, any>) {
    if (!baseUrl.startsWith('http:/') && !baseUrl.startsWith('https:/')) {
        baseUrl = `https://${baseUrl}`;
    }

    const links = new Set([
        '',
        '/search',
        '/authenticate',
        '/beauty',
        '/blog',
        '/business',
    ]);

    sitemapBusinesses.reduce(
        (acc: Set<string>, business: SitemapBusinessModel) =>
            acc.add(`/b/${business.loc}`).add(`/${business.loc}`),
        links,
    );

    const urls = createLinks(links, { baseUrl, locales });

    return `<?xml version="1.0" encoding="UTF-8"?><urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">${DEFAULT_SITEMAP_URLS}${urls}</urlset>`;
}

function SiteMap() {
    // getServerSideProps will do the heavy lifting
}

export async function getServerSideProps(ctx: NextAppContext) {
    await withSession()(ctx);

    const [sitemapBusinessesResult, err] = await tryPromise(
        ctx.apiClients.bookr.businesses.getSitemapBusinesses(),
    );

    if (err) {
        captureError(new Error(`unable to fetch sitemap businesses: ${err}`));
    }

    const sitemapBusinesses = sitemapBusinessesResult?.data ?? [];
    const sitemap = generateSiteMap({
        baseUrl: `${
            ctx.req.headers?.host?.includes('bookr')
                ? ctx.req.headers.host
                : getServerConfig().urls.web
        }`,
        locales: ctx.locales,
        sitemapBusinesses,
    });

    ctx.res.setHeader('Content-Type', 'text/xml');
    ctx.res.write(sitemap);
    ctx.res.end();

    return {
        props: {},
    };
}

export default SiteMap;
