import Paper from '@mui/material/Paper';
import Typography from '@mui/material/Typography';
import { AccountLayout } from '~/components/AccountLayout';
import { pipe } from '~/pipes/pipe';
import { withSession } from '~/pipes/withSession';
import { withUser } from '~/pipes/withUser';

function AccountFeedbackPage() {
    return (
        <AccountLayout>
            <Paper sx={{ p: 4 }}>
                <Typography variant={'title1'} fontWeight={800}>
                    Feedback
                </Typography>
            </Paper>
        </AccountLayout>
    );
}

export const getServerSideProps = pipe(withSession(true), withUser());

export default AccountFeedbackPage;
