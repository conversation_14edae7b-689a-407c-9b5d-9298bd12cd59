import Grid from '@mui/material/Grid';
import Paper from '@mui/material/Paper';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { Form, Formik, FormikHelpers } from 'formik';
import { str } from '@bookr-technologies/core';
import {
    changePassword,
    FirebaseErrorCode,
    isFirebaseError,
    matchFirebaseError,
} from '@bookr-technologies/firebase';
import {
    useDialog,
    useEvent,
    useNotification,
    useValidationSchema,
} from '@bookr-technologies/hooks';
import { useI18n } from '@bookr-technologies/i18n';
import {
    FormikButton,
    FormikTextField,
} from '@bookr-technologies/ui/components';
import { useUserQuery } from '~/api';
import { AccountLayout } from '~/components/AccountLayout';
import { NotificationDialog } from '~/components/NotificationDialog';
import { pipe } from '~/pipes/pipe';
import { withSession } from '~/pipes/withSession';
import { withUser } from '~/pipes/withUser';

const initialValues = {
    oldPassword: '',
    newPassword: '',
    confirmPassword: '',
};

function AccountSecurityPage() {
    const { t } = useI18n();
    const user = useUserQuery();
    const notification = useNotification();
    const notificationDialog = useDialog();

    const validation = useValidationSchema((yup) => ({
        oldPassword: yup.string().required(t('requiredField')),
        newPassword: yup
            .string()
            .required(t('requiredField'))
            .min(8, t('expectedMinLength', { length: 8 })),
        confirmPassword: yup
            .string()
            .required(t('requiredField'))
            .oneOf([yup.ref('newPassword')], t('passwordsDontMatch')),
    }));

    const handleSubmit = useEvent(
        async (
            { oldPassword, newPassword }: typeof initialValues,
            helpers: FormikHelpers<any>,
        ) => {
            try {
                await changePassword(
                    str(user.data?.email),
                    oldPassword,
                    newPassword,
                );
                notificationDialog.open();
                helpers.resetForm({
                    values: initialValues,
                });
            } catch (error) {
                let message = t('somethingWentWrong');
                if (isFirebaseError(error)) {
                    message = matchFirebaseError(error, {
                        default: message,
                        [FirebaseErrorCode.WrongPassword]: t('wrongPassword'),
                    });
                }

                notification.error(message);
            }
        },
    );

    return (
        <AccountLayout>
            <Stack component={Paper} p={{ xs: 2, md: 4 }} spacing={3}>
                <Stack>
                    <Typography variant={'title1'} fontWeight={800}>
                        {t('changePassword')}
                    </Typography>
                    <Typography
                        variant={'footnote'}
                        fontWeight={500}
                        color={'textSecondary'}
                    >
                        {t('changePasswordMessage')}
                    </Typography>
                </Stack>
                <Formik
                    initialValues={initialValues}
                    onSubmit={handleSubmit}
                    {...validation}
                >
                    <Stack component={Form} spacing={2}>
                        <FormikTextField
                            type={'password'}
                            name={'oldPassword'}
                            label={t('oldPassword')}
                        />
                        <Stack>
                            <Typography
                                variant={'footnote'}
                                fontWeight={500}
                                color={'textSecondary'}
                                mb={1}
                            >
                                {t('newPasswordRequirements')}
                            </Typography>
                            <FormikTextField
                                type={'password'}
                                name={'newPassword'}
                                label={t('newPassword')}
                            />
                        </Stack>
                        <FormikTextField
                            type={'password'}
                            name={'confirmPassword'}
                            label={t('confirmPassword')}
                        />

                        <Grid container justifyContent={'flex-end'} pt={1}>
                            <FormikButton
                                variant={'contained'}
                                color={'info'}
                                validateDirty
                            >
                                {t('changePassword')}
                            </FormikButton>
                        </Grid>
                    </Stack>
                </Formik>
            </Stack>

            <NotificationDialog
                title={t('yourPasswordChangedSuccessfully')}
                message={t('yourPasswordChangedSuccessfullyDescription')}
                buttonLabel={t('gotIt')}
                {...notificationDialog.props}
            />
        </AccountLayout>
    );
}

export const getServerSideProps = pipe(withSession(true), withUser());

export default AccountSecurityPage;
