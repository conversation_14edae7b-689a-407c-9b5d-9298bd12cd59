import Button from '@mui/material/Button';
import Grid from '@mui/material/Grid';
import Paper from '@mui/material/Paper';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { Form, Formik } from 'formik';
import { useMemo } from 'react';
import { str } from '@bookr-technologies/core';
import {
    useDialog,
    useEvent,
    useNotification,
    useValidationSchema,
} from '@bookr-technologies/hooks';
import { useI18n } from '@bookr-technologies/i18n';
import {
    AvatarUploader,
    FormikButton,
    FormikPhoneNumber,
    FormikTextField,
} from '@bookr-technologies/ui/components';
import { FormikLanguage } from '@bookr-technologies/ui/components/Formik/FormikLanguage';
import {
    useUpdateUserMutation,
    useUploadUserPhotoMutation,
    useUserQuery,
} from '~/api';
import { AccountLayout } from '~/components/AccountLayout';
import { DeleteAccountDialog } from '~/components/DeleteAccountDialog';
import { pipe } from '~/pipes/pipe';
import { withSession } from '~/pipes/withSession';
import { withUser } from '~/pipes/withUser';

function AccountProfilePage() {
    const { t } = useI18n();
    const notification = useNotification();
    const user = useUserQuery();
    const updateUser = useUpdateUserMutation();
    const uploadUserPhoto = useUploadUserPhotoMutation();
    const deleteDialog = useDialog();
    const validation = useValidationSchema((y) => ({
        displayName: y.string().required(t('requiredField')),
        phoneNumber: y.string().required(t('requiredField')),
        language: y.string().required(t('requiredField')),
    }));

    const initialValues = useMemo(
        () => ({
            displayName: str(user.data?.displayName),
            phoneNumber: str(user.data?.phoneNumber),
            email: str(user.data?.email),
            language: str(user.data?.language),
        }),
        [
            user.data?.displayName,
            user.data?.email,
            user.data?.language,
            user.data?.phoneNumber,
        ],
    );

    const handleAvatarUpload = useEvent(async (file: File) => {
        await uploadUserPhoto.mutateAsync({ file });
    });

    const handleSubmit = useEvent(async (values: typeof initialValues) => {
        try {
            await updateUser.mutateAsync({
                displayName: values.displayName,
                phoneNumber: values.phoneNumber,
                language: values.language,
            });
        } catch (e) {
            notification.error(t('somethingWentWrong'));
        }
    });

    return (
        <AccountLayout>
            <Paper component={Stack} p={{ xs: 2, md: 4 }} mb={2}>
                <Stack
                    justifyContent={'flex-start'}
                    alignItems={'flex-start'}
                    gap={2}
                >
                    <Typography variant={'title1'} fontWeight={800}>
                        {t('myProfile')}
                    </Typography>

                    <AvatarUploader
                        src={user.data?.photoURL}
                        size={80}
                        onChange={handleAvatarUpload}
                    />
                </Stack>
                <Formik
                    initialValues={initialValues}
                    onSubmit={handleSubmit}
                    enableReinitialize
                    {...validation}
                >
                    <Stack component={Form} gap={3} mt={3}>
                        <Stack gap={2}>
                            <Typography
                                variant={'footnote'}
                                fontWeight={600}
                                color={'textSecondary'}
                            >
                                {t('personalDetails')}
                            </Typography>

                            <FormikTextField
                                name={'displayName'}
                                label={t('displayName')}
                            />
                            <FormikPhoneNumber
                                name={'phoneNumber'}
                                label={t('phoneNumber')}
                            />
                            <FormikTextField
                                readOnly
                                disabled
                                name={'email'}
                                label={t('email')}
                            />
                            <FormikLanguage
                                name={'language'}
                                label={t('language')}
                            />
                        </Stack>

                        <Grid container justifyContent={'flex-end'}>
                            <FormikButton
                                variant={'contained'}
                                color={'info'}
                                validateDirty
                            >
                                {t('saveChanges')}
                            </FormikButton>
                        </Grid>
                    </Stack>
                </Formik>
            </Paper>

            <Paper component={Stack} p={4} gap={2}>
                <Stack gap={1}>
                    <Typography variant={'body'} fontWeight={800}>
                        {t('deleteYourAccount')}
                    </Typography>
                    <Typography
                        variant={'subhead'}
                        fontWeight={600}
                        color={'textSecondary'}
                    >
                        {t('deleteYourAccountMessage')}
                    </Typography>
                </Stack>

                <Grid container>
                    <Button
                        variant={'subtle'}
                        color={'error'}
                        onClick={deleteDialog.open}
                    >
                        {t('deleteMyAccount')}
                    </Button>
                    <DeleteAccountDialog {...deleteDialog.props} />
                </Grid>
            </Paper>
        </AccountLayout>
    );
}

export const getServerSideProps = pipe(withSession(true), withUser());

export default AccountProfilePage;
