import CircularProgress from '@mui/material/CircularProgress';
import Container from '@mui/material/Container';
import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import { Theme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import dynamic from 'next/dynamic';
import { useMemo } from 'react';
import { useEvent } from '@bookr-technologies/hooks';
import {
    collectPages,
    useBusinessFavoriteMutation,
    useBusinessFavoriteQuery,
    useBusinessQuery,
    useBusinessReviewsQuery,
} from '~/api';
import { BookNowDialog } from '~/components/BookNowButton';
import {
    DetailsCard,
    Gallery,
    InfoCard,
    ReportCard,
    Reviews,
    Services,
} from '~/components/BusinessBlocks';
import { NonBookrServices } from '~/components/BusinessBlocks/NonBookrServices';
import { pipe, pipeAsync, PropsOf } from '~/pipes/pipe';
import { withBusinessPage } from '~/pipes/withBusinessPage';
import { withSession } from '~/pipes/withSession';
import { withUser } from '~/pipes/withUser';

const Header = dynamic(
    async () => {
        const { Header } = await import('~/components/BusinessBlocks/Header');
        return Header;
    },
    {
        ssr: false,
    },
);

function BusinessViewPage(props: PropsOf<typeof getServerSideProps>) {
    const businessQuery = useBusinessQuery(props.businessId);
    const businessReviewsQuery = useBusinessReviewsQuery(props.businessId, {
        pageable: { page: 0, size: 10 },
    });
    const businessFavoriteQuery = useBusinessFavoriteQuery(props.businessId);
    const favoriteMutation = useBusinessFavoriteMutation();
    const isDownSm = useMediaQuery<Theme>((theme) =>
        theme.breakpoints.down('sm'),
    );

    const { data: business } = businessQuery;
    const { data: favorite } = businessFavoriteQuery;
    const reviews = collectPages(businessReviewsQuery.data);

    const isFavorite = !!favorite?.id;
    const totalReviewPages = reviews?.totalPages ?? 0;

    const handleFavorite = useEvent(async () => {
        try {
            await favoriteMutation.mutateAsync({
                businessId: props.businessId,
                action: isFavorite ? 'remove' : 'add',
            });
            await businessFavoriteQuery.refetch();
        } catch (e) {
            // TODO: treat error
        }
    });

    const isInstantBooking = useMemo(
        () =>
            !!business?.staffMembers?.find((staff) => !!staff.services.length),
        [business?.staffMembers],
    );

    const isLoading = businessQuery.isLoading || businessReviewsQuery.isLoading;

    if (isLoading) {
        return (
            <Stack
                padding={5}
                alignItems={'center'}
                justifyContent={'center'}
                flexGrow={1}
            >
                <CircularProgress
                    size={32}
                    thickness={6}
                    sx={{ opacity: 0.5 }}
                />
            </Stack>
        );
    }

    return (
        <>
            <BookNowDialog />
            <Header
                businessId={business?.id}
                businessName={business?.name}
                rate={business?.reviewInfo?.averageRating}
                noOfReviews={business?.reviewInfo?.noOfReviews}
                isFavorite={isFavorite}
                onFavorite={handleFavorite}
            />
            <Container disableGutters={isDownSm}>
                <Grid
                    container
                    gap={2.5}
                    wrap={'nowrap'}
                    mt={{ sm: 3 }}
                    flexDirection={{ xs: 'column', md: 'row' }}
                >
                    {business?.photos?.length ? (
                        <Grid item xs={12} md={7} container>
                            <Gallery images={business?.photos ?? []} />
                        </Grid>
                    ) : null}

                    <Grid item xs={12} md={business?.photos?.length ? 5 : 12}>
                        <DetailsCard
                            title={business?.name}
                            businessId={business?.id}
                            businessName={business?.name}
                            phoneNumber={business?.phoneNumber}
                            formattedAddress={business?.formattedAddress}
                            description={business?.description}
                            averageRating={business?.reviewInfo?.averageRating}
                            noOfReviews={business?.reviewInfo?.noOfReviews}
                            virtualTour={business?.virtualTourURL}
                            isInstantBooking={isInstantBooking}
                            withImage={!!business?.photos?.length}
                        />
                    </Grid>
                </Grid>

                <Grid
                    container
                    flexDirection={{ xs: 'column-reverse', md: 'row' }}
                    alignItems={'flex-start'}
                    gap={2.5}
                    wrap={'nowrap'}
                    py={2.5}
                    pb={5}
                >
                    <Grid
                        item
                        xs={12}
                        md={8}
                        container
                        justifyContent={'flex-start'}
                        flexDirection={'column'}
                        gap={2}
                    >
                        {isInstantBooking ? (
                            <Services
                                businessId={business?.id ?? ''}
                                staffMembers={business?.staffMembers}
                            />
                        ) : (
                            <NonBookrServices
                                businessId={business?.id ?? ''}
                                businessName={business?.name ?? ''}
                                phoneNumber={business?.phoneNumber ?? ''}
                            />
                        )}

                        <Reviews
                            averageRating={business?.reviewInfo?.averageRating}
                            noOfReviews={business?.reviewInfo?.noOfReviews}
                            reviews={reviews?.content?.slice(0, 10) ?? []}
                            businessId={business?.id ?? ''}
                            hasMore={totalReviewPages > 1}
                        />
                    </Grid>

                    <Grid
                        item
                        xs={12}
                        md={4}
                        container
                        justifyContent={'flex-start'}
                        flexDirection={'column'}
                        gap={2}
                    >
                        <InfoCard
                            phone={business?.phoneNumber}
                            website={business?.websiteURL}
                            instagram={business?.instagramURL}
                            facebook={business?.facebookURL}
                            workingHours={business?.workingHours}
                            hideProgram={!isInstantBooking}
                            timezone={business?.zoneId}
                        />
                        {business?.id ? (
                            <ReportCard businessId={business?.id} />
                        ) : null}
                    </Grid>
                </Grid>
            </Container>
        </>
    );
}

export const getServerSideProps = pipe(
    withSession(),
    pipeAsync(withUser(), withBusinessPage()),
);

export default BusinessViewPage;
