import { preloadGetCategories } from '~/api';
import { HomePageContainer } from '~/containers/HomePageContainer';
import { pipe, pipeAsync } from '~/pipes/pipe';
import { withPreload } from '~/pipes/withPreload';
import { withSession } from '~/pipes/withSession';
import { withUser } from '~/pipes/withUser';

function HomePage() {
    return <HomePageContainer />;
}

export const getServerSideProps = pipe(
    withSession(),
    withUser(),
    pipeAsync(
        withPreload(preloadGetCategories),
        // withPreload(preloadGetBestBusinesses),
        // withPreload(preloadGetNearbyBusinesses),
        // withPreload(preloadGetRecentBusinesses),
        // withPreload(preloadGetBookedBusinesses),
    ),
);

export default HomePage;
