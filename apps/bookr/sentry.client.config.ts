import * as Sentry from '@sentry/nextjs';
import { isAnalyticsEnabled } from '@bookr-technologies/analytics';
import { getClientConfig } from '@bookr-technologies/config';

const { analytics, production } = getClientConfig();

if (isAnalyticsEnabled('sentry')) {
    Sentry.init({
        dsn: analytics.sentry.dsn,
        tracesSampleRate: 1,
        debug: !production,
        replaysOnErrorSampleRate: 1.0,
        replaysSessionSampleRate: 0.1,
        integrations: [
            new Sentry.Replay({
                // Additional Replay configuration goes in here, for example:
                maskAllText: true,
                blockAllMedia: true,
            }),
        ],
    });
}
