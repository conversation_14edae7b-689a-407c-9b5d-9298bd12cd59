FROM node:20-alpine AS builder

ARG APP_VERSION
ARG APP_REVISION
ENV APP_VERSION=$APP_VERSION
ENV APP_REVISION=$APP_REVISION

RUN apk add --no-cache libc6-compat
RUN apk update

# Set working directory
WORKDIR /app

RUN yarn global add turbo@1.9.6

COPY . .

RUN turbo prune --scope=bookr --docker

# Add lockfile and package.json's of isolated subworkspace
FROM node:20-alpine AS installer

ARG APP_VERSION
ARG APP_REVISION
ENV APP_VERSION=$APP_VERSION
ENV APP_REVISION=$APP_REVISION

RUN apk add --no-cache libc6-compat
RUN apk update
WORKDIR /app

# First install the dependencies (as they change less often)
COPY .gitignore .gitignore
COPY --from=builder /app/out/json/ .
COPY --from=builder /app/out/yarn.lock ./yarn.lock
RUN yarn install

# Build the project
COPY --from=builder /app/out/full/ .
COPY --from=builder /app/secrets ./secrets

RUN rm -f ./apps/bookr/.env.local

RUN yarn turbo run build

FROM node:20-alpine AS runner
WORKDIR /app

ARG APP_VERSION
ARG APP_REVISION
ENV APP_VERSION=$APP_VERSION
ENV APP_REVISION=$APP_REVISION

RUN apk --update add --no-cache redis supervisor

# Don't run production as root
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs
USER nextjs

COPY --from=installer /app/apps/bookr/next.config.js .
COPY --from=installer /app/apps/bookr/package.json .

# Automatically leverage output traces to reduce image size
# https://nextjs.org/docs/advanced-features/output-file-tracing
COPY --from=installer --chown=nextjs:nodejs /app/apps/bookr/.next/standalone ./
COPY --from=installer --chown=nextjs:nodejs /app/apps/bookr/.next/static ./apps/bookr/.next/static
COPY --from=installer --chown=nextjs:nodejs /app/apps/bookr/public ./apps/bookr/public
COPY ./apps/bookr/.docker/supervisord.conf /etc/supervisord.conf

USER root

ENV PORT '80'

EXPOSE 80

CMD ["/usr/bin/supervisord", "-c", "/etc/supervisord.conf"]
