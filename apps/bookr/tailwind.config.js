const bookr = {
    radius: {
        md: 'var(--radius-md)',
        lg: 'var(--radius-lg)',
    },

    spacing: {
        sm: 'var(--spacing-sm)',
        md: 'var(--spacing-md)',
        xl: 'var(--spacing-xl)',
    },

    colors: {
        brand: 'var(--colors-brand)',
        bg: {
            primary: 'var(--background-primary)',
            secondary: 'var(--background-secondary)',
            tertiary: 'var(--background-tertiary)',
        },
        content: {
            primary: 'var(--content-primary)',
            secondary: 'var(--content-secondary)',
            tertiary: 'var(--content-tertiary)',
            inverse: 'var(--content-inverse)',
        },
    },
};

/** @type {import('tailwindcss').Config} */
module.exports = {
    darkMode: ['class'],
    content: ['./src/**/*.{js,ts,jsx,tsx,mdx}'],
    theme: {
        extend: {
            spacing: {
                ...bookr.spacing,
            },
            borderRadius: {
                lg: 'var(--radius)',
                sm: 'calc(var(--radius) - 4px)',
                ...bookr.radius,
            },
            colors: {
                background: 'hsl(var(--background))',
                foreground: 'hsl(var(--foreground))',
                card: {
                    DEFAULT: 'hsl(var(--card))',
                    foreground: 'hsl(var(--card-foreground))',
                },
                popover: {
                    DEFAULT: 'hsl(var(--popover))',
                    foreground: 'hsl(var(--popover-foreground))',
                },
                primary: {
                    DEFAULT: 'hsl(var(--primary))',
                    foreground: 'hsl(var(--primary-foreground))',
                },
                secondary: {
                    DEFAULT: 'hsl(var(--secondary))',
                    foreground: 'hsl(var(--secondary-foreground))',
                },
                muted: {
                    DEFAULT: 'hsl(var(--muted))',
                    foreground: 'hsl(var(--muted-foreground))',
                },
                accent: {
                    DEFAULT: 'hsl(var(--accent))',
                    foreground: 'hsl(var(--accent-foreground))',
                },
                destructive: {
                    DEFAULT: 'hsl(var(--destructive))',
                    foreground: 'hsl(var(--destructive-foreground))',
                },
                border: 'hsl(var(--border))',
                input: 'hsl(var(--input))',
                ring: 'hsl(var(--ring))',
                chart: {
                    '1': 'hsl(var(--chart-1))',
                    '2': 'hsl(var(--chart-2))',
                    '3': 'hsl(var(--chart-3))',
                    '4': 'hsl(var(--chart-4))',
                    '5': 'hsl(var(--chart-5))',
                },
                ...bookr.colors,
            },
        },
    },
    plugins: [require('tailwindcss-animate')],
};
