{"name": "bookr", "version": "0.18.1", "private": true, "scripts": {"test": "jest --<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@bookr-technologies/analytics": "*", "@bookr-technologies/config": "*", "@bookr-technologies/core": "*", "@bookr-technologies/firebase": "*", "@bookr-technologies/hooks": "*", "@bookr-technologies/i18n": "*", "@bookr-technologies/sdk": "*", "@bookr-technologies/types": "*", "@bookr-technologies/ui": "*", "@emotion/react": "^11.11.0", "@emotion/server": "^11.11.0", "@emotion/styled": "^11.11.0", "@fontsource/plus-jakarta-sans": "^5.0.0", "@googlemaps/js-api-loader": "^1.16.2", "@hapi/boom": "^10.0.1", "@mui/icons-material": "^5.11.16", "@mui/lab": "^5.0.0-alpha.129", "@mui/material": "^5.13.0", "@mui/x-date-pickers": "^6.4.0", "@radix-ui/react-slot": "^1.1.0", "@sentry/nextjs": "^7.52.1", "@tanstack/react-query": "^4.36.1", "@vis.gl/react-google-maps": "^1.4.0", "axios": "^1.4.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "connect-redis": "^7.1.0", "cookies": "^0.8.0", "date-fns": "^2.30.0", "date-fns-tz": "^1.3.8", "express-session": "^1.17.3", "firebase": "^10.0.0", "formik": "^2.2.9", "ioredis": "^5.3.2", "jotai": "^2.1.0", "keen-slider": "^6.8.5", "libphonenumber-js": "^1.10.30", "lucide-react": "^0.454.0", "next": "13.5.5", "next-pwa": "^5.6.0", "next-session": "^4.0.5", "nprogress": "^0.2.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-is": "^18.2.0", "sharp": "^0.32.3", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "uuid": "^9.0.0", "yup": "^1.1.1"}, "devDependencies": {"@bookr-technologies/eslint-config-custom": "*", "@bookr-technologies/tsconfig": "*", "@types/cookies": "^0.7.7", "@types/google.maps": "^3.53.4", "@types/nprogress": "^0.2.0", "@types/react": "^18.2.6", "@types/react-is": "^18.2.1", "@types/uuid": "^9.0.2", "autoprefixer": "^10.4.20", "postcss": "^8.4.47", "tailwindcss": "^3.4.14"}}