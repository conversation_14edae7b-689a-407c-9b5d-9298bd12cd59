import { GluegunToolbox } from 'gluegun';
import { GluegunTemplateGenerateOptions } from 'gluegun/build/types/toolbox/template-types';
import * as path from 'path';

declare global {
    interface CliToolbox extends GluegunToolbox {
        generateTemplate(
            options: GluegunTemplateGenerateOptions & {
                force?: boolean;
                skipIfExists?: boolean;
            },
        ): Promise<string>;
    }
}

function TemplatePlugin(toolbox: CliToolbox) {
    toolbox.generateTemplate = async ({ force, skipIfExists, ...options }) => {
        const exists = await toolbox.filesystem.existsAsync(options.target);
        if (exists && !force) {
            if (skipIfExists) {
                return;
            }

            toolbox.fatal(
                `File ${path.relative(
                    toolbox.baseDir,
                    options.target,
                )} already exists`,
            );
        }

        const result = await toolbox.template.generate(options);
        toolbox.print.success(
            `Generated ${path.relative(toolbox.baseDir, options.target)}`,
        );

        toolbox.prettier(options.target);

        return result;
    };
}

export default TemplatePlugin;
