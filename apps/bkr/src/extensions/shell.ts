import * as execa from 'execa';
import { GluegunToolbox } from 'gluegun';
import { PromptOptions } from 'gluegun/build/types/toolbox/prompt-enquirer-types';
import { get } from 'lodash';

type PromptArgument = PromptOptions & {
    source: readonly (
        | `${keyof CliToolbox['parameters']}`
        | `array.${number}`
        | `options.${string}`
        | string
    )[];
    ask?: false;
};

type FlagRecord<
    A extends any[],
    T = A extends [infer F, ...infer R]
        ? F extends { name: infer K; type: infer V }
            ? K extends string
                ? {
                      [U in K]: V extends 'confirm' ? boolean : string;
                  } & FlagRecord<R>
                : FlagRecord<R>
            : FlagRecord<R>
        : // eslint-disable-next-line @typescript-eslint/ban-types
          {},
> = T extends never ? Record<string, any> : T;

declare global {
    interface CliToolbox extends GluegunToolbox {
        fatal(message: string, status?: number): void;

        flag<A extends PromptArgument[]>(...prompts: A): Promise<FlagRecord<A>>;

        runScript(
            cmd: string,
            args?: string[],
            options?: Partial<execa.Options & { mute: boolean }>,
        ): execa.ExecaChildProcess;
    }
}

function ShellPlugin(toolbox: CliToolbox) {
    toolbox.fatal = (message: string, status = 1) => {
        toolbox.print.error(message);
        process.exit(status);
    };

    toolbox.flag = async (...prompts) => {
        const transform = (type: any, value: any) => {
            switch (type) {
                case 'numeral':
                    return Number(value);
                default:
                    return value;
            }
        };

        const values: any = {};
        for (const options of prompts) {
            const { source, ask, ...prompt } = options;
            const name =
                typeof prompt.name === 'function' ? prompt.name() : prompt.name;
            let value = source
                .map(
                    (key) =>
                        get(toolbox.parameters.options, key) ||
                        get(toolbox.parameters, key),
                )
                .find((v) => v !== undefined);

            if (!value && ask !== false) {
                const result = await toolbox.prompt.ask(prompt as any);
                value = result[name];
            }

            values[name] = transform(prompt.type, value);
        }

        return values;
    };

    toolbox.runScript = (cmd, args = [], options = {}) => {
        let opts = { ...options } as Partial<
            execa.Options & {
                mute?: boolean;
            }
        >;

        if (!opts.stdout && !opts.stdio && !opts.stderr && !opts.stdio) {
            opts = {
                ...opts,
                stdio: 'inherit',
            };
        }

        if (cmd === 'yarn') {
            args = ['--silent', ...args];
        }

        if (!opts.mute) {
            toolbox.print.muted(`$ ${cmd} ${args.join(' ')}`);
        }

        return execa(cmd, args, opts);
    };
}

export default ShellPlugin;
