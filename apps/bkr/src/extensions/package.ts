import { GluegunToolbox } from 'gluegun';
import { Choice } from 'gluegun/build/types/toolbox/prompt-enquirer-types';
import * as path from 'path';

enum PackageTypeEnum {
    Application = 'application',
    Library = 'library',
}

interface Package {
    name: string;
    dirname: string;
    version: string;
    directory: string;
    packageType: PackageTypeEnum;
}

declare global {
    interface CliToolbox extends GluegunToolbox {
        baseDir: string;

        getPackageJson(): Record<string, any>;

        ensureDirectory(...path: string[]): void;

        packageDirectory(pkgName?: string, ...path: string[]): string;

        appDirectory(appName?: string, ...path: string[]): string;

        getPackages(): Package[];

        packageFlag(): Promise<Package>;
    }
}

export default (toolbox: CliToolbox) => {
    toolbox.baseDir = toolbox.filesystem.path(__dirname, '../../../../');

    toolbox.getPackageJson = () =>
        require(toolbox.filesystem.path(toolbox.baseDir, 'package.json'));

    toolbox.ensureDirectory = (...path: string[]) => {
        const dir = toolbox.filesystem.path(__dirname, ...path);
        if (!toolbox.filesystem.exists(dir)) {
            toolbox.filesystem.dir(dir);
        }
    };

    toolbox.packageDirectory = (pkgName?: string, ...path: string[]) => {
        if (pkgName) {
            path = [pkgName, ...path];
        }

        return toolbox.filesystem.path('packages', ...path);
    };

    toolbox.appDirectory = (appName?: string, ...path: string[]) => {
        if (appName) {
            path = [appName, ...path];
        }

        return toolbox.filesystem.path('apps', ...path);
    };

    toolbox.getPackages = (skipCli = true) => {
        const apps = toolbox.filesystem.subdirectories(toolbox.appDirectory());
        const libs = toolbox.filesystem.subdirectories(
            toolbox.packageDirectory(),
        );

        return [...apps, ...libs]
            .filter((dir) => !(skipCli && path.basename(dir) === 'bkr'))
            .sort((a, b) => a.localeCompare(b))
            .map((directory) => {
                const dir = toolbox.filesystem.dir(directory);
                const packageJson = dir.read('package.json', 'json');
                return {
                    name: packageJson.name,
                    dirname: path.basename(directory),
                    version: packageJson.version,
                    packageType:
                        path.basename(path.dirname(directory)) === 'apps'
                            ? PackageTypeEnum.Application
                            : PackageTypeEnum.Library,
                    directory,
                };
            });
    };

    toolbox.packageFlag = async () => {
        const packages = toolbox.getPackages();
        const { pkg } = await toolbox.flag({
            name: 'pkg' as const,
            type: 'select',
            source: ['pkg', 'package', 'p'],
            message: 'Select package',
            sort: true,
            choices: packages.map(
                (pkg): Choice => ({
                    name: pkg.name,
                    message: `• ${pkg.name}`,
                    hint: `${pkg.packageType}, ${pkg.version}`,
                }),
            ),
        });

        const packageOption = packages.find(
            (p) => p.name === pkg || p.name.split('/')[1] === pkg,
        );
        if (!packageOption) {
            toolbox.fatal(`Package ${pkg} not found`);
        }

        return packageOption;
    };
};
