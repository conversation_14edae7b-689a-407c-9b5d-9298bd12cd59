import { GluegunToolbox } from 'gluegun';

declare global {
    interface CliToolbox extends GluegunToolbox {
        prettySet: Set<string>;
        prettier(file: string): void;
        prettify(): Promise<void>;
    }
}

function prettierPlugin(toolbox: CliToolbox) {
    toolbox.prettySet = new Set();
    toolbox.prettier = (file: string) => toolbox.prettySet.add(file);
    toolbox.prettify = async () => {
        if (toolbox.prettySet.size) {
            const files = Array.from(toolbox.prettySet.values());
            try {
                toolbox.print.highlight('Prettifying files...');
                await toolbox.runScript(
                    'yarn',
                    ['prettier', '--write', ...files],
                    { mute: true },
                );
                toolbox.prettySet.clear();
            } catch (e) {
                toolbox.print.error(
                    `Something went wrong while prettifying files: ${e.message}`,
                );
            }
        }
    };
}

export default prettierPlugin;
