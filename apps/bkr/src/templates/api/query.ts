import { useQuery } from '@tanstack/react-query';
import {<%= props.name %>Keys } from '~/api';

export const <%= props.name %>Queries = {
    <%= props.queryMethodName %>() {
        return [];
    },
};

export function use<%= props.queryName %>Query() {
    return useQuery({
        queryKey: <%= props.name %>Keys.<%= props.queryMethodName %>(),
        queryFn: () => <%= props.name %>Queries.<%= props.queryMethodName %>(),
    });
}
