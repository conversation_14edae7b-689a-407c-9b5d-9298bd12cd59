import { useMutation } from '@tanstack/react-query';
import {<%= props.name %>Keys } from '~/api';

export const <%= props.name %>Queries = {
    <%= props.mutationMethodName %>() {
        return [];
    },
};

export function use<%= props.mutationName %>Mutation() {
    return useMutation({
        mutationKey: <%= props.name %>Keys.<%= props.mutationMethodName %>(),
        mutationFn: () => <%= props.name %>Queries.<%= props.mutationMethodName %>(),
    });
}
