import { GluegunCommand } from 'gluegun';

const command: GluegunCommand = {
    name: 'mutation',
    async run(toolbox: CliToolbox) {
        const pkg = toolbox.appDirectory('bookr', 'src/api');

        const { name, mutationName } = await toolbox.flag(
            {
                name: 'name' as const,
                message: 'Mutation namespace',
                type: 'input',
                source: ['first', 'namespace', 'n'],
            },
            {
                name: 'mutationName' as const,
                message: 'Mutation name',
                type: 'input',
                ask: false,
                source: ['second', 'mutationName', 'q'],
            },
        );

        const namespace = toolbox.strings.camelCase(name);
        const props = {
            namespace,
            name,
            mutationMethodName: toolbox.strings.camelCase(mutationName || name),
            mutationName: toolbox.strings.pascalCase(mutationName || name),
        };

        toolbox.print.highlight(
            `Generating mutation ${props.mutationMethodName}@use${props.mutationName}Mutation for ${namespace}...`,
        );

        await toolbox.generateTemplate({
            props,
            template: 'api/mutation.ts',
            target: toolbox.filesystem
                .dir(pkg)
                .path('mutations', `${namespace}.ts`),
            skipIfExists: true,
        });

        await toolbox.generateTemplate({
            props,
            template: 'api/keys.ts',
            target: toolbox.filesystem.dir(pkg).path('keys', `${namespace}.ts`),
            skipIfExists: true,
        });

        await toolbox.runtime.run('generate index');
    },
};

export default command;
