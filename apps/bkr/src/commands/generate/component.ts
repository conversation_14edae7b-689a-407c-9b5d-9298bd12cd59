import { GluegunCommand } from 'gluegun';

const command: GluegunCommand = {
    name: 'component',
    alias: ['c'],
    async run(toolbox: CliToolbox) {
        const { directory: packageDir } = await toolbox.packageFlag();

        const { name, isStyled, hasProps } = await toolbox.flag(
            {
                name: 'name' as const,
                message: 'Component name',
                type: 'input',
                source: ['first', 'name', 'n'],
            },
            {
                name: 'isStyled' as const,
                type: 'confirm',
                source: ['styled', 's'],
                message: 'Will component need styles?',
            },
            {
                name: 'hasProps' as const,
                type: 'confirm',
                source: ['props', 'o'],
                message: 'Will component need props?',
            },
        );

        const componentName = toolbox.strings.pascalCase(name);
        const props = { componentName };

        await toolbox.generateTemplate({
            template: 'component/index.ts',
            target: `${packageDir}/components/${componentName}/index.ts`,
            props,
        });

        if (isStyled) {
            await toolbox.generateTemplate({
                template: 'component/styles.ts',
                target: `${packageDir}/components/${componentName}/styles.ts`,
                props,
            });
        }

        if (hasProps) {
            await toolbox.generateTemplate({
                template: 'component/types.ts',
                target: `${packageDir}/components/${componentName}/types.ts`,
                props,
            });
        }

        const componentTemplate =
            isStyled && hasProps
                ? 'component/ComponentStyledProps.tsx'
                : isStyled
                  ? 'component/ComponentStyled.tsx'
                  : hasProps
                    ? 'component/ComponentProps.tsx'
                    : 'component/Component.tsx';

        await toolbox.generateTemplate({
            template: componentTemplate,
            target: `${packageDir}/components/${componentName}/${componentName}.tsx`,
            props,
        });
    },
};

export default command;
