import { GluegunCommand } from 'gluegun';
import * as path from 'path';

const command: GluegunCommand = {
    name: 'index',
    async run(toolbox: CliToolbox) {
        const base = toolbox.filesystem.dir(toolbox.baseDir);
        const index = await base.readAsync('.indexrc');

        const config = index
            .split('\n')
            .map((s) => s.trim())
            .filter(Boolean)
            .reduce((acc, line) => {
                const [key, ...props] = line.split(/[:,]/g);

                acc.push({
                    file: path.join(toolbox.baseDir, key),
                    props: props.map((p) => p.trim()).filter(Boolean),
                });

                return acc;
            }, []);

        await Promise.all(config.map((c) => indexFile(toolbox, c)));

        toolbox.print.success(`Built ${config.length} indexes`);
    },
};

async function indexFile(
    toolbox: CliToolbox,
    { file, props }: { file: string; props: string[] },
) {
    const { recreate, r } = toolbox.parameters.options;
    const exists = await toolbox.filesystem.existsAsync(file);
    let added = 0;
    toolbox.print.highlight(`Indexing ${toolbox.relativePath(file)}...`);
    if (toolbox.filesystem.isDirectory(file)) {
        file = path.join(file, 'index.ts');
    }

    const [contents, files] = await Promise.all([
        await toolbox.filesystem.readAsync(file),
        await toolbox.filesystem.listAsync(path.dirname(file)),
    ]);

    if (!exists) {
        toolbox.print.warning(
            `No contents found in ${toolbox.relativePath(file)}, skipping...`,
        );
        return;
    }

    const lines = recreate || r ? [] : (contents || '').split('\n');
    let lastImportIndex = 0;

    for (const [index, line] of Object.entries(lines).reverse()) {
        if (!/^export (.*) from '(.*)';$/.test(line)) {
            lastImportIndex = parseInt(index);
            break;
        }
    }

    files.forEach((f) => {
        const filename = path.basename(f).replace(/\.(ts|js|tsx|jsx)$/, '');
        const typed = props.includes('typed') ? ' type ' : ' ';
        const named = props.includes('named')
            ? ` as ${toolbox.strings.pascalCase(filename)} `
            : ' ';

        const exportLine = `export${typed}*${named}from './${filename}';`;

        if (!lines.includes(exportLine) && filename !== 'index') {
            toolbox.print.muted(`Adding [${exportLine}]`);
            lines.splice(lastImportIndex, 0, exportLine);
            added++;
        }
    });

    const newContents = lines.join('\n');
    await toolbox.filesystem.writeAsync(file, newContents);
    if (added > 0) {
        toolbox.print.success(
            `Added ${added} exports to ${toolbox.relativePath(file)}`,
        );
        toolbox.prettier(file);
    }
}

export default command;
