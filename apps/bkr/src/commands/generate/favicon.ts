import { GluegunCommand } from 'gluegun';

const command: GluegunCommand = {
    name: 'favicon',
    async run(toolbox: CliToolbox) {
        const publicDir = toolbox.appDirectory('bookr', 'public');
        const faviconDir = toolbox.filesystem.dir(publicDir).path('favicon');
        // const faviconDark = toolbox.filesystem.dir(faviconDir).path('favicon_mono_dark-1024.png');
        // const faviconLight = toolbox.filesystem.dir(faviconDir).path('favicon_mono_light-1024.png');
        const favicons = [
            toolbox.filesystem.dir(faviconDir).path('favicon-16.png'),
            toolbox.filesystem.dir(faviconDir).path('favicon-32.png'),
            toolbox.filesystem.dir(faviconDir).path('favicon-48.png'),
            toolbox.filesystem.dir(faviconDir).path('favicon-144.png'),
        ];

        await toolbox.runScript('convert', [
            ...favicons,
            toolbox.filesystem.dir(publicDir).path('favicon.ico'),
        ]);
    },
};

export default command;
