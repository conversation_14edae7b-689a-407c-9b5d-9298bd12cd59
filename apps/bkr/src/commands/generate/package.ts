import { GluegunCommand } from 'gluegun';

const command: GluegunCommand = {
    name: 'package',
    alias: ['pkg'],
    async run({
        template,
        parameters,
        getPackageJson,
        print,
        filesystem,
        packageDirectory,
        ensureDirectory,
    }: CliToolbox) {
        const { name: scope } = getPackageJson();
        const { first: name, options } = parameters;
        if (!name) {
            print.error('Missing package name');
            process.exit(1);
        }

        const packageDir = packageDirectory(name);
        const tsconfig = options.tsconfig ? options.tsconfig : 'base';
        const tsconfigExists = filesystem.exists(
            packageDirectory('tsconfig', `${tsconfig}.json`),
        );
        if (tsconfigExists !== 'file') {
            print.error(
                `Invalid typescript configuration, ${tsconfig} doesn't exist`,
            );
            process.exit(1);
            return;
        }

        print.colors.enable();
        print.info(`Generating package @${scope}/${name}...`);
        ensureDirectory(packageDir);
        const props = { name, scope, tsconfig };

        await template.generate({
            template: 'pkg/package.json',
            target: `${packageDir}/package.json`,
            props,
        });
        await template.generate({
            template: 'pkg/.eslintrc.js',
            target: `${packageDir}/.eslintrc.js`,
            props,
        });
        await template.generate({
            template: 'pkg/tsconfig.json',
            target: `${packageDir}/tsconfig.json`,
            props,
        });
        await template.generate({
            template: 'pkg/index.ts',
            target: `${packageDir}/index.ts`,
            props,
        });
    },
};

export default command;
