import { GluegunCommand } from 'gluegun';

const command: GluegunCommand = {
    name: 'jest',
    async run(toolbox: CliToolbox) {
        const { name, directory: packageDir } = await toolbox.packageFlag();
        toolbox.print.highlight(`Adding jest to ${name}`);

        const props = {
            displayName: name.split('/').pop() ?? name,
        };
        await toolbox.generateTemplate({
            template: 'jest/jest.config.ts',
            target: `${packageDir}/jest.config.ts`,
            props,
        });

        await toolbox.generateTemplate({
            template: 'jest/tsconfig.spec.json',
            target: `${packageDir}/tsconfig.spec.json`,
            props,
        });

        await toolbox.generateTemplate({
            template: 'jest/__tests__/index.spec.ts',
            target: `${packageDir}/__tests__/index.spec.ts`,
            props,
        });

        toolbox.print.highlight('Adding jest script to package.json');
        await toolbox.patching.update(
            `${packageDir}/package.json`,
            (contents) => {
                contents.scripts = {
                    ...contents.scripts,
                    test: 'jest',
                };

                return contents;
            },
        );
    },
};

export default command;
