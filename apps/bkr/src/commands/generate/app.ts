import { GluegunCommand } from 'gluegun';

const command: GluegunCommand = {
    name: 'app',
    alias: ['a'],
    async run(toolbox: CliToolbox) {
        const { name } = await toolbox.flag({
            name: 'name',
            source: ['first', 'name', 'n'],
            message: 'What is the name of the app?',
            type: 'input',
        });

        const props = { name };

        await toolbox.generateTemplate({
            template: 'app/package.json',
            target: toolbox.appDirectory(name, 'package.json'),
            props,
        });

        await toolbox.generateTemplate({
            template: 'app/.eslintrc.js',
            target: toolbox.appDirectory(name, '.eslintrc.js'),
            props,
        });

        await toolbox.generateTemplate({
            template: 'app/tsconfig.json',
            target: toolbox.appDirectory(name, 'tsconfig.json'),
            props,
        });
    },
};

export default command;
