import { GluegunCommand } from 'gluegun';

const command: GluegunCommand = {
    name: 'query',
    async run(toolbox: CliToolbox) {
        const pkg = toolbox.appDirectory('bookr', 'src/api');

        const { name, queryName } = await toolbox.flag(
            {
                name: 'name' as const,
                message: 'Query namespace',
                type: 'input',
                source: ['first', 'namespace', 'n'],
            },
            {
                name: 'queryName' as const,
                message: 'Query name',
                type: 'input',
                ask: false,
                source: ['second', 'queryName', 'q'],
            },
        );

        const namespace = toolbox.strings.camelCase(name);
        const props = {
            namespace,
            name,
            queryMethodName: toolbox.strings.camelCase(queryName || name),
            queryName: toolbox.strings.pascalCase(queryName || name),
        };

        toolbox.print.highlight(
            `Generating query ${props.queryMethodName}@use${props.queryName}Query for ${namespace}...`,
        );

        await toolbox.generateTemplate({
            props,
            template: 'api/query.ts',
            target: toolbox.filesystem
                .dir(pkg)
                .path('queries', `${namespace}.ts`),
        });

        await toolbox.generateTemplate({
            props,
            template: 'api/keys.ts',
            target: toolbox.filesystem.dir(pkg).path('keys', `${namespace}.ts`),
        });

        await toolbox.runtime.run('generate index');
    },
};

export default command;
