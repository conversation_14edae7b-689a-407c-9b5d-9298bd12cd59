import { GluegunCommand } from 'gluegun';

async function ensureFirebaseCertificate(toolbox: CliToolbox) {
    const certPath = toolbox.packageDirectory('firebase', 'firebase.cert.json');
    if (
        // eslint-disable-next-line turbo/no-undeclared-env-vars
        !process.env.SKIP_FIREBASE_CERT &&
        !toolbox.filesystem.exists(certPath)
    ) {
        const loader = toolbox.print.spin('Generating firebase certificate...');
        await toolbox.system.run(
            `gcloud secrets versions access latest --secret=firebase-cert --out-file=${certPath}`,
        );
        loader.stop();
        toolbox.print.success('Firebase certificate obtained');
    }
}

const command: GluegunCommand = {
    name: 'postinstall',
    async run(toolbox: CliToolbox) {
        // eslint-disable-next-line turbo/no-undeclared-env-vars
        if (process.env.SKIP_POSTINSTALL) {
            return;
        }

        toolbox.print.info('Post install script');
        await ensureFirebaseCertificate(toolbox);

        await toolbox.filesystem
            .existsAsync('/usr/local/bin/bkr')
            .then((state) => {
                if (!state) {
                    toolbox.print.info('Creating symlink to bkr');
                    toolbox.runScript('bash', [
                        '-c',
                        'sudo  ln -s "$(yarn bin bkr)" /usr/local/bin/bkr',
                    ]);
                }
            });

        await toolbox.runScript('yarn', ['bkr', 'api', 'gen']);
    },
};

export default command;
