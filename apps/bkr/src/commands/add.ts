import { GluegunCommand } from 'gluegun';

const command: GluegunCommand = {
    name: 'add',
    alias: ['a'],
    async run(toolbox: CliToolbox) {
        const { name, directory: packageDir } = await toolbox.packageFlag();
        const isDev = !!(
            toolbox.parameters.options.dev || toolbox.parameters.options.dev
        );
        const packages = toolbox.parameters.array;
        const deps = packages.join(' ');

        toolbox.print.highlight(
            `Adding [${deps}] to [${name}] ${
                isDev ? 'devDependencies' : 'dependencies'
            }...`,
        );
        await toolbox.runScript(
            'yarn',
            ['add', isDev ? '--dev' : '', ...packages].filter(Boolean),
            {
                cwd: packageDir,
            },
        );

        toolbox.print.success(
            `Added to ${toolbox.relativePath(
                toolbox.packageDirectory(name, 'package.json'),
            )}`,
        );
    },
};

export default command;
