import { GluegunCommand } from 'gluegun';
import * as path from 'path';

const command: GluegunCommand = {
    name: 'addScript',
    async run(toolbox: CliToolbox) {
        const packages = toolbox.filesystem
            .subdirectories(toolbox.packageDirectory())
            .filter((pkg) => pkg !== 'bkr');
        const [scriptName, ...script] = toolbox.parameters.array;
        const scripts = {
            [scriptName]: script.join(' '),
        };

        const spinner = toolbox.print.spin(
            `Adding ${JSON.stringify(scripts)} script to:`,
        );

        await Promise.all(
            packages.map(async (dir) => {
                if (
                    !toolbox.filesystem.exists(path.join(dir, 'tsconfig.json'))
                ) {
                    return;
                }

                try {
                    const pkgPath = path.join(dir, 'package.json');
                    const contents = toolbox.filesystem.read(pkgPath, 'json');

                    contents.scripts = {
                        ...contents?.scripts,
                        ...scripts,
                    };

                    toolbox.filesystem.write(pkgPath, contents);
                    toolbox.prettier(pkgPath);
                    spinner.succeed(toolbox.relativePath(pkgPath));
                } catch (e) {
                    spinner.fail(toolbox.relativePath(dir));
                }
            }),
        );

        spinner.stop();
    },
};

export default command;
