import { GluegunCommand } from 'gluegun';

const command: GluegunCommand = {
    name: 'countries',
    async run(toolbox: CliToolbox) {
        toolbox.print.highlight('Generating REST API...');
        const file = toolbox.filesystem
            .dir(toolbox.packageDirectory('ui'))
            .dir('assets');

        try {
            const res = await fetch('https://restcountries.com/v3.1/all');
            const data = await res.json();

            const mappedData = data
                .filter(({ independent }) => independent)
                .map(({ name, flags, cca2, ccn3, cca3, idd }) => {
                    const callingCodes = (idd.suffixes ?? []).map(
                        (suffix) => `${idd.root}${suffix}`,
                    );

                    return {
                        name: name.common,
                        flag: flags.svg,
                        callingCodes,
                        cca2,
                        ccn3,
                        cca3,
                    };
                })
                .sort((a, b) => a.name.localeCompare(b.name))
                .map((country, id) => ({ id: id + 1, ...country }));

            file.write('countries.json', JSON.stringify(mappedData));
            toolbox.print.success(
                `Countries fetched to './${toolbox.relativePath(
                    file.path('countries.json'),
                )}'!`,
            );
        } catch (e) {
            toolbox.fatal(`Failed to fetch countries!\n${e}`);
        }
    },
};

export default command;
