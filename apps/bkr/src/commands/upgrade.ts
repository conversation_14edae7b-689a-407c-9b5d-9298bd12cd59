import { GluegunCommand } from 'gluegun';

const command: GluegunCommand = {
    name: 'upgrade',
    async run(toolbox: CliToolbox) {
        const packages = toolbox.getPackages();

        await toolbox.runScript('npx', ['npm-check-updates', '-i'], {
            cwd: toolbox.baseDir,
        });
        await toolbox.runScript(
            'npx',
            [
                'npm-check-updates',
                ...packages.map(({ dirname }) => ['-w', dirname]).flat(),
                '-i',
            ],
            {
                cwd: toolbox.baseDir,
            },
        );
    },
};

export default command;
