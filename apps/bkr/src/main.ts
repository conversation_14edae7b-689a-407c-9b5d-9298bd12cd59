import { build } from 'gluegun';

async function run(argv) {
    const result: CliToolbox = (await build()
        .brand('bkr')
        .src(__dirname)
        .plugins('./node_modules', { matching: 'bkr-*', hidden: true })
        .help() // provides default for help, h, --help, -h
        .version() // provides default for version, v, --version, -v
        .create()
        .run(argv)) as any;

    await result.prettify();

    return result;
}

module.exports = { run };
