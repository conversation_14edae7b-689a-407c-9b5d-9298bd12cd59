{"compilerOptions": {"allowSyntheticDefaultImports": false, "experimentalDecorators": true, "lib": ["es2015", "scripthost", "es2015.promise", "es2015.generator", "es2015.iterable", "dom"], "module": "commonjs", "moduleResolution": "node", "noImplicitAny": false, "noImplicitThis": true, "noUnusedLocals": true, "sourceMap": false, "inlineSourceMap": true, "outDir": "build", "strict": false, "target": "es5", "declaration": true, "declarationDir": "build/types"}, "files": ["./src/types.ts"], "include": ["src/**/*"], "exclude": ["node_modules", "./src/templates/**/*"]}