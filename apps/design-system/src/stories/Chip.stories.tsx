import Chip from '@mui/material/Chip';
import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { ComponentMeta } from '@storybook/react';
import React from 'react';
import { ThemeProvider } from '@bookr-technologies/ui';

export default {
    title: 'Mui/Chip',
    component: Chip,
} as ComponentMeta<typeof Chip>;

export const Default = () => (
    <ThemeProvider>
        <Stack gap={3}>
            <Stack>
                <Typography>Filled</Typography>
                <Grid container gap={1} alignItems={'center'}>
                    <Chip variant={'filled'} label="Chip default" />
                    <Chip
                        variant={'filled'}
                        label="Chip primary"
                        color={'primary'}
                    />
                    <Chip
                        variant={'filled'}
                        label="Chip secondary"
                        color={'secondary'}
                    />
                    <Chip variant={'filled'} label="Chip info" color={'info'} />
                    <Chip
                        variant={'filled'}
                        label="Chip success"
                        color={'success'}
                    />
                    <Chip
                        variant={'filled'}
                        label="Chip warning"
                        color={'warning'}
                    />
                    <Chip
                        variant={'filled'}
                        label="Chip error"
                        color={'error'}
                    />
                </Grid>
            </Stack>

            <Stack>
                <Typography>Subtle</Typography>
                <Grid container gap={1} alignItems={'center'}>
                    <Chip variant={'subtle'} label="Chip default" />
                    <Chip
                        variant={'subtle'}
                        label="Chip primary"
                        color={'primary'}
                    />
                    <Chip
                        variant={'subtle'}
                        label="Chip secondary"
                        color={'secondary'}
                    />
                    <Chip variant={'subtle'} label="Chip info" color={'info'} />
                    <Chip
                        variant={'subtle'}
                        label="Chip success"
                        color={'success'}
                    />
                    <Chip
                        variant={'subtle'}
                        label="Chip warning"
                        color={'warning'}
                    />
                    <Chip
                        variant={'subtle'}
                        label="Chip error"
                        color={'error'}
                    />
                </Grid>
            </Stack>

            <Stack>
                <Typography>Outlined</Typography>
                <Grid container gap={1} alignItems={'center'}>
                    <Chip variant={'outlined'} label="Chip default" />
                    <Chip
                        variant={'outlined'}
                        label="Chip primary"
                        color={'primary'}
                    />
                    <Chip
                        variant={'outlined'}
                        label="Chip secondary"
                        color={'secondary'}
                    />
                    <Chip
                        variant={'outlined'}
                        label="Chip info"
                        color={'info'}
                    />
                    <Chip
                        variant={'outlined'}
                        label="Chip success"
                        color={'success'}
                    />
                    <Chip
                        variant={'outlined'}
                        label="Chip warning"
                        color={'warning'}
                    />
                    <Chip
                        variant={'outlined'}
                        label="Chip error"
                        color={'error'}
                    />
                </Grid>
            </Stack>
        </Stack>
    </ThemeProvider>
);
