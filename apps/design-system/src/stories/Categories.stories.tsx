import Chip from '@mui/material/Chip';
import Grid from '@mui/material/Grid';
import Paper from '@mui/material/Paper';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { ComponentMeta } from '@storybook/react';
import { categoryComponents } from 'bookr/src/components/CategoryIcon';
import React from 'react';
import { ThemeProvider } from '@bookr-technologies/ui';

export default {
    title: 'Categories',
    component: Chip,
} as ComponentMeta<typeof Chip>;

export const Default = () => (
    <ThemeProvider>
        <Stack gap={3}>
            <Stack>
                <Typography variant={'title1'} fontWeight={800} mb={2}>
                    Icons
                </Typography>
                <Grid container spacing={3} alignItems={'center'}>
                    {Object.entries(categoryComponents).map(([key, Icon]) => (
                        <Grid item xs={6} key={key}>
                            <Paper
                                component={Grid}
                                container
                                direction={'row'}
                                alignItems={'center'}
                                justifyContent={'flex-start'}
                                gap={2}
                            >
                                <Icon height={64} width={64} />
                                <Typography
                                    variant={'caption1'}
                                    fontWeight={700}
                                >
                                    {key}
                                </Typography>
                            </Paper>
                        </Grid>
                    ))}
                </Grid>
            </Stack>
        </Stack>
    </ThemeProvider>
);
