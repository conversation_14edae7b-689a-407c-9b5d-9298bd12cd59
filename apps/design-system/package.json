{"name": "design-system", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@babel/core": "^7.21.8", "@storybook/addon-actions": "^7.0.12", "@storybook/addon-essentials": "^7.0.12", "@storybook/addon-interactions": "^7.0.12", "@storybook/addon-links": "^7.0.12", "@storybook/react": "^7.0.12", "@storybook/react-vite": "^7.0.12", "@storybook/testing-library": "^0.2.0", "@types/react": "^18.2.6", "@types/react-dom": "^18.2.4", "@vitejs/plugin-react-swc": "^3.3.1", "babel-loader": "^9.1.2", "storybook": "^7.0.12", "typescript": "^5.0.4", "vite": "^4.3.7"}}