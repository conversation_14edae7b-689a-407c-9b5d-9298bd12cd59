version: '3.7'

volumes:
  redis_data:
    driver: local

services:
  redis:
    image: redis:alpine
    ports:
      - '6379:6379'
    volumes:
      - redis_data:/data

  bookr:
    build:
      context: .
      dockerfile: ./apps/bookr/Dockerfile
    extra_hosts:
      - 'host.docker.internal:host-gateway'
    environment:
      SKIP_FIREBASE_CERT: 'true'
      SKIP_POSTINSTALL: 'true'
      REDIS_HOST: redis
    ports:
      - '3000:3000'
    volumes:
      - ./secrets:/usr/src/app/secrets
      - ./apps:/usr/src/app/apps
      - ./packages:/usr/src/app/packages
      - ./package.json:/usr/src/app/package.json
      - ./turbo.json:/usr/src/app/turbo.json
    depends_on:
      - redis
